<?php

namespace app\middleware;

use app\model\UserModel;
use base\exception\BaseException;
use Firebase\JWT\JWT;
use Firebase\JWT\Key;
use Webman\Http\Request;
use Webman\Http\Response;
use Webman\MiddlewareInterface;
use support\Container;

/**
 * User authentication middleware
 */
class UserAuth implements MiddlewareInterface
{
    /**
     * Process request
     *
     * @param Request $request
     * @param callable $handler
     * @return Response
     * @throws BaseException
     */
    public function process(Request $request, callable $handler): Response
    {
        // Get JWT token from authorization header
        $authorization = $request->header('authorization');
        if (empty($authorization)) {
            throw new BaseException('需要授权认证', 401);
        }

        // Extract token (remove Bearer prefix)
        $token = $authorization;
        if (str_starts_with($authorization, 'Bearer ')) {
            $token = substr($authorization, 7);
        }

        try {
            // Decode JWT token
            $decoded = JWT::decode($token, new Key(config('app.jwt_secret'), 'HS256'));
            
            // Get user ID from token
            $userId = $decoded->sub;
            
            // Get user from database
            $user = UserModel::find($userId);
            if (empty($user) || $user->status !== 1) {
                throw new BaseException('用户不存在或已被禁用', 401);
            }
            
            // Set user in request
            $request->user = $user;
            
            // Store user in container
            Container::set('user', $user);
            
        } catch (\Exception $e) {
            throw new BaseException('无效或过期的认证令牌', 401);
        }

        return $handler($request);
    }
} 