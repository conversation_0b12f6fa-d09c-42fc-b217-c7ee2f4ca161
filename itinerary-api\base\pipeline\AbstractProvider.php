<?php

declare(strict_types=1);

namespace base\pipeline;

use support\Container;
use base\base\DS;
use base\exception\BaseException;
use base\pipeline\contract\PluginInterface;
use base\pipeline\contract\ShortcutInterface;


abstract class AbstractProvider
{
    abstract public function __call($shortcut, $params);

    abstract public function mergeCommonPlugins(array $plugins): array;

    /**
     *
     * @param string $plugin
     * @param array $params
     *
     * @return DS
     * @throws BaseException
     */
    public function call(string $plugin, array $params = []): DS
    {
        if (!class_exists($plugin) || ! in_array(ShortcutInterface::class, class_implements($plugin), true)) {
            throw new BaseException( "[$plugin] is not incompatible");
        }

        /* @var ShortcutInterface $money */
        $money = Container::get($plugin);

        return $this->run(
            $this->mergeCommonPlugins($money->getPlugins($params)), $params
        );
    }

    /**
     * 执行
     *
     * @param array $plugins
     * @param array $params
     *
     * @return DS
     * @throws BaseException
     */
    public function run(array $plugins, array $params): DS
    {
        $this->verifyPlugin($plugins);

        $pipeline = new Pipeline(Container::instance());

        /* @var Payload $structure */
        $structure = $pipeline
            ->send((new Payload())->setParams($params)->setResponse([]))
            ->through($plugins)
            ->via('assembly')
            ->then(function ($structure) {
                return $structure;
            });

        return $structure->getResponse();
    }


    /**
     * @param array $plugins
     *
     * @throws BaseException
     */
    protected function verifyPlugin(array $plugins): void
    {
        foreach ($plugins as $plugin) {
            if (is_callable($plugin)) {
                continue;
            }

            if ((is_object($plugin) ||
                    (is_string($plugin) && class_exists($plugin))) &&
                in_array(PluginInterface::class, class_implements($plugin), true)) {
                continue;
            }

            throw new BaseException("[$plugin] is not incompatible");
        }
    }
}
