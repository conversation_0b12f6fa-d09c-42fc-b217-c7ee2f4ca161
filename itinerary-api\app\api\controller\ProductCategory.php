<?php

namespace app\api\controller;

use app\model\ProductCategoryModel;
use support\Request;
use support\Response;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;

class ProductCategory
{
    /**
     * 获取分类列表
     * @param Request $request
     * @return Response
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function list(Request $request): Response
    {
        $accessStatus = $request->get('accessStatus');
        $categoryDiscard = $request->get('categoryDiscard');
        $parentId = $request->get('parentId');
        
        $where = [];
        if ($accessStatus) {
            $where['access_status'] = $accessStatus;
        }
        if ($categoryDiscard) {
            $where['category_discard'] = $categoryDiscard;
        }
        if ($parentId) {
            $where['parent_id'] = $parentId;
        }

        $list = ProductCategoryModel::mk()
            ->where($where)
            ->order('sort_order', 'asc')
            ->select()
            ->toArray();
        $total = count($list);
        return success(['list' => $list, 'total' => $total]);
    }
    
    /**
     * 获取分类树
     * @param Request $request
     * @return Response
     */
    public function tree(Request $request): Response
    {
        $parentId = $request->input('parentId', 0);
        $includeDiscard = $request->input('includeDiscard', false);
        
        $model = new ProductCategoryModel();
        $tree = $model->getCategoryTree($parentId, $includeDiscard);
        
        return success($tree);
    }
    
    /**
     * 获取分类详情
     * @param Request $request
     * @return Response
     */
    public function detail(Request $request ,$id): Response
    {
        if (!$id) {
            return error(400,'参数错误');
        }

        $detail = ProductCategoryModel::mk()
            ->where('category_id', $id)

            ->find();

        if (!$detail) {
            return error(404, '分类不存在');
        }
        
        return success($detail->toArray());
    }
    
    /**
     * 创建分类
     * @param Request $request
     * @return Response
     */
    public function create(Request $request): Response
    {
        $data = $request->post();
        
        // 转换字段为下划线格式
        $data = $this->convertFieldsToDB($data);
        
        // 验证数据
        if (empty($data['category_name']) || !isset($data['parent_id'])) {
            return error(400, '参数不完整');
        }
        
        // 验证父级分类是否存在
        if ($data['parent_id'] > 0) {
            $model = new ProductCategoryModel();
            $parent = $model->getDetail($data['parent_id']);
            if (!$parent) {
                return error(400, '父级分类不存在');
            }
            
            // 设置分类层级
            $data['category_level'] = $parent['category_level'] + 1;
        } else {
            $data['category_level'] = 1;
        }
        
        // 添加时间戳
        $data['create_time'] = $data['update_time'] = time();
        
        // 保存分类
        $model = new ProductCategoryModel();
        $id = $model->insertGetId($data);
        
        if (!$id) {
            return error(500, '创建失败');
        }
        
        return success(['category_id' => $id]);
    }
    
    /**
     * 更新分类
     * @param Request $request
     * @return Response
     */
    public function update(Request $request): Response
    {
        $categoryId = $request->input('categoryId');
        if (!$categoryId) {
            return error(400, '参数不完整');
        }
        
        $data = $request->post();
        unset($data['categoryId']);
        
        // 转换字段为下划线格式
        $data = $this->convertFieldsToDB($data);
        
        // 验证分类是否存在
        $model = new ProductCategoryModel();
        $detail = $model->getDetail($categoryId);
        if (!$detail) {
            return error(400, '分类不存在');
        }
        
        // 防止更改父级为自己或自己的子分类
        if (isset($data['parent_id']) && $data['parent_id'] != $detail['parent_id']) {
            if ($data['parent_id'] == $categoryId) {
                return error(400, '父级分类不能为自己');
            }
            
            // 验证父级不能是自己的子分类
            $childIds = $this->getAllChildIds($categoryId);
            if (in_array($data['parent_id'], $childIds)) {
                return error(400, '父级分类不能是自己的子分类');
            }
            
            // 验证父级分类是否存在
            $parent = $model->getDetail($data['parent_id']);
            if (!$parent) {
                return error(400, '父级分类不存在');
            }
            
            // 更新分类层级
            $data['category_level'] = $parent['category_level'] + 1;
            
            // 更新所有子分类的层级
            $this->updateChildLevel($categoryId, $data['category_level']);
        }
        
        // 添加更新时间
        $data['update_time'] = time();
        
        // 更新分类
        $model->where('category_id', $categoryId)->update($data);
        
        return success();
    }
    
    /**
     * 删除分类
     * @param Request $request
     * @return Response
     */
    public function delete(Request $request): Response
    {
        $categoryId = $request->input('categoryId');
        
        if (!$categoryId) {
            return error(400, '参数错误');
        }
        
        // 验证分类是否存在
        $model = new ProductCategoryModel();
        $detail = $model->getDetail($categoryId);
        if (!$detail) {
            return error(400, '分类不存在');
        }
        
        // 检查是否有子分类
        $children = $model->where('parent_id', $categoryId)->count();
        if ($children > 0) {
            return error(400, '该分类下有子分类，无法删除');
        }
        
        // 删除分类
        $model->where('category_id', $categoryId)->delete();
        
        return success();
    }
    
    /**
     * 获取所有子分类ID
     * @param int $categoryId 分类ID
     * @return array
     */
    private function getAllChildIds(int $categoryId): array
    {
        $model = new ProductCategoryModel();
        $children = $model->where('parent_id', $categoryId)->column('category_id');
        
        $result = $children;
        foreach ($children as $childId) {
            $childrenIds = $this->getAllChildIds($childId);
            $result = array_merge($result, $childrenIds);
        }
        
        return $result;
    }

    /**
     * 更新所有子分类的层级
     * @param int $categoryId 分类ID
     * @param int $parentLevel 父级层级
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    private function updateChildLevel(int $categoryId, int $parentLevel): void
    {
        $model = new ProductCategoryModel();
        $children = $model->where('parent_id', $categoryId)->select();
        
        foreach ($children as $child) {
            $newLevel = $parentLevel + 1;
            $model->where('category_id', $child['category_id'])->update([
                'category_level' => $newLevel,
                'update_time' => time()
            ]);
            
            // 递归更新子分类
            $this->updateChildLevel($child['category_id'], $newLevel);
        }
    }
    
    /**
     * 将小驼峰转换为下划线格式
     * @param array $data
     * @return array
     */
    private function convertFieldsToDB(array $data): array
    {
        $result = [];
        $map = [
            'categoryId' => 'category_id',
            'parentId' => 'parent_id',
            'categoryName' => 'category_name',
            'categoryLevel' => 'category_level',
            'accessStatus' => 'access_status',
            'categoryDiscard' => 'category_discard',
            'allowAccessScope' => 'allow_access_scope',
            'noAccessScope' => 'no_access_scope',
            'detailUrl' => 'detail_url',
            'priceLimit' => 'price_limit',
            'sortOrder' => 'sort_order',
            'createTime' => 'create_time',
            'updateTime' => 'update_time',
            'deleteTime' => 'delete_time'
        ];
        
        foreach ($data as $key => $value) {
            if (isset($map[$key])) {
                $result[$map[$key]] = $value;
            } else {
                $result[$key] = $value;
            }
        }
        
        return $result;
    }

    public function status(Request $request, $id): Response
    {
        $status = $request->input('status');
        if (!$id) {
            return error(400, '参数错误');
        }

        // 验证分类是否存在
        $model = new ProductCategoryModel();
        $detail = $model->where('category_id', $id)->find();
        if (!$detail) {
            return error(400, '分类不存在');
        }

        $detail->save([
            'access_status' => $status,
        ]);

        return success();
    }
} 