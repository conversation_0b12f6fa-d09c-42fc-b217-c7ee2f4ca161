<?php

declare(strict_types=1);

namespace app\model;

use base\base\BaseModel;

class ReservationModel extends BaseModel
{
    /**
     * 设置表名
     * @var string
     */
    protected $name = 'reservations';

    /**
     * 设置主键
     * @var string
     */
    protected $pk = 'id';

    /**
     * 预约状态：待确认
     */
    const STATUS_PENDING = 'pending';

    /**
     * 预约状态：已确认
     */
    const STATUS_CONFIRMED = 'confirmed';

    /**
     * 预约状态：已拒绝
     */
    const STATUS_REFUSED = 'refused';

    /**
     * 预约状态：已取消
     */
    const STATUS_CANCELLED = 'cancelled';

    /**
     * 预约状态：已完成
     */
    const STATUS_COMPLETED = 'completed';

    /**
     * 允许写入的字段
     * @var array
     */
    protected $field = [
        'id',
        'order_id',
        'user_id',
        'name',
        'phone',
        'travel_date',
        'adult_count',
        'child_count',
        'surcharge_required',
        'surcharge_amount',
        'surcharge_paid',
        'occupancies',
        'status',
        'create_time',
        'update_time',
        'delete_time'
    ];

    /**
     * 类型转换
     * @var array
     */
    protected $type = [
        'id' => 'integer',
        'adult_count' => 'integer',
        'child_count' => 'integer',
        'surcharge_required' => 'boolean',
        'surcharge_amount' => 'float',
        'surcharge_paid' => 'boolean',
        'travel_date' => 'date',
        'create_time' => 'integer',
        'update_time' => 'integer',
        'delete_time' => 'integer',
        'occupancies' => 'json'
    ];

    /**
     * 创建预约
     * @param array $data 预约数据
     * @return string 预约ID
     */
    public function createReservation(array $data): string
    {
        $data['status'] = self::STATUS_PENDING;
        $data['create_time'] = $data['update_time'] = time();

        $this->save($data);
        return (string)$this->id;
    }

    /**
     * 更新预约状态
     * @param string $id 预约ID
     * @param string $status 状态
     * @return bool
     */
    public function updateStatus(string $id, string $status, string $note = ''): bool
    {
        return $this->where('id', $id)->where('delete_time', 0)->update([
            'status' => $status,
            'update_time' => time(),
            'note' => $note
        ]) > 0;
    }

    /**
     * 标记加价已支付
     * @param string $id 预约ID
     * @return bool
     */
    public function markSurchargePaid(string $id): bool
    {
        return $this->where('id', $id)->where('delete_time', 0)->update([
            'surcharge_paid' => true,
            'update_time' => time()
        ]) > 0;
    }

    /**
     * 获取预约详情
     * @param string $id 预约ID
     * @return array|null
     */
    public function getDetail(string $id): ?array
    {
        $reservation = $this->where('id', $id)->where('delete_time', 0)->find();

        if (!$reservation) {
            return null;
        }

        return $reservation->toArray();
    }

    /**
     * 通过订单ID获取预约
     * @param string $orderId 订单ID
     * @return array|null
     */
    public function getByOrderId(string $orderId): ?array
    {
        $reservation = $this->where('order_id', $orderId)
            ->where('delete_time', 0)
            ->order('id', 'desc')
            ->find();

        if (!$reservation) {
            return null;
        }

        return $reservation->toArray();
    }

    /**
     * 取消预约
     * @param string $id 预约ID
     * @return bool
     */
    public function cancelReservation(string $id): bool
    {
        return $this->updateStatus($id, self::STATUS_CANCELLED);
    }
}
