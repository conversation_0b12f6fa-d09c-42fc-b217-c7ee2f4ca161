# Auth 鉴权注解使用文档

## 简介

本文档介绍如何在项目中使用 `Auth` 注解进行接口鉴权，以及如何获取和使用当前登录用户信息。

## Auth 注解使用

### 基本用法

在控制器方法上添加 `Auth` 注解来保护接口访问：

```php
use base\auth\Auth;

class YourController
{
    #[Auth('permission:module:action')]
    public function yourMethod(Request $request)
    {
        // 只有拥有 'permission:module:action' 权限的用户才能访问此方法
    }
}
```

### 权限标识格式

权限标识通常采用 `业务:模块:操作` 的三段式命名：

- **业务**：代表业务线，如 `booking`(预订)、`admin`(管理)等
- **模块**：代表功能模块，如 `hotel`(酒店)、`order`(订单)等
- **操作**：代表具体操作，如 `list`(列表)、`save`(保存)、`update`(更新)、`delete`(删除)等

例如：
- `booking:hotel:list` - 酒店列表查看权限
- `booking:hotel:save` - 酒店新增权限
- `booking:hotel:update` - 酒店更新权限
- `booking:hotel:delete` - 酒店删除权限

### 多权限控制

一个方法可以要求用户同时拥有多个权限：

```php
#[Auth(['booking:hotel:update'])]
#[Auth(['booking:room:update'])]
public function updateBoth(Request $request)
{
    // 用户必须同时拥有酒店更新权限和房间更新权限
}
```

### 权限注解示例

以下是在 Hotel 控制器中的实际使用示例：

```php
<?php

namespace app\api\controller;

use base\auth\Auth;
use support\Request;

class Hotel
{
    // 查看所有酒店
    public function list(Request $request)
    {
        // 不需要权限即可访问
    }
    
    // 查看酒店详情
    public function detail(Request $request)
    {
        // 不需要权限即可访问
    }
    
    // 新增酒店
    #[Auth('booking:hotel:save')]
    public function save(Request $request)
    {
        // 需要 booking:hotel:save 权限
    }
    
    // 更新酒店
    #[Auth('booking:hotel:update')]
    public function update(Request $request)
    {
        // 需要 booking:hotel:update 权限
    }
    
    // 删除酒店
    #[Auth('booking:hotel:delete')]
    public function delete(Request $request)
    {
        // 需要 booking:hotel:delete 权限
    }
}
```

## 用户信息获取

### Request 中的用户信息

使用 `Auth` 注解进行鉴权后，当前登录用户的信息会被自动注入到 `base\auth\UserInfo::class` 对象中, `userinfo()` 可快速获取实例：

```php
#[Auth('booking:hotel:update')]
public function update(Request $request)
{
    // 获取当前登录用户信息
    /** @var \base\auth\UserInfo $userInfo */
    $userInfo = userinfo();
    
    // 使用用户信息
    $userId = $userInfo['userId'];
    $username = $userInfo->username();
    $realName = $userInfo->get('realName');
    
    // 业务逻辑...
}
```


## 最佳实践

### 权限命名规范

1. 始终使用三段式命名：`业务:模块:操作`
2. 权限名称使用小写字母和冒号，避免使用空格和特殊字符
3. 针对同一资源的不同操作，保持权限名称的一致性

### 权限粒度

1. 为增删改查等基本操作分配独立权限
2. 敏感操作应使用专门的权限标识
3. 批量操作应与单条操作使用相同的权限标识，保持一致性

### 权限检查位置

1. 将 `Auth` 注解放在控制器方法上，不要放在服务层或模型层
2. 复杂权限逻辑（如根据资源所有权判断）应在方法内部手动实现

### 用户信息使用

1. 不要修改 `userinfo()` 中的内容
2. 在记录日志时务必传入完整的用户信息
3. 敏感操作时应验证用户身份信息

## 故障排查

如果遇到权限问题，请检查：

1. 是否正确添加了 `Auth` 注解
2. 权限标识是否与系统中定义的权限一致
3. 当前用户是否拥有所需权限
4. 系统日志中是否有鉴权相关的错误信息
