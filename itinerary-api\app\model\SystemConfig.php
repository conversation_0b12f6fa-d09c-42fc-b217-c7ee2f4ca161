<?php

namespace app\model;

use base\base\BaseModel;

/**
 * 系统配置模型
 * 对应数据库表：system_configs
 */
class SystemConfig extends BaseModel
{
    /**
     * 与模型关联的表名
     *
     * @var string
     */
    protected $table = 'system_configs';

    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'config_key',
        'config_name',
        'config_value',
        'config_type',
        'config_group',
        'group_id',
        'config_description',
        'default_value',
        'validation_rules',
        'options',
        'is_encrypted',
        'is_public',
        'is_editable',
        'sort_order',
        'created_by',
        'updated_by',
        'created_at',
        'updated_at'
    ];

    /**
     * 属性转换
     *
     * @var array
     */
    protected $casts = [
        'config_value' => 'string',
        'validation_rules' => 'array',
        'options' => 'array',
        'is_encrypted' => 'boolean',
        'is_public' => 'boolean',
        'is_editable' => 'boolean',
        'sort_order' => 'integer',
        'created_by' => 'integer',
        'updated_by' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    /**
     * 配置类型常量
     */
    const TYPE_STRING = 'string';
    const TYPE_INTEGER = 'integer';
    const TYPE_FLOAT = 'float';
    const TYPE_BOOLEAN = 'boolean';
    const TYPE_JSON = 'json';
    const TYPE_ARRAY = 'array';
    const TYPE_EMAIL = 'email';
    const TYPE_URL = 'url';
    const TYPE_PASSWORD = 'password';
    const TYPE_TEXT = 'text';
    const TYPE_SELECT = 'select';
    const TYPE_MULTISELECT = 'multiselect';
    const TYPE_RADIO = 'radio';
    const TYPE_CHECKBOX = 'checkbox';
    const TYPE_DATE = 'date';
    const TYPE_DATETIME = 'datetime';
    const TYPE_TIME = 'time';
    const TYPE_FILE = 'file';
    const TYPE_IMAGE = 'image';

    /**
     * 配置分组常量
     */
    const GROUP_SYSTEM = 'system';
    const GROUP_DATABASE = 'database';
    const GROUP_CACHE = 'cache';
    const GROUP_EMAIL = 'email';
    const GROUP_SMS = 'sms';
    const GROUP_PAYMENT = 'payment';
    const GROUP_BOOKING = 'booking';
    const GROUP_HOTEL = 'hotel';
    const GROUP_INVENTORY = 'inventory';
    const GROUP_PRICING = 'pricing';
    const GROUP_OTA = 'ota';
    const GROUP_SUPPLIER = 'supplier';
    const GROUP_NOTIFICATION = 'notification';
    const GROUP_SECURITY = 'security';
    const GROUP_API = 'api';
    const GROUP_LOGGING = 'logging';
    const GROUP_MONITORING = 'monitoring';
    const GROUP_BACKUP = 'backup';
    const GROUP_MAINTENANCE = 'maintenance';

    /**
     * 获取创建者
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * 获取更新者
     */
    public function updater()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    /**
     * 作用域：按分组筛选
     */
    public function scopeByGroup($query, $group)
    {
        return $query->where('config_group', $group);
    }

    /**
     * 作用域：按类型筛选
     */
    public function scopeByType($query, $type)
    {
        return $query->where('config_type', $type);
    }

    /**
     * 作用域：公开的配置
     */
    public function scopePublic($query)
    {
        return $query->where('is_public', 1);
    }

    /**
     * 作用域：可编辑的配置
     */
    public function scopeEditable($query)
    {
        return $query->where('is_editable', true);
    }

    /**
     * 作用域：按排序
     */
    public function scopeOrdered($query)
    {
        return $query->order(['config_group', 'sort_order', 'config_key']);
    }

    /**
     * 获取配置类型名称
     */
    public function getConfigTypeNameAttribute()
    {
        $types = [
            self::TYPE_STRING => '字符串',
            self::TYPE_INTEGER => '整数',
            self::TYPE_FLOAT => '浮点数',
            self::TYPE_BOOLEAN => '布尔值',
            self::TYPE_JSON => 'JSON',
            self::TYPE_ARRAY => '数组',
            self::TYPE_EMAIL => '邮箱',
            self::TYPE_URL => 'URL',
            self::TYPE_PASSWORD => '密码',
            self::TYPE_TEXT => '文本',
            self::TYPE_SELECT => '下拉选择',
            self::TYPE_MULTISELECT => '多选',
            self::TYPE_RADIO => '单选',
            self::TYPE_CHECKBOX => '复选框',
            self::TYPE_DATE => '日期',
            self::TYPE_DATETIME => '日期时间',
            self::TYPE_TIME => '时间',
            self::TYPE_FILE => '文件',
            self::TYPE_IMAGE => '图片'
        ];

        return $types[$this->config_type] ?? $this->config_type;
    }

    /**
     * 获取配置分组名称
     */
    public function getConfigGroupNameAttribute()
    {
        $groups = [
            self::GROUP_SYSTEM => '系统设置',
            self::GROUP_DATABASE => '数据库设置',
            self::GROUP_CACHE => '缓存设置',
            self::GROUP_EMAIL => '邮件设置',
            self::GROUP_SMS => '短信设置',
            self::GROUP_PAYMENT => '支付设置',
            self::GROUP_BOOKING => '预订设置',
            self::GROUP_HOTEL => '酒店设置',
            self::GROUP_INVENTORY => '库存设置',
            self::GROUP_PRICING => '价格设置',
            self::GROUP_OTA => 'OTA设置',
            self::GROUP_SUPPLIER => '供应商设置',
            self::GROUP_NOTIFICATION => '通知设置',
            self::GROUP_SECURITY => '安全设置',
            self::GROUP_API => 'API设置',
            self::GROUP_LOGGING => '日志设置',
            self::GROUP_MONITORING => '监控设置',
            self::GROUP_BACKUP => '备份设置',
            self::GROUP_MAINTENANCE => '维护设置'
        ];

        return $groups[$this->config_group] ?? $this->config_group;
    }

    /**
     * 获取格式化的配置值
     */
    public function getFormattedValueAttribute()
    {
        $value = $this->config_value;

        if ($this->is_encrypted && $value) {
            return '***加密内容***';
        }

        switch ($this->config_type) {
            case self::TYPE_BOOLEAN:
                return $value ? '是' : '否';
            case self::TYPE_JSON:
            case self::TYPE_ARRAY:
                return is_string($value) ? $value : json_encode($value, JSON_UNESCAPED_UNICODE);
            case self::TYPE_PASSWORD:
                return $value ? '***已设置***' : '未设置';
            default:
                return $value;
        }
    }

    /**
     * 获取实际配置值（解密后）
     */
    public function getRealValue()
    {
        $value = $this->config_value;

        if ($this->is_encrypted && $value) {
            // 这里应该实现解密逻辑
            // 暂时返回原值
            return $value;
        }

        switch ($this->config_type) {
            case self::TYPE_INTEGER:
                return (int)$value;
            case self::TYPE_FLOAT:
                return (float)$value;
            case self::TYPE_BOOLEAN:
                return in_array(strtolower($value), ['1', 'true', 'yes', 'on']);
            case self::TYPE_JSON:
                return json_decode($value, true);
            case self::TYPE_ARRAY:
                return is_string($value) ? explode(',', $value) : $value;
            default:
                return $value;
        }
    }

    /**
     * 设置配置值（加密）
     */
    public function setRealValue($value)
    {
        switch ($this->config_type) {
            case self::TYPE_BOOLEAN:
                $value = $value ? '1' : '0';
                break;
            case self::TYPE_JSON:
                $value = is_string($value) ? $value : json_encode($value, JSON_UNESCAPED_UNICODE);
                break;
            case self::TYPE_ARRAY:
                $value = is_array($value) ? implode(',', $value) : $value;
                break;
        }

        if ($this->is_encrypted && $value) {
            // 这里应该实现加密逻辑
            // 暂时直接存储
        }

        $this->config_value = $value;
        return $this;
    }

    /**
     * 验证配置值
     */
    public function validateValue($value)
    {
        $rules = $this->validation_rules ?? [];

        if (empty($rules)) {
            return ['valid' => true];
        }

        // 这里应该实现验证逻辑
        // 暂时返回通过
        return ['valid' => true];
    }

    /**
     * 获取配置选项
     */
    public function getConfigOptions()
    {
        return $this->options ?? [];
    }

    /**
     * 检查是否可以编辑
     */
    public function canEdit()
    {
        return $this->is_editable;
    }

    /**
     * 检查是否可以公开访问
     */
    public function isPublic()
    {
        return $this->is_public;
    }

    /**
     * 获取配置摘要信息
     */
    public function getSummaryInfo()
    {
        return [
            'id' => $this->id,
            'config_key' => $this->config_key,
            'config_name' => $this->config_name,
            'config_value' => $this->formatted_value,
            'config_type' => $this->config_type,
            'config_type_name' => $this->config_type_name,
            'config_group' => $this->config_group,
            'config_group_name' => $this->config_group_name,
            'config_description' => $this->config_description,
            'default_value' => $this->default_value,
            'is_encrypted' => $this->is_encrypted,
            'is_public' => $this->is_public,
            'is_editable' => $this->is_editable,
            'can_edit' => $this->canEdit(),
            'options' => $this->getConfigOptions(),
            'sort_order' => $this->sort_order,
            'updated_at' => $this->updated_at
        ];
    }

    /**
     * 静态方法：获取配置值
     */
    public static function getValue($key, $default = null)
    {
        $config = static::where('config_key', $key)->find();

        if (!$config) {
            return $default;
        }

        return $config->getRealValue();
    }

    /**
     * 静态方法：设置配置值
     */
    public static function setValue($key, $value, $userId = null)
    {
        $config = static::where('config_key', $key)->find();

        if (!$config) {
            return false;
        }

        if (!$config->canEdit()) {
            return false;
        }

        $config->setRealValue($value);
        $config->updated_by = $userId;
        $config->updated_at = time();

        return $config->save();
    }

    /**
     * 静态方法：获取分组配置
     */
    public static function getGroupConfigs($group)
    {
        $configList = static::where('config_group', $group)
            ->order(['sort_order', 'config_key'])
            ->select();

        $result = [];
        foreach ($configList as $config) {
            $result[] = $config->getSummaryInfo();
        }

        return $result;
    }

    /**
     * 静态方法：获取所有公开配置
     */
    public static function getPublicConfigs()
    {
        $configList = static::where('is_public', 1)
            ->order(['config_group', 'sort_order', 'config_key'])
            ->select();

        $result = [];
        foreach ($configList as $config) {
            $group = $config->config_group;
            if (!isset($result[$group])) {
                $result[$group] = [];
            }
            $result[$group][] = $config->getSummaryInfo();
        }

        return $result;
    }

    /**
     * 静态方法：批量设置配置
     */
    public static function setBatchConfigs($configs, $userId = null)
    {
        $results = [];

        foreach ($configs as $key => $value) {
            $results[$key] = static::setValue($key, $value, $userId);
        }

        return $results;
    }

    /**
     * 静态方法：重置配置为默认值
     */
    public static function resetToDefault($key, $userId = null)
    {
        $config = static::where('config_key', $key)->find();

        if (!$config || !$config->canEdit()) {
            return false;
        }

        $config->setRealValue($config->default_value);
        $config->updated_by = $userId;
        $config->updated_at = time();

        return $config->save();
    }
}
