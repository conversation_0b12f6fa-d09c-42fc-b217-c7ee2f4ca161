<?php

declare(strict_types=1);

namespace app\service\payment;

use app\service\AlipayService;
use base\exception\BaseException;

class AlipayPayment implements PaymentInterface
{
    protected AlipayService $alipay;

    public function __construct()
    {
        $this->alipay = new AlipayService();
    }

    public function pay(array $payload): array
    {
        // payload: out_trade_no, total_amount, subject, notify_url, return_url
        $payUrl = $this->alipay->pagePay(
            $payload['out_trade_no'],
            (float)$payload['total_amount'],
            $payload['subject'],
            $payload['return_url'] ?? '',
            $payload['notify_url'] ?? ''
        );

        return [
            'channel' => 'alipay',
            'out_trade_no' => $payload['out_trade_no'],
            'amount' => $payload['total_amount'],
            'pay_info' => [ 'pay_url' => $payUrl ],
        ];
    }

    public function refund(array $payload): array
    {
        $res = $this->alipay->refund(
            $payload['out_trade_no'],
            (string)$payload['refund_amount'],
            $payload['out_refund_no'] ?? ('REF' . date('YmdHis'))
        );
        return [ 'success' => true, 'channel' => 'alipay', 'raw' => $res ];
    }

    public function query(array $payload): array
    {
        $res = $this->alipay->query($payload['out_trade_no']);
        return [ 'channel' => 'alipay', 'raw' => $res ];
    }

    public function notify(array $params): array
    {
        $res = $this->alipay->handleNotify($params);
        return [
            'success' => (bool)($res['success'] ?? false),
            'out_trade_no' => $res['out_trade_no'] ?? '',
            'trade_no' => $res['trade_no'] ?? '',
            'raw' => $res,
        ];
    }

    public function cancel(array $payload): array
    {
        // 支付宝撤销一般在未支付交易，暂用占位
        return [ 'success' => true, 'channel' => 'alipay' ];
    }
}

