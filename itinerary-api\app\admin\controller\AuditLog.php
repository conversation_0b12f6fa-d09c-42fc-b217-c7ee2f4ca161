<?php

namespace app\admin\controller;

use base\AuditLog\AirParse;
use base\exception\BaseException;
use support\Request;
use think\facade\Db;
use think\helper\Str;

class AuditLog
{
    public function index(Request $request)
    {
        $package = $request->get('package');
        $module = $request->get('module');
        $traceId = $request->get('traceId');
        $page = (int)$request->get('page', 1);
        $limit = (int)$request->get('pageSize', 100);
        if (empty($package)) {
            throw new BaseException('package is null');
        }
        $package = Str::snake($package);
        $where = [];

        if (!empty($traceId)) {
            $where[] = ['trace_id', '=', $traceId];
        }

        if (!empty($module)) {
            $where[] = ['module', '=', $module];
        }
        $lang = $request->get('lang', $request->acceptLanguage);

        try {
            $total = Db::name('audit_log_' . $package)
                ->where($where)
                ->count();
            $list = Db::name('audit_log_' . $package)
                ->where($where)
                ->page($page)
                ->limit($limit)
                ->order('create_time', 'desc')
                ->withAttr('create_time', function ($value) {
                    return formatDateTime($value);
                })
                ->withAttr('desc', function ($value) {
                    return json2Arr($value ?? '[]');
                })
                ->withAttr('diff', function ($value) {
                    return json2Arr($value ?? '[]');
                })
                ->withAttr('new', function ($value) {
                    return json2Arr($value ?? '[]');
                })
                ->withAttr('old', function ($value) {
                    return json2Arr($value ?? '[]');
                })
                ->withAttr('lang', function ($value) {
                    return json2Arr($value ?? '[]');
                })
                ->withAttr('config', function ($value) {
                    return json2Arr($value ?? '[]');
                })
                ->select()->toArray();
            foreach ($list as $key => $value) {
                $list[$key] = array_merge($value, (new AirParse($value, $lang))->get());
                $list[$key]['traceId'] = $list[$key]['trace_id'];
                $list[$key]['createTime'] = $list[$key]['create_time'];
                unset(
                    $list[$key]['config'],
                    $list[$key]['new'],
                    $list[$key]['old'],
                    $list[$key]['diff'],
                    $list[$key]['lang'],
                    $list[$key]['trace_id'],
                    $list[$key]['create_time'],
                );
            }
        } catch (\Exception $e) {
           throw $e;
        }

        return success(['items' => $list, 'total' =>$total ], '查询');
    }
}