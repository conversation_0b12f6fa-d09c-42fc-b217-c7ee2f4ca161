{"name": "workerman/webman", "type": "project", "keywords": ["high performance", "http service"], "homepage": "https://www.workerman.net", "license": "MIT", "description": "High performance HTTP Service Framework.", "authors": [{"name": "walkor", "email": "<EMAIL>", "homepage": "https://www.workerman.net", "role": "Developer"}], "support": {"email": "<EMAIL>", "issues": "https://github.com/walkor/webman/issues", "forum": "https://wenda.workerman.net/", "wiki": "https://workerman.net/doc/webman", "source": "https://github.com/walkor/webman"}, "require": {"php": ">=8.1", "workerman/webman-framework": "^2.1", "monolog/monolog": "^2.0", "php-di/php-di": "^7.0", "guzzlehttp/guzzle": "^7.9", "webman/think-orm": "^2.1", "vlucas/phpdotenv": "^5.6", "workerman/crontab": "^1.0", "tinywan/validate": "^1.0", "webman/console": "^2.1", "ext-bcmath": "*", "webman/redis-queue": "^2.1", "webman/redis": "^2.1", "illuminate/events": "^12.10", "webman/cache": "^2.1", "yzh52521/flysystem-oss": "^3.0", "exorcist-guo/filesystem": "^1.0.0", "firebase/php-jwt": "^6.11", "alipaysdk/easysdk": "^2.0"}, "suggest": {"ext-event": "For better performance. "}, "autoload": {"psr-4": {"": "./", "app\\": "./app", "App\\": "./app", "app\\View\\Components\\": "./app/view/components", "tests\\": "./tests"}, "files": ["./app/functions.php"]}, "scripts": {"post-package-install": ["support\\Plugin::install"], "post-package-update": ["support\\Plugin::install"], "pre-package-uninstall": ["support\\Plugin::uninstall"]}, "minimum-stability": "dev", "prefer-stable": true, "require-dev": {"phpunit/phpunit": "^11.5"}}