# 直播间功能说明

## 概述

本项目已完成直播间管理功能的开发，包括后台管理API和前端用户API。功能涵盖直播间的创建、管理、商品关联、统计数据等完整的直播电商功能。

## 数据库表结构

### 1. 直播间表 (live_room)
- 存储直播间基本信息
- 包含主播信息、直播状态、观看数据等
- 支持推荐、排序等功能

### 2. 直播商品关联表 (live_room_product)
- 管理直播间与商品的关联关系
- 支持直播间特价、折扣率设置
- 库存限制和销售统计

### 3. 直播间统计表 (live_room_stats)
- 记录直播间的日常统计数据
- 包含观看人数、点赞、分享、销售等数据

## 安装和配置

### 1. 创建数据库表
```bash
# 执行SQL文件创建表结构
mysql -u username -p database_name < database/migrations/create_live_room_tables.sql
```

### 2. 测试功能
```bash
# 测试模型功能
php test_live_room.php

# 测试API接口（需要先启动服务器）
php test_live_room_api.php
```

## API接口文档

### 后台管理API

#### 直播间管理
- `GET /admin/live-room` - 获取直播间列表
- `POST /admin/live-room` - 创建直播间
- `GET /admin/live-room/{id}` - 获取直播间详情
- `PUT /admin/live-room/{id}` - 更新直播间
- `DELETE /admin/live-room/{id}` - 删除直播间
- `POST /admin/live-room/batch-delete` - 批量删除直播间

#### 状态管理
- `PUT /admin/live-room/{id}/status` - 更新直播间状态

#### 商品管理
- `GET /admin/live-room/{id}/products` - 获取直播间商品列表
- `POST /admin/live-room/{id}/products` - 添加商品到直播间
- `POST /admin/live-room/{id}/products/batch` - 批量添加商品
- `DELETE /admin/live-room/{id}/products/{product_id}` - 移除直播间商品

#### 统计数据
- `GET /admin/live-room/{id}/stats` - 获取直播间统计数据

### 前端用户API

#### 直播间浏览
- `GET /api/live-room/list` - 获取直播间列表
- `GET /api/live-room/detail/{id}` - 获取直播间详情
- `GET /api/live-room/featured` - 获取推荐直播间
- `GET /api/live-room/live` - 获取正在直播的房间

#### 商品浏览
- `GET /api/live-room/{id}/products` - 获取直播间商品列表
- `GET /api/live-room/{id}/featured-products` - 获取直播间推荐商品

#### 互动功能
- `POST /api/live-room/{id}/like` - 点赞直播间
- `POST /api/live-room/{id}/share` - 分享直播间
- `GET /api/live-room/{id}/status` - 获取直播间状态信息
- `POST /api/live-room/{id}/viewer-count` - 更新观看人数

## 使用示例

### 创建直播间
```json
POST /admin/live-room
{
    "title": "巴厘岛6天5晚纯玩无购物",
    "description": "专业导游带您体验巴厘岛最美风光",
    "anchor_name": "小美导游",
    "anchor_avatar": "https://example.com/avatar.jpg",
    "cover_image": "https://example.com/cover.jpg",
    "scheduled_start_time": "2024-01-15 14:00:00",
    "scheduled_end_time": "2024-01-15 16:00:00",
    "is_featured": 1,
    "tags": ["巴厘岛", "海岛游", "纯玩团"],
    "notice": "欢迎大家来到直播间！"
}
```

### 添加商品到直播间
```json
POST /admin/live-room/{id}/products
{
    "product_id": 1,
    "special_price": 1199.00,
    "discount_rate": 80,
    "stock_limit": 10,
    "is_featured": 1
}
```

### 获取直播间列表
```json
GET /api/live-room/list?page=1&limit=10&status=live

Response:
{
    "code": 200,
    "message": "success",
    "data": {
        "list": [
            {
                "id": 1,
                "title": "巴厘岛6天5晚纯玩无购物",
                "anchor_name": "小美导游",
                "status": "live",
                "viewer_count": 1500,
                "like_count": 100
            }
        ],
        "total": 1,
        "page": 1,
        "page_size": 10
    }
}
```

## 直播状态说明

- `upcoming` - 即将开始
- `live` - 直播中
- `ended` - 已结束
- `cancelled` - 已取消

## 商品状态说明

- `active` - 激活状态
- `inactive` - 非激活状态

## 注意事项

1. **权限控制**: 后台管理API需要添加适当的权限验证中间件
2. **实时更新**: 观看人数、点赞数等需要配合WebSocket或定时刷新实现实时更新
3. **库存管理**: 直播间商品库存需要与实际商品库存同步
4. **数据统计**: 建议定时任务定期更新统计数据
5. **缓存优化**: 热门直播间数据可以考虑使用Redis缓存

## 扩展功能建议

1. **直播回放**: 支持直播结束后的回放功能
2. **弹幕系统**: 实时聊天和弹幕功能
3. **抽奖活动**: 直播间内的抽奖和优惠券发放
4. **分享奖励**: 分享直播间获得积分或优惠券
5. **预约提醒**: 直播开始前的推送提醒
6. **多主播**: 支持多个主播同时直播
7. **直播录制**: 自动录制直播内容
8. **数据分析**: 更详细的观看行为分析

## 技术栈

- **后端框架**: Webman
- **数据库**: MySQL
- **ORM**: ThinkORM
- **缓存**: Redis (可选)
- **队列**: 支持异步任务处理

## 联系方式

如有问题或建议，请联系开发团队。
