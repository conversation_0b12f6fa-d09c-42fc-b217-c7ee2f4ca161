CREATE TABLE `itinerary_inventory` (
   `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
   `itinerary_id` varchar(32) NOT NULL COMMENT '线路ID',
   `inventory_date` date NOT NULL COMMENT '库存日期',
   `status` enum('1','0') NOT NULL DEFAULT '0' COMMENT '库存状态',
   `total_amount` int(11) DEFAULT NULL COMMENT '总库存数量，NULL表示不限',
   `booked_amount` int(11) NOT NULL DEFAULT '0' COMMENT '已预约数量',
   `rule_id` varchar(32) DEFAULT NULL COMMENT '对应的规则ID',
   `has_hot_date_surcharge` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否有热门日期加价',
   `hot_date_surcharge_amount` decimal(10,2) DEFAULT NULL COMMENT '热门日期加价金额',
   `has_traveler_surcharge` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否有出行人加价',
   `adult_surcharge_amount` decimal(10,2) DEFAULT NULL COMMENT '成人加价金额',
   `child_surcharge_amount` decimal(10,2) DEFAULT NULL COMMENT '儿童加价金额',
   `create_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
   `update_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
   `delete_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '删除时间',
   PRIMARY KEY (`id`),
   UNIQUE KEY `uk_itinerary_date` (`itinerary_id`,`inventory_date`),
   KEY `idx_inventory_date` (`inventory_date`),
   KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='线路每日库存表';

CREATE TABLE `itinerary_order` (
   `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
   `itinerary_id` varchar(32) NOT NULL COMMENT '线路ID',
   `order_no` varchar(32) NOT NULL COMMENT '订单编号',
   `user_id` varchar(32) NOT NULL COMMENT '用户ID',
   `name` varchar(32) NOT NULL COMMENT '用户姓名',
   `phone` varchar(32) NOT NULL COMMENT '联系电话',
   `travel_date` date NOT NULL COMMENT '出行日期',
   `adult_count` int(11) NOT NULL DEFAULT '0' COMMENT '成人人数',
   `child_count` int(11) NOT NULL DEFAULT '0' COMMENT '儿童人数',
   `base_price` decimal(10,2) NOT NULL COMMENT '基础价格',
   `hot_date_surcharge` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '热门日期加价总额',
   `adult_surcharge` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '成人加价总额',
   `child_surcharge` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '儿童加价总额',
   `room_count` int(11) DEFAULT '0' COMMENT '房间数量',
   `single_room_count` int(11) DEFAULT '0' COMMENT '单人间数量',
   `upgrade_room_count` int(11) DEFAULT '0' COMMENT '升级房间数量',
   `single_room_surcharge` decimal(10,2) DEFAULT '0.00' COMMENT '单房差',
   `upgrade_room_surcharge` decimal(10,2) DEFAULT '0.00' COMMENT '升级房差',
   `remark` text COMMENT '订单备注',
   `total_amount` decimal(10,2) NOT NULL COMMENT '订单总金额',
   `status` enum('pending','paid','cancelled','completed') NOT NULL DEFAULT 'pending' COMMENT '订单状态',
   `create_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
   `update_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
   `delete_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '删除时间',
   PRIMARY KEY (`id`),
   UNIQUE KEY `uk_order_no` (`order_no`),
   KEY `idx_itinerary_id` (`itinerary_id`),
   KEY `idx_user_id` (`user_id`),
   KEY `idx_travel_date` (`travel_date`),
   KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='线路订单表';