<?php

namespace app\controller;

use base\auth\Auth;
use base\auth\UserInfo;
use support\Request;
use support\Response;

/**
 * 测试鉴权功能的控制器
 */
class TestAuthController
{
    /**
     * 测试需要鉴权的接口
     * 
     * @param Request $request
     * @return Response
     */
    #[Auth('itinerary:test:read', '测试读取权限')]
    public function testRead(Request $request): Response
    {
        // 获取当前用户信息
        $userInfo = UserInfo::get();
        
        return response()->json([
            'code' => 200,
            'message' => '鉴权成功',
            'data' => [
                'action' => 'itinerary:test:read',
                'user_info' => $userInfo,
                'timestamp' => date('Y-m-d H:i:s')
            ]
        ]);
    }

    /**
     * 测试需要写权限的接口
     * 
     * @param Request $request
     * @return Response
     */
    #[Auth('itinerary:test:write', '测试写入权限')]
    public function testWrite(Request $request): Response
    {
        // 获取当前用户信息
        $userInfo = UserInfo::get();
        
        return response()->json([
            'code' => 200,
            'message' => '鉴权成功',
            'data' => [
                'action' => 'itinerary:test:write',
                'user_info' => $userInfo,
                'request_data' => $request->all(),
                'timestamp' => date('Y-m-d H:i:s')
            ]
        ]);
    }

    /**
     * 测试需要管理员权限的接口
     * 
     * @param Request $request
     * @return Response
     */
    #[Auth('itinerary:test:admin', '测试管理员权限')]
    public function testAdmin(Request $request): Response
    {
        // 获取当前用户信息
        $userInfo = UserInfo::get();
        
        return response()->json([
            'code' => 200,
            'message' => '管理员鉴权成功',
            'data' => [
                'action' => 'itinerary:test:admin',
                'user_info' => $userInfo,
                'timestamp' => date('Y-m-d H:i:s')
            ]
        ]);
    }

    /**
     * 测试多个权限的接口
     * 
     * @param Request $request
     * @return Response
     */
    #[Auth('itinerary:test:read', '测试读取权限')]
    #[Auth('itinerary:test:write', '测试写入权限')]
    public function testMultiple(Request $request): Response
    {
        // 获取当前用户信息
        $userInfo = UserInfo::get();
        
        return response()->json([
            'code' => 200,
            'message' => '多权限鉴权成功',
            'data' => [
                'actions' => ['itinerary:test:read', 'itinerary:test:write'],
                'user_info' => $userInfo,
                'timestamp' => date('Y-m-d H:i:s')
            ]
        ]);
    }

    /**
     * 不需要鉴权的公开接口
     * 
     * @param Request $request
     * @return Response
     */
    public function testPublic(Request $request): Response
    {
        return response()->json([
            'code' => 200,
            'message' => '公开接口，无需鉴权',
            'data' => [
                'timestamp' => date('Y-m-d H:i:s')
            ]
        ]);
    }
}
