<?php

declare(strict_types=1);

namespace app\api\controller;

use app\model\UserAuthModel;
use app\service\UserAuthService;
use app\service\XiaoHongShuClient;
use base\exception\BaseException;
use base\helper\Output;
use support\Request;
use app\model\UserModel;
use think\Exception;

class User
{
    /**
     * @var UserAuthService
     */
    private UserAuthService $authService;

    /**
     * Constructor
     */
    public function __construct()
    {
        $this->authService = new UserAuthService();
    }

    /**
     * Register a new user
     *
     * @param Request $request
     * @return \support\Response
     */
    public function register(Request $request)
    {
        try {
            $data = $request->post();
            $result = $this->authService->register($data);

            return Output::success($result);
        } catch (BaseException $e) {
            return Output::error($e->getCode(), $e->getMessage());
        }
    }

    /**
     * Login with username and password
     *
     * @param Request $request
     * @return \support\Response
     */
    public function login(Request $request)
    {
        try {
            $username = $request->post('username');
            $password = $request->post('password');

            if (empty($username) || empty($password)) {
                throw new BaseException('用户名和密码不能为空', 400);
            }

            $result = $this->authService->login($username, $password);

            return Output::success($result);
        } catch (BaseException $e) {
            return Output::error($e->getCode(), $e->getMessage());
        }
    }

    /**
     * 第三方预登录
     */
    public function preLogin(Request $request)
    {
        $platform = $request->post('platform');
        $code = $request->post('code');
        if (empty($code)) {
            throw new BaseException('code不能为空', 400);
        }

        // 第三方预登录
        switch ($platform) {
            case UserAuthModel::PLATFORM_XHS :
                $xhsClient = new XiaoHongShuClient();
                $result = $xhsClient->sendRequest(XiaoHongShuClient::API_RMP_SESSION, ['code' => $code], 'GET');
                if (!$result || (isset($result['success']) && $result['success'] == false)) {
                    $error_msg = isset($result['error_description']) ? $result['error_description'] : '小红书登录失败';
                    throw new Exception($error_msg);
                }
                $result = [
                    'platform_id' => $result['data']['openid'] ?? ''
                ];
                break;
            default:
                throw new BaseException('暂不支持的登录方式', 400);
        }

        return Output::success($result);
    }

    /**
     * Third-party authorization login
     *
     * @param Request $request
     * @return \support\Response
     */
    public function authLogin(Request $request)
    {
        try {
            $platform = $request->post('platform');
            $platformId = $request->post('platform_id');
            $authData = $request->post();

            if (empty($platform) || empty($platformId)) {
                throw new BaseException('平台和平台ID不能为空', 400);
            }

            $result = $this->authService->authLogin($platform, $platformId, $authData);
            return Output::success($result);
        } catch (BaseException $e) {
            return Output::error($e->getCode(), $e->getMessage());
        }
    }

    /**
     * Get current user info
     *
     * @param Request $request
     * @return \support\Response
     */
    public function profile(Request $request)
    {
        try {
            // User is already set in request by middleware
            $user = $request->user;

            if (!$user) {
                throw new BaseException('未登录', 401);
            }

            return Output::success([
                'user' => $user
            ]);
        } catch (BaseException $e) {
            return Output::error($e->getCode(), $e->getMessage());
        }
    }

    /**
     * Update user profile
     *
     * @param Request $request
     * @return \support\Response
     */
    public function updateProfile(Request $request)
    {
        try {
            $user = $request->user;

            if (!$user) {
                throw new BaseException('未登录', 401);
            }

            $data = $request->post();
            $allowedFields = ['nickname', 'avatar', 'gender', 'email', 'phone'];
            $updateData = array_intersect_key($data, array_flip($allowedFields));

            // Check if email already exists
            if (!empty($updateData['email']) && $updateData['email'] !== $user->email) {
                $existingUser = UserModel::getByEmail($updateData['email']);
                if ($existingUser && $existingUser->id !== $user->id) {
                    throw new BaseException('邮箱已被使用', 400);
                }
            }

            // Check if phone already exists
            if (!empty($updateData['phone']) && $updateData['phone'] !== $user->phone) {
                $existingUser = UserModel::getByPhone($updateData['phone']);
                if ($existingUser && $existingUser->id !== $user->id) {
                    throw new BaseException('手机号已被使用', 400);
                }
            }

            if (!empty($updateData)) {
                $user->save($updateData);
            }

            return Output::success([
                'user' => $user
            ]);
        } catch (BaseException $e) {
            return Output::error($e->getCode(), $e->getMessage());
        }
    }

    /**
     * Change password
     *
     * @param Request $request
     * @return \support\Response
     */
    public function changePassword(Request $request)
    {
        try {
            $user = $request->user;

            if (!$user) {
                throw new BaseException('未登录', 401);
            }

            $oldPassword = $request->post('old_password');
            $newPassword = $request->post('new_password');
            $confirmPassword = $request->post('confirm_password');

            if (empty($oldPassword) || empty($newPassword) || empty($confirmPassword)) {
                throw new BaseException('所有密码字段都不能为空', 400);
            }

            if ($newPassword !== $confirmPassword) {
                throw new BaseException('两次输入的新密码不一致', 400);
            }

            // Verify old password
            if (!$user->verifyPassword($oldPassword)) {
                throw new BaseException('原密码不正确', 400);
            }

            // Update password
            $user->save([
                'password' => password_hash($newPassword, PASSWORD_DEFAULT)
            ]);

            return Output::success([
                'message' => '密码修改成功'
            ]);
        } catch (BaseException $e) {
            return Output::error($e->getCode(), $e->getMessage());
        }
    }
}
