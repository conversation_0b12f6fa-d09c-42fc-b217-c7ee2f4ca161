[2025-08-11 14:03:28] default.ERROR: 禁用用户失败 {"error":"Attempt to read property \"status\" on null","file":"D:\\work\\hotel\\all\\itinerary-api\\app\\admin\\controller\\UserController.php","line":62,"trace":"#0 D:\\work\\hotel\\all\\itinerary-api\\app\\admin\\controller\\UserController.php(62): {closure}(2, 'Attempt to read...', 'D:\\\\work\\\\hotel\\\\a...', 62)
#1 D:\\work\\hotel\\all\\itinerary-api\\vendor\\workerman\\webman-framework\\src\\App.php(336): app\\admin\\controller\\UserController->disabled(Object(support\\Request), 1)
#2 D:\\work\\hotel\\all\\itinerary-api\\vendor\\workerman\\webman-framework\\src\\App.php(359): Webman\\App::Webman\\{closure}(Object(support\\Request), 1)
#3 D:\\work\\hotel\\all\\itinerary-api\\base\\middleware\\AcceptLanguage.php(81): Webman\\App::Webman\\{closure}(Object(support\\Request))
#4 D:\\work\\hotel\\all\\itinerary-api\\vendor\\workerman\\webman-framework\\src\\App.php(352): base\\middleware\\AcceptLanguage->process(Object(support\\Request), Object(Closure))
#5 D:\\work\\hotel\\all\\itinerary-api\\base\\middleware\\AccessControlTest.php(13): Webman\\App::Webman\\{closure}(Object(support\\Request))
#6 D:\\work\\hotel\\all\\itinerary-api\\vendor\\workerman\\webman-framework\\src\\App.php(352): base\\middleware\\AccessControlTest->process(Object(support\\Request), Object(Closure))
#7 D:\\work\\hotel\\all\\itinerary-api\\vendor\\workerman\\webman-framework\\src\\App.php(663): Webman\\App::Webman\\{closure}(Object(support\\Request))
#8 D:\\work\\hotel\\all\\itinerary-api\\vendor\\workerman\\webman-framework\\src\\App.php(158): Webman\\App::findRoute(Object(Workerman\\Connection\\TcpConnection), '/admin/user/dis...', 'POST/admin/user...', Object(support\\Request), 200)
#9 D:\\work\\hotel\\all\\itinerary-api\\vendor\\workerman\\workerman\\src\\Connection\\TcpConnection.php(750): Webman\\App->onMessage(Object(Workerman\\Connection\\TcpConnection), Object(support\\Request))
#10 D:\\work\\hotel\\all\\itinerary-api\\vendor\\workerman\\workerman\\src\\Events\\Select.php(406): Workerman\\Connection\\TcpConnection->baseRead(Resource id #372)
#11 D:\\work\\hotel\\all\\itinerary-api\\vendor\\workerman\\workerman\\src\\Worker.php(1613): Workerman\\Events\\Select->run()
#12 D:\\work\\hotel\\all\\itinerary-api\\vendor\\workerman\\workerman\\src\\Worker.php(1528): Workerman\\Worker::forkWorkersForWindows()
#13 D:\\work\\hotel\\all\\itinerary-api\\vendor\\workerman\\workerman\\src\\Worker.php(594): Workerman\\Worker::forkWorkers()
#14 D:\\work\\hotel\\all\\itinerary-api\\runtime\\windows\\start_webman.php(33): Workerman\\Worker::runAll()
#15 {main}"} []
[2025-08-11 14:07:04] default.ERROR: 127.0.0.1 POST 127.0.0.1:8899/admin/user/disabled/1
Error: Attempt to assign property "status" on null in D:\work\hotel\all\itinerary-api\app\admin\controller\UserController.php:62
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\App.php(336): app\admin\controller\UserController->disabled(Object(support\Request), 1)
#1 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\App.php(359): Webman\App::Webman\{closure}(Object(support\Request), 1)
#2 D:\work\hotel\all\itinerary-api\base\middleware\AcceptLanguage.php(81): Webman\App::Webman\{closure}(Object(support\Request))
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\App.php(352): base\middleware\AcceptLanguage->process(Object(support\Request), Object(Closure))
#4 D:\work\hotel\all\itinerary-api\base\middleware\AccessControlTest.php(13): Webman\App::Webman\{closure}(Object(support\Request))
#5 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\App.php(352): base\middleware\AccessControlTest->process(Object(support\Request), Object(Closure))
#6 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\App.php(663): Webman\App::Webman\{closure}(Object(support\Request))
#7 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\App.php(158): Webman\App::findRoute(Object(Workerman\Connection\TcpConnection), '/admin/user/dis...', 'POST/admin/user...', Object(support\Request), 200)
#8 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Connection\TcpConnection.php(750): Webman\App->onMessage(Object(Workerman\Connection\TcpConnection), Object(support\Request))
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Events\Select.php(406): Workerman\Connection\TcpConnection->baseRead(Resource id #332)
#10 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1613): Workerman\Events\Select->run()
#11 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#12 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#13 D:\work\hotel\all\itinerary-api\runtime\windows\start_webman.php(33): Workerman\Worker::runAll()
#14 {main} [] []
[2025-08-11 14:08:10] default.ERROR: 127.0.0.1 POST 127.0.0.1:8899/admin/user/disabled/1
Error: Attempt to assign property "status" on null in D:\work\hotel\all\itinerary-api\app\admin\controller\UserController.php:62
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\App.php(336): app\admin\controller\UserController->disabled(Object(support\Request), 1)
#1 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\App.php(359): Webman\App::Webman\{closure}(Object(support\Request), 1)
#2 D:\work\hotel\all\itinerary-api\base\middleware\AcceptLanguage.php(81): Webman\App::Webman\{closure}(Object(support\Request))
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\App.php(352): base\middleware\AcceptLanguage->process(Object(support\Request), Object(Closure))
#4 D:\work\hotel\all\itinerary-api\base\middleware\AccessControlTest.php(13): Webman\App::Webman\{closure}(Object(support\Request))
#5 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\App.php(352): base\middleware\AccessControlTest->process(Object(support\Request), Object(Closure))
#6 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\App.php(663): Webman\App::Webman\{closure}(Object(support\Request))
#7 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\App.php(158): Webman\App::findRoute(Object(Workerman\Connection\TcpConnection), '/admin/user/dis...', 'POST/admin/user...', Object(support\Request), 200)
#8 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Connection\TcpConnection.php(750): Webman\App->onMessage(Object(Workerman\Connection\TcpConnection), Object(support\Request))
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Events\Select.php(406): Workerman\Connection\TcpConnection->baseRead(Resource id #332)
#10 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1613): Workerman\Events\Select->run()
#11 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#12 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#13 D:\work\hotel\all\itinerary-api\runtime\windows\start_webman.php(33): Workerman\Worker::runAll()
#14 {main} [] []
[2025-08-11 14:08:19] default.ERROR: 127.0.0.1 POST 127.0.0.1:8899/admin/user/disabled/1
Error: Attempt to assign property "status" on null in D:\work\hotel\all\itinerary-api\app\admin\controller\UserController.php:62
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\App.php(336): app\admin\controller\UserController->disabled(Object(support\Request), 1)
#1 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\App.php(359): Webman\App::Webman\{closure}(Object(support\Request), 1)
#2 D:\work\hotel\all\itinerary-api\base\middleware\AcceptLanguage.php(81): Webman\App::Webman\{closure}(Object(support\Request))
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\App.php(352): base\middleware\AcceptLanguage->process(Object(support\Request), Object(Closure))
#4 D:\work\hotel\all\itinerary-api\base\middleware\AccessControlTest.php(13): Webman\App::Webman\{closure}(Object(support\Request))
#5 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\App.php(352): base\middleware\AccessControlTest->process(Object(support\Request), Object(Closure))
#6 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\App.php(663): Webman\App::Webman\{closure}(Object(support\Request))
#7 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\App.php(158): Webman\App::findRoute(Object(Workerman\Connection\TcpConnection), '/admin/user/dis...', 'POST/admin/user...', Object(support\Request), 200)
#8 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Connection\TcpConnection.php(750): Webman\App->onMessage(Object(Workerman\Connection\TcpConnection), Object(support\Request))
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Events\Select.php(406): Workerman\Connection\TcpConnection->baseRead(Resource id #332)
#10 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1613): Workerman\Events\Select->run()
#11 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#12 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#13 D:\work\hotel\all\itinerary-api\runtime\windows\start_webman.php(33): Workerman\Worker::runAll()
#14 {main} [] []
[2025-08-11 14:08:21] default.ERROR: 127.0.0.1 POST 127.0.0.1:8899/admin/user/disabled/1
Error: Attempt to assign property "status" on null in D:\work\hotel\all\itinerary-api\app\admin\controller\UserController.php:62
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\App.php(336): app\admin\controller\UserController->disabled(Object(support\Request), 1)
#1 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\App.php(359): Webman\App::Webman\{closure}(Object(support\Request), 1)
#2 D:\work\hotel\all\itinerary-api\base\middleware\AcceptLanguage.php(81): Webman\App::Webman\{closure}(Object(support\Request))
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\App.php(352): base\middleware\AcceptLanguage->process(Object(support\Request), Object(Closure))
#4 D:\work\hotel\all\itinerary-api\base\middleware\AccessControlTest.php(13): Webman\App::Webman\{closure}(Object(support\Request))
#5 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\App.php(352): base\middleware\AccessControlTest->process(Object(support\Request), Object(Closure))
#6 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\App.php(150): Webman\App::Webman\{closure}(Object(support\Request))
#7 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Connection\TcpConnection.php(679): Webman\App->onMessage(Object(Workerman\Connection\TcpConnection), Object(support\Request))
#8 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Events\Select.php(406): Workerman\Connection\TcpConnection->baseRead(Resource id #332)
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1613): Workerman\Events\Select->run()
#10 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#11 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#12 D:\work\hotel\all\itinerary-api\runtime\windows\start_webman.php(33): Workerman\Worker::runAll()
#13 {main} [] []
[2025-08-11 14:08:40] default.ERROR: 127.0.0.1 POST 127.0.0.1:8899/admin/user/disabled/1
Error: Attempt to assign property "status" on null in D:\work\hotel\all\itinerary-api\app\admin\controller\UserController.php:62
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\App.php(336): app\admin\controller\UserController->disabled(Object(support\Request), 1)
#1 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\App.php(359): Webman\App::Webman\{closure}(Object(support\Request), 1)
#2 D:\work\hotel\all\itinerary-api\base\middleware\AcceptLanguage.php(81): Webman\App::Webman\{closure}(Object(support\Request))
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\App.php(352): base\middleware\AcceptLanguage->process(Object(support\Request), Object(Closure))
#4 D:\work\hotel\all\itinerary-api\base\middleware\AccessControlTest.php(13): Webman\App::Webman\{closure}(Object(support\Request))
#5 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\App.php(352): base\middleware\AccessControlTest->process(Object(support\Request), Object(Closure))
#6 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\App.php(663): Webman\App::Webman\{closure}(Object(support\Request))
#7 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\App.php(158): Webman\App::findRoute(Object(Workerman\Connection\TcpConnection), '/admin/user/dis...', 'POST/admin/user...', Object(support\Request), 200)
#8 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Connection\TcpConnection.php(750): Webman\App->onMessage(Object(Workerman\Connection\TcpConnection), Object(support\Request))
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Events\Select.php(406): Workerman\Connection\TcpConnection->baseRead(Resource id #332)
#10 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1613): Workerman\Events\Select->run()
#11 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#12 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#13 D:\work\hotel\all\itinerary-api\runtime\windows\start_webman.php(33): Workerman\Worker::runAll()
#14 {main} [] []
[2025-08-11 14:09:33] default.ERROR: 127.0.0.1 POST 127.0.0.1:8899/admin/user/disabled/1
Error: Call to a member function toArray() on null in D:\work\hotel\all\itinerary-api\app\admin\controller\UserController.php:62
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\App.php(336): app\admin\controller\UserController->disabled(Object(support\Request), 1)
#1 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\App.php(359): Webman\App::Webman\{closure}(Object(support\Request), 1)
#2 D:\work\hotel\all\itinerary-api\base\middleware\AcceptLanguage.php(81): Webman\App::Webman\{closure}(Object(support\Request))
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\App.php(352): base\middleware\AcceptLanguage->process(Object(support\Request), Object(Closure))
#4 D:\work\hotel\all\itinerary-api\base\middleware\AccessControlTest.php(13): Webman\App::Webman\{closure}(Object(support\Request))
#5 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\App.php(352): base\middleware\AccessControlTest->process(Object(support\Request), Object(Closure))
#6 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\App.php(663): Webman\App::Webman\{closure}(Object(support\Request))
#7 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\App.php(158): Webman\App::findRoute(Object(Workerman\Connection\TcpConnection), '/admin/user/dis...', 'POST/admin/user...', Object(support\Request), 200)
#8 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Connection\TcpConnection.php(750): Webman\App->onMessage(Object(Workerman\Connection\TcpConnection), Object(support\Request))
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Events\Select.php(406): Workerman\Connection\TcpConnection->baseRead(Resource id #332)
#10 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1613): Workerman\Events\Select->run()
#11 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#12 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#13 D:\work\hotel\all\itinerary-api\runtime\windows\start_webman.php(33): Workerman\Worker::runAll()
#14 {main} [] []
[2025-08-11 14:57:54] default.ERROR: 127.0.0.1 GET 127.0.0.1:8899/system-config/types
Error: Call to undefined method app\admin\controller\SystemConfigController::success() in D:\work\hotel\all\itinerary-api\app\admin\controller\SystemConfigController.php:310
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\App.php(336): app\admin\controller\SystemConfigController->getTypes(Object(support\Request))
#1 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\App.php(359): Webman\App::Webman\{closure}(Object(support\Request))
#2 D:\work\hotel\all\itinerary-api\base\middleware\AcceptLanguage.php(81): Webman\App::Webman\{closure}(Object(support\Request))
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\App.php(352): base\middleware\AcceptLanguage->process(Object(support\Request), Object(Closure))
#4 D:\work\hotel\all\itinerary-api\base\middleware\AccessControlTest.php(13): Webman\App::Webman\{closure}(Object(support\Request))
#5 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\App.php(352): base\middleware\AccessControlTest->process(Object(support\Request), Object(Closure))
#6 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\App.php(663): Webman\App::Webman\{closure}(Object(support\Request))
#7 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\App.php(158): Webman\App::findRoute(Object(Workerman\Connection\TcpConnection), '/system-config/...', 'GET/system-conf...', Object(support\Request), 200)
#8 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Connection\TcpConnection.php(750): Webman\App->onMessage(Object(Workerman\Connection\TcpConnection), Object(support\Request))
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Events\Select.php(406): Workerman\Connection\TcpConnection->baseRead(Resource id #485)
#10 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1613): Workerman\Events\Select->run()
#11 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#12 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#13 D:\work\hotel\all\itinerary-api\runtime\windows\start_webman.php(33): Workerman\Worker::runAll()
#14 {main} [] []
[2025-08-11 14:58:57] default.ERROR: 127.0.0.1 GET 127.0.0.1:8899/system-config/types
Error: Call to undefined method app\admin\controller\SystemConfigController::success() in D:\work\hotel\all\itinerary-api\app\admin\controller\SystemConfigController.php:310
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\App.php(336): app\admin\controller\SystemConfigController->getTypes(Object(support\Request))
#1 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\App.php(359): Webman\App::Webman\{closure}(Object(support\Request))
#2 D:\work\hotel\all\itinerary-api\base\middleware\AcceptLanguage.php(81): Webman\App::Webman\{closure}(Object(support\Request))
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\App.php(352): base\middleware\AcceptLanguage->process(Object(support\Request), Object(Closure))
#4 D:\work\hotel\all\itinerary-api\base\middleware\AccessControlTest.php(13): Webman\App::Webman\{closure}(Object(support\Request))
#5 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\App.php(352): base\middleware\AccessControlTest->process(Object(support\Request), Object(Closure))
#6 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\App.php(150): Webman\App::Webman\{closure}(Object(support\Request))
#7 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Connection\TcpConnection.php(679): Webman\App->onMessage(Object(Workerman\Connection\TcpConnection), Object(support\Request))
#8 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Events\Select.php(406): Workerman\Connection\TcpConnection->baseRead(Resource id #494)
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1613): Workerman\Events\Select->run()
#10 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#11 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#12 D:\work\hotel\all\itinerary-api\runtime\windows\start_webman.php(33): Workerman\Worker::runAll()
#13 {main} [] []
[2025-08-11 15:22:11] default.ERROR: 127.0.0.1 PUT localhost:8899/system-config/12
TypeError: error(): Argument #1 ($code) must be of type int, string given, called in D:\work\hotel\all\itinerary-api\app\admin\controller\BaseController.php on line 255 and defined in D:\work\hotel\all\itinerary-api\app\functions.php:140
Stack trace:
#0 D:\work\hotel\all\itinerary-api\app\admin\controller\BaseController.php(255): error('\xE9\x85\x8D\xE7\xBD\xAE\xE4\xB8\x8D\xE5\xAD\x98\xE5\x9C\xA8', 404, NULL)
#1 D:\work\hotel\all\itinerary-api\app\admin\controller\SystemConfigController.php(185): app\admin\controller\BaseController->handleServiceResult(Array)
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\App.php(336): app\admin\controller\SystemConfigController->update(Object(support\Request), '12')
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\App.php(359): Webman\App::Webman\{closure}(Object(support\Request), '12')
#4 D:\work\hotel\all\itinerary-api\base\middleware\AcceptLanguage.php(81): Webman\App::Webman\{closure}(Object(support\Request))
#5 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\App.php(352): base\middleware\AcceptLanguage->process(Object(support\Request), Object(Closure))
#6 D:\work\hotel\all\itinerary-api\base\middleware\AccessControlTest.php(13): Webman\App::Webman\{closure}(Object(support\Request))
#7 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\App.php(352): base\middleware\AccessControlTest->process(Object(support\Request), Object(Closure))
#8 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\App.php(663): Webman\App::Webman\{closure}(Object(support\Request))
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\App.php(158): Webman\App::findRoute(Object(Workerman\Connection\TcpConnection), '/system-config/...', 'PUT/system-conf...', Object(support\Request), 200)
#10 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Connection\TcpConnection.php(750): Webman\App->onMessage(Object(Workerman\Connection\TcpConnection), Object(support\Request))
#11 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Events\Select.php(406): Workerman\Connection\TcpConnection->baseRead(Resource id #396)
#12 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1613): Workerman\Events\Select->run()
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#14 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#15 D:\work\hotel\all\itinerary-api\runtime\windows\start_webman.php(33): Workerman\Worker::runAll()
#16 {main} [] []
[2025-08-11 16:50:39] default.ERROR: 127.0.0.1 POST localhost:8899/system-config
TypeError: error(): Argument #1 ($code) must be of type int, string given, called in D:\work\hotel\all\itinerary-api\app\admin\controller\SystemConfigController.php on line 146 and defined in D:\work\hotel\all\itinerary-api\app\functions.php:140
Stack trace:
#0 D:\work\hotel\all\itinerary-api\app\admin\controller\SystemConfigController.php(146): error('\xE6\x97\xA0\xE6\x95\x88\xE7\x9A\x84\xE9\x85\x8D\xE7\xBD\xAE...')
#1 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\App.php(336): app\admin\controller\SystemConfigController->store(Object(support\Request))
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\App.php(359): Webman\App::Webman\{closure}(Object(support\Request))
#3 D:\work\hotel\all\itinerary-api\base\middleware\AcceptLanguage.php(81): Webman\App::Webman\{closure}(Object(support\Request))
#4 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\App.php(352): base\middleware\AcceptLanguage->process(Object(support\Request), Object(Closure))
#5 D:\work\hotel\all\itinerary-api\base\middleware\AccessControlTest.php(13): Webman\App::Webman\{closure}(Object(support\Request))
#6 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\App.php(352): base\middleware\AccessControlTest->process(Object(support\Request), Object(Closure))
#7 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\App.php(663): Webman\App::Webman\{closure}(Object(support\Request))
#8 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\App.php(158): Webman\App::findRoute(Object(Workerman\Connection\TcpConnection), '/system-config', 'POST/system-con...', Object(support\Request), 200)
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Connection\TcpConnection.php(750): Webman\App->onMessage(Object(Workerman\Connection\TcpConnection), Object(support\Request))
#10 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Events\Select.php(406): Workerman\Connection\TcpConnection->baseRead(Resource id #728)
#11 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1613): Workerman\Events\Select->run()
#12 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#14 D:\work\hotel\all\itinerary-api\runtime\windows\start_webman.php(33): Workerman\Worker::runAll()
#15 {main} [] []
[2025-08-11 16:52:38] default.ERROR: 127.0.0.1 POST localhost:8899/system-config
TypeError: error(): Argument #1 ($code) must be of type int, string given, called in D:\work\hotel\all\itinerary-api\app\admin\controller\SystemConfigController.php on line 146 and defined in D:\work\hotel\all\itinerary-api\app\functions.php:140
Stack trace:
#0 D:\work\hotel\all\itinerary-api\app\admin\controller\SystemConfigController.php(146): error('\xE6\x97\xA0\xE6\x95\x88\xE7\x9A\x84\xE9\x85\x8D\xE7\xBD\xAE...')
#1 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\App.php(336): app\admin\controller\SystemConfigController->store(Object(support\Request))
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\App.php(359): Webman\App::Webman\{closure}(Object(support\Request))
#3 D:\work\hotel\all\itinerary-api\base\middleware\AcceptLanguage.php(81): Webman\App::Webman\{closure}(Object(support\Request))
#4 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\App.php(352): base\middleware\AcceptLanguage->process(Object(support\Request), Object(Closure))
#5 D:\work\hotel\all\itinerary-api\base\middleware\AccessControlTest.php(13): Webman\App::Webman\{closure}(Object(support\Request))
#6 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\App.php(352): base\middleware\AccessControlTest->process(Object(support\Request), Object(Closure))
#7 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\App.php(150): Webman\App::Webman\{closure}(Object(support\Request))
#8 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Connection\TcpConnection.php(750): Webman\App->onMessage(Object(Workerman\Connection\TcpConnection), Object(support\Request))
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Events\Select.php(406): Workerman\Connection\TcpConnection->baseRead(Resource id #751)
#10 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1613): Workerman\Events\Select->run()
#11 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#12 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#13 D:\work\hotel\all\itinerary-api\runtime\windows\start_webman.php(33): Workerman\Worker::runAll()
#14 {main} [] []
[2025-08-11 17:13:29] default.ERROR: 127.0.0.1 POST localhost:8899/system-config
TypeError: error(): Argument #1 ($code) must be of type int, string given, called in D:\work\hotel\all\itinerary-api\app\admin\controller\SystemConfigController.php on line 146 and defined in D:\work\hotel\all\itinerary-api\app\functions.php:140
Stack trace:
#0 D:\work\hotel\all\itinerary-api\app\admin\controller\SystemConfigController.php(146): error('\xE6\x97\xA0\xE6\x95\x88\xE7\x9A\x84\xE9\x85\x8D\xE7\xBD\xAE...')
#1 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\App.php(336): app\admin\controller\SystemConfigController->store(Object(support\Request))
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\App.php(359): Webman\App::Webman\{closure}(Object(support\Request))
#3 D:\work\hotel\all\itinerary-api\base\middleware\AcceptLanguage.php(81): Webman\App::Webman\{closure}(Object(support\Request))
#4 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\App.php(352): base\middleware\AcceptLanguage->process(Object(support\Request), Object(Closure))
#5 D:\work\hotel\all\itinerary-api\base\middleware\AccessControlTest.php(13): Webman\App::Webman\{closure}(Object(support\Request))
#6 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\App.php(352): base\middleware\AccessControlTest->process(Object(support\Request), Object(Closure))
#7 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\App.php(663): Webman\App::Webman\{closure}(Object(support\Request))
#8 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\App.php(158): Webman\App::findRoute(Object(Workerman\Connection\TcpConnection), '/system-config', 'POST/system-con...', Object(support\Request), 200)
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Connection\TcpConnection.php(750): Webman\App->onMessage(Object(Workerman\Connection\TcpConnection), Object(support\Request))
#10 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Events\Select.php(406): Workerman\Connection\TcpConnection->baseRead(Resource id #374)
#11 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1613): Workerman\Events\Select->run()
#12 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#14 D:\work\hotel\all\itinerary-api\runtime\windows\start_webman.php(33): Workerman\Worker::runAll()
#15 {main} [] []
[2025-08-11 17:25:28] default.ERROR: 127.0.0.1 GET 127.0.0.1:8899/api/itinerary/detail/16
think\db\exception\PDOException: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'day' in 'order clause' in D:\work\hotel\all\itinerary-api\vendor\topthink\think-orm\src\db\PDOConnection.php:861
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\topthink\think-orm\src\db\PDOConnection.php(768): think\db\PDOConnection->getPDOStatement('SELECT * FROM `...', Array, false, false)
#1 D:\work\hotel\all\itinerary-api\vendor\topthink\think-orm\src\db\PDOConnection.php(990): think\db\PDOConnection->pdoQuery(Object(think\db\Query), 'SELECT * FROM `...')
#2 D:\work\hotel\all\itinerary-api\vendor\topthink\think-orm\src\db\BaseQuery.php(1523): think\db\PDOConnection->select(Object(think\db\Query))
#3 D:\work\hotel\all\itinerary-api\vendor\topthink\think-orm\src\db\Query.php(615): think\db\BaseQuery->select()
#4 D:\work\hotel\all\itinerary-api\vendor\topthink\think-orm\src\model\relation\HasMany.php(236): think\db\Query->lazy()
#5 D:\work\hotel\all\itinerary-api\vendor\topthink\think-orm\src\model\relation\HasMany.php(94): think\model\relation\HasMany->eagerlyOneToMany(Array, Array, NULL, Array, true)
#6 D:\work\hotel\all\itinerary-api\vendor\topthink\think-orm\src\model\concern\RelationShip.php(448): think\model\relation\HasMany->eagerlyResultSet(Array, 'schedules', Array, NULL, Array, false)
#7 D:\work\hotel\all\itinerary-api\vendor\topthink\think-orm\src\db\concern\ModelRelationQuery.php(640): think\Model->eagerlyResultSet(Array, Array, Array, false, false)
#8 D:\work\hotel\all\itinerary-api\vendor\topthink\think-orm\src\db\BaseQuery.php(1533): think\db\BaseQuery->resultSetToModelCollection(Array)
#9 D:\work\hotel\all\itinerary-api\vendor\topthink\think-orm\src\db\Query.php(617): think\db\BaseQuery->select()
#10 D:\work\hotel\all\itinerary-api\vendor\topthink\think-orm\src\model\relation\HasMany.php(236): think\db\Query->lazy()
#11 D:\work\hotel\all\itinerary-api\vendor\topthink\think-orm\src\model\relation\HasMany.php(127): think\model\relation\HasMany->eagerlyOneToMany(Array, Array, NULL, Array)
#12 D:\work\hotel\all\itinerary-api\vendor\topthink\think-orm\src\model\concern\RelationShip.php(503): think\model\relation\HasMany->eagerlyResult(Object(app\model\ItineraryModel), 'scheduleDays', Array, NULL, Array, false)
#13 D:\work\hotel\all\itinerary-api\vendor\topthink\think-orm\src\db\concern\ModelRelationQuery.php(693): think\Model->eagerlyResult(Object(app\model\ItineraryModel), Array, Array, false, false)
#14 D:\work\hotel\all\itinerary-api\vendor\topthink\think-orm\src\db\BaseQuery.php(1575): think\db\BaseQuery->resultToModel(Object(app\model\ItineraryModel))
#15 D:\work\hotel\all\itinerary-api\app\api\controller\Itinerary.php(145): think\db\BaseQuery->find()
#16 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\App.php(336): app\api\controller\Itinerary->detail(Object(support\Request), '16')
#17 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\App.php(359): Webman\App::Webman\{closure}(Object(support\Request), '16')
#18 D:\work\hotel\all\itinerary-api\base\middleware\AcceptLanguage.php(81): Webman\App::Webman\{closure}(Object(support\Request))
#19 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\App.php(352): base\middleware\AcceptLanguage->process(Object(support\Request), Object(Closure))
#20 D:\work\hotel\all\itinerary-api\base\middleware\AccessControlTest.php(13): Webman\App::Webman\{closure}(Object(support\Request))
#21 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\App.php(352): base\middleware\AccessControlTest->process(Object(support\Request), Object(Closure))
#22 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\App.php(663): Webman\App::Webman\{closure}(Object(support\Request))
#23 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\App.php(158): Webman\App::findRoute(Object(Workerman\Connection\TcpConnection), '/api/itinerary/...', 'GET/api/itinera...', Object(support\Request), 200)
#24 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Connection\TcpConnection.php(750): Webman\App->onMessage(Object(Workerman\Connection\TcpConnection), Object(support\Request))
#25 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Events\Select.php(406): Workerman\Connection\TcpConnection->baseRead(Resource id #333)
#26 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1613): Workerman\Events\Select->run()
#27 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#28 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#29 D:\work\hotel\all\itinerary-api\runtime\windows\start_webman.php(33): Workerman\Worker::runAll()
#30 {main} [] []
[2025-08-11 17:41:10] default.ERROR: 更新配置失败 {"error":"Undefined variable $key","file":"D:\\work\\hotel\\all\\itinerary-api\\app\\service\\SystemConfigService.php","line":191,"trace":"#0 D:\\work\\hotel\\all\\itinerary-api\\app\\service\\SystemConfigService.php(191): {closure}(2, 'Undefined varia...', 'D:\\\\work\\\\hotel\\\\a...', 191)
#1 D:\\work\\hotel\\all\\itinerary-api\\app\\admin\\controller\\SystemConfigController.php(187): app\\service\\SystemConfigService->updateConfig('15', Array, NULL)
#2 D:\\work\\hotel\\all\\itinerary-api\\vendor\\workerman\\webman-framework\\src\\App.php(336): app\\admin\\controller\\SystemConfigController->update(Object(support\\Request), '15')
#3 D:\\work\\hotel\\all\\itinerary-api\\vendor\\workerman\\webman-framework\\src\\App.php(359): Webman\\App::Webman\\{closure}(Object(support\\Request), '15')
#4 D:\\work\\hotel\\all\\itinerary-api\\base\\middleware\\AcceptLanguage.php(81): Webman\\App::Webman\\{closure}(Object(support\\Request))
#5 D:\\work\\hotel\\all\\itinerary-api\\vendor\\workerman\\webman-framework\\src\\App.php(352): base\\middleware\\AcceptLanguage->process(Object(support\\Request), Object(Closure))
#6 D:\\work\\hotel\\all\\itinerary-api\\base\\middleware\\AccessControlTest.php(13): Webman\\App::Webman\\{closure}(Object(support\\Request))
#7 D:\\work\\hotel\\all\\itinerary-api\\vendor\\workerman\\webman-framework\\src\\App.php(352): base\\middleware\\AccessControlTest->process(Object(support\\Request), Object(Closure))
#8 D:\\work\\hotel\\all\\itinerary-api\\vendor\\workerman\\webman-framework\\src\\App.php(663): Webman\\App::Webman\\{closure}(Object(support\\Request))
#9 D:\\work\\hotel\\all\\itinerary-api\\vendor\\workerman\\webman-framework\\src\\App.php(158): Webman\\App::findRoute(Object(Workerman\\Connection\\TcpConnection), '/system-config/...', 'PUT/system-conf...', Object(support\\Request), 200)
#10 D:\\work\\hotel\\all\\itinerary-api\\vendor\\workerman\\workerman\\src\\Connection\\TcpConnection.php(750): Webman\\App->onMessage(Object(Workerman\\Connection\\TcpConnection), Object(support\\Request))
#11 D:\\work\\hotel\\all\\itinerary-api\\vendor\\workerman\\workerman\\src\\Events\\Select.php(406): Workerman\\Connection\\TcpConnection->baseRead(Resource id #333)
#12 D:\\work\\hotel\\all\\itinerary-api\\vendor\\workerman\\workerman\\src\\Worker.php(1613): Workerman\\Events\\Select->run()
#13 D:\\work\\hotel\\all\\itinerary-api\\vendor\\workerman\\workerman\\src\\Worker.php(1528): Workerman\\Worker::forkWorkersForWindows()
#14 D:\\work\\hotel\\all\\itinerary-api\\vendor\\workerman\\workerman\\src\\Worker.php(594): Workerman\\Worker::forkWorkers()
#15 D:\\work\\hotel\\all\\itinerary-api\\runtime\\windows\\start_webman.php(33): Workerman\\Worker::runAll()
#16 {main}"} []
[2025-08-11 17:42:03] default.INFO: 系统配置变更 {"config_key":{"app\\model\\SystemConfig":{"id":"15","description":"111","configKey":"test","configValue":"111","configType":"string","configGroup":"general","groupId":1,"isPublic":0,"isEditable":1,"isEncrypted":0,"validationRules":null,"defaultValue":null,"sortOrder":0,"createdAt":"2025-08-11 17:14:21","updatedAt":"2025-08-11 17:42:03","updatedBy":null,"configName":"dw"}},"action":"update","old_value":"1","new_value":"111","user_id":null,"timestamp":"2025-08-11 17:42:03"} []
[2025-08-11 17:42:34] default.ERROR: 127.0.0.1 POST localhost:8899/system-config
TypeError: error(): Argument #1 ($code) must be of type int, string given, called in D:\work\hotel\all\itinerary-api\app\admin\controller\SystemConfigController.php on line 146 and defined in D:\work\hotel\all\itinerary-api\app\functions.php:140
Stack trace:
#0 D:\work\hotel\all\itinerary-api\app\admin\controller\SystemConfigController.php(146): error('\xE6\x97\xA0\xE6\x95\x88\xE7\x9A\x84\xE9\x85\x8D\xE7\xBD\xAE...')
#1 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\App.php(336): app\admin\controller\SystemConfigController->store(Object(support\Request))
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\App.php(359): Webman\App::Webman\{closure}(Object(support\Request))
#3 D:\work\hotel\all\itinerary-api\base\middleware\AcceptLanguage.php(81): Webman\App::Webman\{closure}(Object(support\Request))
#4 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\App.php(352): base\middleware\AcceptLanguage->process(Object(support\Request), Object(Closure))
#5 D:\work\hotel\all\itinerary-api\base\middleware\AccessControlTest.php(13): Webman\App::Webman\{closure}(Object(support\Request))
#6 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\App.php(352): base\middleware\AccessControlTest->process(Object(support\Request), Object(Closure))
#7 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\App.php(663): Webman\App::Webman\{closure}(Object(support\Request))
#8 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\App.php(158): Webman\App::findRoute(Object(Workerman\Connection\TcpConnection), '/system-config', 'POST/system-con...', Object(support\Request), 200)
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Connection\TcpConnection.php(750): Webman\App->onMessage(Object(Workerman\Connection\TcpConnection), Object(support\Request))
#10 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Events\Select.php(406): Workerman\Connection\TcpConnection->baseRead(Resource id #428)
#11 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1613): Workerman\Events\Select->run()
#12 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#14 D:\work\hotel\all\itinerary-api\runtime\windows\start_webman.php(33): Workerman\Worker::runAll()
#15 {main} [] []
[2025-08-11 17:48:05] default.INFO: 系统配置变更 {"config_key":{"app\\model\\SystemConfig":{"id":"15","description":"","configKey":"test","configValue":"","configType":"string","configGroup":"general","groupId":1,"isPublic":0,"isEditable":1,"isEncrypted":0,"validationRules":null,"defaultValue":null,"sortOrder":0,"createdAt":"2025-08-11 17:14:21","updatedAt":"2025-08-11 17:48:05","updatedBy":null,"configName":"111"}},"action":"update","old_value":"111","new_value":"","user_id":null,"timestamp":"2025-08-11 17:48:05"} []
[2025-08-11 17:49:37] default.INFO: 系统配置变更 {"config_key":{"app\\model\\SystemConfig":{"id":"15","description":"","configKey":"test","configValue":"222","configType":"string","configName":"配置名称","groupId":1,"isPublic":0,"isEditable":1,"isEncrypted":0,"validationRules":null,"defaultValue":null,"sortOrder":0,"createdAt":"2025-08-11 17:14:21","updatedAt":"2025-08-11 17:49:37","updatedBy":null}},"action":"update","old_value":"","new_value":"222","user_id":null,"timestamp":"2025-08-11 17:49:37"} []
[2025-08-11 17:51:58] default.INFO: 系统配置变更 {"config_key":{"app\\model\\SystemConfig":{"id":"15","description":"","configKey":"test","configValue":"2221","configType":"string","configName":"配置名称","groupId":1,"isPublic":0,"isEditable":1,"isEncrypted":0,"validationRules":null,"defaultValue":null,"sortOrder":0,"createdAt":"2025-08-11 17:14:21","updatedAt":"2025-08-11 17:51:58","updatedBy":null}},"action":"update","old_value":"222","new_value":"2221","user_id":null,"timestamp":"2025-08-11 17:51:58"} []
[2025-08-11 17:56:23] default.INFO: 系统配置变更 {"config_key":{"app\\model\\SystemConfig":{"id":"15","description":"","configKey":"test","configValue":"{“a”:1}","configType":"json","configName":"配置名称","groupId":1,"isPublic":0,"isEditable":1,"isEncrypted":0,"validationRules":null,"defaultValue":null,"sortOrder":0,"createdAt":"2025-08-11 17:14:21","updatedAt":"2025-08-11 17:56:23","updatedBy":null}},"action":"update","old_value":"2221","new_value":"{“a”:1}","user_id":null,"timestamp":"2025-08-11 17:56:23"} []
[2025-08-11 17:56:53] default.INFO: 系统配置变更 {"config_key":{"app\\model\\SystemConfig":{"id":"15","description":"","configKey":"test","configValue":"1","configType":"boolean","configName":"配置名称","groupId":1,"isPublic":0,"isEditable":1,"isEncrypted":0,"validationRules":null,"defaultValue":null,"sortOrder":0,"createdAt":"2025-08-11 17:14:21","updatedAt":"2025-08-11 17:56:53","updatedBy":null}},"action":"update","old_value":"{“a”:1}","new_value":true,"user_id":null,"timestamp":"2025-08-11 17:56:53"} []
[2025-08-11 17:57:17] default.INFO: 系统配置变更 {"config_key":{"app\\model\\SystemConfig":{"id":"15","description":"","configKey":"test","configValue":"1","configType":"text","configName":"配置名称","groupId":1,"isPublic":0,"isEditable":1,"isEncrypted":0,"validationRules":null,"defaultValue":null,"sortOrder":0,"createdAt":"2025-08-11 17:14:21","updatedAt":"2025-08-11 17:57:17","updatedBy":null}},"action":"update","old_value":"1","new_value":"1","user_id":null,"timestamp":"2025-08-11 17:57:17"} []
[2025-08-11 17:57:22] default.INFO: 系统配置变更 {"config_key":{"app\\model\\SystemConfig":{"id":"15","description":"","configKey":"test","configValue":"dwdwd","configType":"text","configName":"配置名称","groupId":1,"isPublic":0,"isEditable":1,"isEncrypted":0,"validationRules":null,"defaultValue":null,"sortOrder":0,"createdAt":"2025-08-11 17:14:21","updatedAt":"2025-08-11 17:57:22","updatedBy":null}},"action":"update","old_value":"1","new_value":"dwdwd","user_id":null,"timestamp":"2025-08-11 17:57:22"} []
[2025-08-11 17:58:54] default.INFO: 系统配置变更 {"config_key":"16","action":"delete","old_value":null,"new_value":null,"user_id":null,"timestamp":"2025-08-11 17:58:54"} []
