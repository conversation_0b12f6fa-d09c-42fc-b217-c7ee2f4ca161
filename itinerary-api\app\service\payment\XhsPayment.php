<?php

declare(strict_types=1);

namespace app\service\payment;

use app\service\XiaoHongShuClient;
use base\exception\BaseException;

class XhsPayment implements PaymentInterface
{
    protected XiaoHongShuClient $client;

    public function __construct()
    {
        $this->client = new XiaoHongShuClient();
    }

    public function pay(array $payload): array
    {
        $defaultImage = rtrim((string)config('app.base_url'), '/') . '/public/avatar.png';
        $data = [
            'out_order_id' => $payload['out_trade_no'],
            'open_id' => $payload['platform_id'],
            'path' => '', // 小红书详情页
            'create_time' => time(),
            'expired_time' => time() + 600,
            'order_price' => (float)bcmul($payload['total_amount'], '100'),
            'product_details' => [
                [
                    'name' => $payload['itinerary']['title'] ?? $payload['subject'],
                    'desc' => $payload['subject'],
                    'category_id' => XiaoHongShuClient::XHS_CATEGORY_ID,
                    'image' => $payload['itinerary']['cover_image'] ?? $defaultImage,
                ]
            ]
        ];
        // 根据小红书支付文档，组织请求参数，
        $res = $this->client->sendRequest(XiaoHongShuClient::API_GENERAL_GPAY, $data, 'POST');
        if (!isset($res['success']) || $res['success'] !== true) {
            throw new BaseException($res['msg']);
        }
        return $res['data'];
    }

    public function refund(array $payload): array
    {
        return [ 'success' => true, 'channel' => 'xhs' ];
    }

    public function query(array $payload): array
    {
        return [ 'trade_state' => 'SUCCESS', 'channel' => 'xhs' ];
    }

    public function notify(array $params): array
    {
        return [ 'success' => true, 'out_trade_no' => $params['out_trade_no'] ?? '', 'trade_no' => $params['transaction_id'] ?? '', 'raw' => $params ];
    }

    public function cancel(array $payload): array
    {
        return [ 'success' => true, 'channel' => 'xhs' ];
    }
}

