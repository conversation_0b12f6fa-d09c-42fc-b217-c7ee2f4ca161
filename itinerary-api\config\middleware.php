<?php
/**
 * This file is part of webman.
 *
 * Licensed under The MIT License
 * For full copyright and license information, please see the MIT-LICENSE.txt
 * Redistributions of files must retain the above copyright notice.
 *
 * <AUTHOR>
 * @copyright walkor<<EMAIL>>
 * @link      http://www.workerman.net/
 * @license   http://www.opensource.org/licenses/mit-license.php MIT License
 */

use base\auth\middleware\Auth;
use base\middleware\AcceptLanguage;
use base\middleware\AccessControlTest;
use app\middleware\UserAuth;

return [
    '' => [
        AccessControlTest::class,
        AcceptLanguage::class,
//        Auth::class,
    ],
    // Register user auth middleware for specific paths if needed
    // 'api/user/protected' => [
    //    UserAuth::class
    // ],
];