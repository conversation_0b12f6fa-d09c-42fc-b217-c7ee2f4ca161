<?php

declare(strict_types=1);

namespace base\helper;

/**
 * Password helper
 */
class Password
{
    /**
     * Hash password
     *
     * @param string $password
     * @return string
     */
    public static function hash(string $password): string
    {
        return password_hash($password, PASSWORD_DEFAULT);
    }

    /**
     * Verify password
     *
     * @param string $password
     * @param string $hash
     * @return bool
     */
    public static function verify(string $password, string $hash): bool
    {
        return password_verify($password, $hash);
    }

    /**
     * Check if password needs rehash
     *
     * @param string $hash
     * @return bool
     */
    public static function needsRehash(string $hash): bool
    {
        return password_needs_rehash($hash, PASSWORD_DEFAULT);
    }

    /**
     * Generate random password
     *
     * @param int $length
     * @return string
     * @throws \Exception
     */
    public static function generate(int $length = 8): string
    {
        return bin2hex(random_bytes($length / 2));
    }
}