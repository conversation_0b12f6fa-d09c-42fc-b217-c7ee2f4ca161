-- 行程主表
CREATE TABLE `itinerary`
(
    `id`                     bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `title`                  varchar(100)   NOT NULL COMMENT '线路名称',
    `price`                  decimal(10, 2) NOT NULL COMMENT '价格',
    `original_price`         decimal(10, 2) DEFAULT NULL COMMENT '原价',
    `days`                   int(11) NOT NULL COMMENT '行程天数',
    `nights`                 int(11) DEFAULT '0' COMMENT '行程夜数',
    `destination`            varchar(50)    NOT NULL COMMENT '目的地',
    `departure_city`         varchar(50)    NOT NULL COMMENT '始发地',
    `product_type`           varchar(20)    DEFAULT NULL COMMENT '产品类型,跟团游、自由行等',
    `cover_image`            varchar(255)   NOT NULL COMMENT '封面图',
    `images`                 json           DEFAULT NULL COMMENT '图片集',
    `image_description`      json           DEFAULT NULL COMMENT '图片描述',
    `description`            text           NOT NULL COMMENT '线路描述',
    `features`               json           DEFAULT NULL COMMENT '线路特色',
    `STATUS`                 enum('online','offline') DEFAULT 'offline' COMMENT '上架状态',
    `travel_mode`            varchar(20)    DEFAULT NULL COMMENT '旅行方式(休闲度假,人文历史等)',
    `group_type`             varchar(20)    DEFAULT NULL COMMENT '成团类型(拼团,包团)',
    `min_group_size`         int(11) DEFAULT '1' COMMENT '最小出行人数',
    `max_group_size`         int(11) DEFAULT '10' COMMENT '最大出行人数',
    `collect_type`           varchar(20)    DEFAULT '按人收费' COMMENT '收费方式',
    `deposit`                decimal(10, 2) DEFAULT '0.00' COMMENT '预付定金',
    `promotion_price`        decimal(10, 2) DEFAULT '0.00' COMMENT '促销价格',
    `category_id`            varchar(36)    DEFAULT NULL COMMENT '商品品类ID',
    `category_path`          json           DEFAULT NULL COMMENT '品类路径',
    `valid_from`             date           DEFAULT NULL COMMENT '预约有效期开始日期',
    `valid_to`               date           DEFAULT NULL COMMENT '预约有效期结束日期',
    `is_long_term`           tinyint(1) DEFAULT '0' COMMENT '是否长期有效',
    `single_room_surcharge`  decimal(10, 2) DEFAULT '0.00' COMMENT '单房差',
    `room_upgrade_surcharge` decimal(10, 2) DEFAULT '0.00' COMMENT '升级房型费用',

    -- 交易规则相关字段
    `consumable_date_type`   enum('specificDates','specificDays') DEFAULT NULL COMMENT '顾客可消费日期类型',
    `consumable_days`        int(11) DEFAULT NULL COMMENT '指定天数',
    `consumable_dates`       json DEFAULT NULL COMMENT '指定日期',

    -- 限购规则
    `purchase_limit_single`  tinyint(1) DEFAULT NULL COMMENT '限制单笔购买数量',
    `purchase_limit_per_order` int(11) DEFAULT NULL COMMENT '每单限购数量',
    `purchase_limit_rules`   tinyint(1) DEFAULT NULL COMMENT '限制购买规则',
    `purchase_limit_details` varchar(255) DEFAULT NULL COMMENT '购买限制详情',

    -- 预约规则
    `earliest_booking_days`  int(11) DEFAULT NULL COMMENT '最早可预约时间(可预约多少天内的库存)',
    `advance_booking_required` tinyint(1) DEFAULT NULL COMMENT '需提前预约',
    `advance_booking_range`  enum('oneDayBefore','sameDay') DEFAULT NULL COMMENT '提前范围',
    `advance_booking_time`   int(11) DEFAULT NULL COMMENT '提前时间',
    `advance_booking_time_unit` enum('days','hours') DEFAULT NULL COMMENT '提前时间单位',

    -- 取消规则
    `has_no_cancellation_after_appointment` tinyint(1) DEFAULT NULL COMMENT '取消选项',
    `refund_before_appointment` tinyint(1) DEFAULT NULL COMMENT '未预约随时退款',
    `auto_refund_after_expiration` tinyint(1) DEFAULT NULL COMMENT '过期未预约自动全额退款',
    `has_breach_of_contract_terms` tinyint(1) DEFAULT NULL COMMENT '预约成功后若双方违约，按如下条款支付违约金',
    `breach_of_contract_terms` json DEFAULT NULL COMMENT '违约金条款，数组结构，包含buyerPenalty、days、sellerPenalty',
    `breach_of_contract_base` enum('amountTotal','amountUnconsumed') DEFAULT NULL COMMENT '违约金基数',

    `create_time`            int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
    `update_time`            int(10) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
    `delete_time`            int(10) unsigned NOT NULL DEFAULT '0' COMMENT '删除时间',
    PRIMARY KEY (`id`),
    KEY                      `idx_status` (`STATUS`),
    KEY                      `idx_destination` (`destination`),
    KEY                      `idx_departure_city` (`departure_city`),
    KEY                      `idx_category_id` (`category_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='旅行线路表';

CREATE TABLE `itinerary_day`
(
    `id`            bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `itinerary_id`  varchar(36)  NOT NULL COMMENT '关联的行程ID',
    `day`           int(11) NOT NULL COMMENT '第几天',
    `title`         varchar(100) NOT NULL COMMENT '行程标题',
    `description`   text COMMENT '行程描述',
    `activities`    json         DEFAULT NULL COMMENT '活动内容',
    `meeting_point` varchar(100) DEFAULT NULL COMMENT '集合点',
    `meeting_time`  time         DEFAULT NULL COMMENT '集合时间',
    `create_time`   int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
    `update_time`   int(10) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
    `delete_time`   int(10) unsigned NOT NULL DEFAULT '0' COMMENT '删除时间',
    PRIMARY KEY (`id`),
    KEY             `idx_itinerary_day` (`itinerary_id`,`day`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COMMENT='旅行行程单天安排表';
-- 行程安排表
CREATE TABLE itinerary_schedule
(
    `id`                       BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    itinerary_id               VARCHAR(36)  NOT NULL COMMENT '关联的行程ID',
    `itinerary_day_id` varchar(36) NOT NULL COMMENT '关联的单天行程ID',
    title                      VARCHAR(100) NOT NULL COMMENT '行程标题',
    description                TEXT COMMENT '行程描述',
    activities                 JSON COMMENT '活动内容',
    meals                      JSON COMMENT '餐食信息',
    accommodation              VARCHAR(100) COMMENT '住宿信息',
    images                     JSON COMMENT '图片集',
    type                       ENUM('meal', 'transport', 'attraction', 'accommodation', 'freeActivity') COMMENT '行程类型',
    start_time                 TIME COMMENT '开始时间',
    meal_type                  JSON COMMENT '餐食类型',
    adult_meal_included        VARCHAR(20) COMMENT '成人是否含餐',
    child_meal_included        VARCHAR(20) COMMENT '儿童是否含餐',
    meal_duration_hours        INT DEFAULT 0 COMMENT '用餐时长-小时',
    meal_duration_minutes      INT DEFAULT 0 COMMENT '用餐时长-分钟',
    meal_price                 DECIMAL(10, 2) COMMENT '餐标价格',
    transport_type             VARCHAR(20) COMMENT '交通类型',
    flight_number              VARCHAR(20) COMMENT '航班号',
    departure_city             VARCHAR(50) COMMENT '出发城市',
    arrival_city               VARCHAR(50) COMMENT '抵达城市',
    estimated_duration_hours   INT DEFAULT 0 COMMENT '预计用时-小时',
    estimated_duration_minutes INT DEFAULT 0 COMMENT '预计用时-分钟',
    stay_type                  VARCHAR(20) COMMENT '住宿方式',
    hotel_name                 VARCHAR(100) COMMENT '酒店名称',
    activity_duration_hours    INT DEFAULT 0 COMMENT '活动时长-小时',
    activity_duration_minutes  INT DEFAULT 0 COMMENT '活动时长-分钟',
    remark                     TEXT COMMENT '补充说明',
    `create_time`              int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
    `update_time`              int(10) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
    `delete_time`              int(10) unsigned NOT NULL DEFAULT '0' COMMENT '删除时间',
    PRIMARY KEY (`id`),
    KEY `idx_itinerary_day` (`itinerary_id`,`itinerary_day_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='旅行行程安排表';

-- 商品分类表
CREATE TABLE product_category
(
    category_id        BIGINT PRIMARY KEY COMMENT '分类ID',
    parent_id          BIGINT               DEFAULT 0 COMMENT '父级分类ID，0表示顶级分类',
    category_name      VARCHAR(50) NOT NULL COMMENT '分类名称',
    category_level     INT         NOT NULL DEFAULT 1 COMMENT '分类层级，1为一级分类',
    access_status      TINYINT              DEFAULT 1 COMMENT '状态：1-可用，2-受限',
    category_discard   BOOLEAN              DEFAULT FALSE COMMENT '是否废弃',
    allow_access_scope TEXT COMMENT '允许访问的产品范围',
    no_access_scope    TEXT COMMENT '不允许访问的产品范围',
    detail_url         VARCHAR(255) COMMENT '详情URL',
    price_limit        DECIMAL(10, 2) COMMENT '价格限制',
    sort_order         INT                  DEFAULT 0 COMMENT '排序顺序',
    `create_time`      int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
    `update_time`      int(10) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
    `delete_time`      int(10) unsigned NOT NULL DEFAULT '0' COMMENT '删除时间',
    INDEX              idx_parent_id (parent_id),
    INDEX              idx_category_name (category_name),
    INDEX              idx_access_status (access_status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品分类表';


-- 批量插入一级分类数据
INSERT INTO product_category (category_id, parent_id, category_name, category_level, access_status, category_discard,
                              sort_order)
VALUES (18000000, 0, '游玩', 1, 2, false, 1),
       (8000000, 0, '住宿', 1, 2, false, 2),
       (32000000, 0, '度假旅游服务', 1, 1, false, 3),
       (31000000, 0, '交通出行', 1, 2, false, 4);

-- 批量插入二级分类数据
INSERT INTO product_category (category_id, parent_id, category_name, category_level, access_status, category_discard,
                              sort_order)
VALUES
-- 游玩分类
(18003000, 18000000, '游玩项目', 2, 1, false, 1),
(18004000, 18000000, '景点票券', 2, 2, false, 2),
(18011000, 18000000, '境外票务', 2, 1, false, 3),
(18012000, 18000000, '港澳票务', 2, 1, false, 4),
(18009000, 18000000, '露营', 2, 2, false, 5),

-- 住宿分类
(8001000, 8000000, '酒店宾馆', 2, 1, false, 1),
(8002000, 8000000, '客栈民宿', 2, 1, false, 2),
(8003000, 8000000, '其他住宿', 2, 1, false, 3),
(8004000, 8000000, '境外酒店', 2, 1, false, 4),
(8005000, 8000000, '境内酒景套餐', 2, 1, false, 5),
(8006000, 8000000, '境外酒景套餐', 2, 1, false, 6),

-- 度假旅游服务
(32002000, 32000000, '出境行程游', 2, 1, false, 1),
(32003000, 32000000, '出境目的地玩乐', 2, 1, false, 2),
(32004000, 32000000, '邮轮', 2, 1, false, 3),
(32006000, 32000000, '境内行程游', 2, 1, false, 4),
(32007000, 32000000, '境内目的地玩乐', 2, 1, false, 5),
(32005000, 32000000, '签证', 2, 2, false, 6),

-- 交通出行
(31001000, 31000000, '机票', 2, 2, false, 1),
(31002000, 31000000, '增值服务', 2, 2, false, 2),
(31003000, 31000000, '机票打包商品', 2, 2, false, 3),
(31004000, 31000000, '汽车票', 2, 2, false, 4);

-- 插入部分三级分类数据 (游玩项目类别)
INSERT INTO product_category (category_id, parent_id, category_name, category_level, access_status, category_discard,
                              allow_access_scope, sort_order)
VALUES (4015004, 18003000, '滑雪场', 3, 1, false, '单滑雪票', 1),
       (18003001, 18003000, '水上体验', 3, 1, false,
        '水上或水下进行的单个活动项目/门票（游泳、潜水、冲浪、漂流、划船、皮划艇、帆船出海、游艇、摩托艇、水上飞人、海上飞鱼滑翔/滑翔伞/热气球/三角翼滑板/滑旱冰、沙滩车、水上乐园、溪降、玻璃水滑道等',
        2),
       (18003002, 18003000, '城市/高空观光', 3, 1, false,
        '攀岩,高空滑索、高空跳伞,翼装飞行,蹦极,跳伞,滑翔伞,热气球,直升机观光体验,高空秋千,高空玻璃栈道等', 3),
       (18003004, 18003000, '温泉', 3, 1, false, '单温泉门票', 4),
       (18003007, 18003000, '国内短途游轮', 3, 1, false, '', 5),
       (18003008, 18003000, '国内长途游轮', 3, 1, false, '仅可发布国内长途游轮商品', 6);

-- 插入部分三级分类数据 (景点票券类别)
INSERT INTO product_category (category_id, parent_id, category_name, category_level, access_status, category_discard,
                              allow_access_scope, no_access_scope, sort_order)
VALUES (18004001, 18004000, '单景点门票', 3, 1, false,
        '单景点门票或单项目票（自然景观、人文古迹、古镇/古村、植物园、动物园、生态园、冰雪乐园、游乐园、水族馆、影视基地、博物馆、会展中心、纪念馆、科技馆、蜡像馆、美术馆、天文馆、展览馆、公园广场、地标建筑、观光街区）',
        '多项目的组合品（如门票+园内餐饮/讲解/住宿、门票A+门票B）；"游玩项目"二级类目下的商品（水上体验、温泉、滑雪场、高空体验等门票或项目票）',
        1),
       (18004002, 18004000, '园内餐饮', 3, 1, false, '景区内单餐饮类商品', '', 2),
       (18004003, 18004000, '园内交通', 3, 1, false,
        '景区内通勤/摆渡的单交通类商品（例如园内的索道、小火车、小游船、小电车票、泰山山脚-景区大门的摆渡车等）', '', 3),
       (18004007, 18004000, '多景点联票', 3, 1, false, '多个景点门票的组合类商品',
        '非景点门票的商品（例如：景点A+景点B+园内餐饮）', 7),
       (18004008, 18004000, '园内套票', 3, 1, false,
        '同一景点内的门票+不同服务/游玩项目的组合、不同服务+游玩项目的组合（门票+园内演出/餐饮/纪念品等）', '', 8);

-- 插入部分三级分类数据 (度假旅游服务类别)
INSERT INTO product_category (category_id, parent_id, category_name, category_level, access_status, category_discard,
                              allow_access_scope, sort_order)
VALUES (32002001, 32002000, '出境自由行', 3, 1, false, '出发地到目的地交通+选填（境内外餐饮/住宿/景点/其他服务）', 1),
       (32002002, 32002000, '出境跟团游', 3, 1, false,
        '出发地到目的地交通+司机/导游+行程安排服务+选填（境内外餐饮/住宿/景点/其他服务）', 2),
       (32006001, 32006000, '境内自由行', 3, 1, false, '出发地到目的地交通+选填（餐饮/住宿/景点/其他服务）', 1),
       (32006002, 32006000, '境内跟团游', 3, 1, false,
        '出发地到目的地交通+司机/导游+行程安排服务+选填（餐饮/住宿/景点/其他服务）', 2),
       (32007001, 32007000, '境内一日游', 3, 1, false, '行程天数≤1天，司导服务/行程安排+景点', 1),
       (32007002, 32007000, '境内二日游', 3, 1, false, '行程天数>1天&≤2天，司导服务/行程安排+景点', 2),
       (32007003, 32007000, '境内多日游', 3, 1, false, '行程天数>2天，司导服务/行程安排+景点', 3);

-- 插入部分三级分类数据 (住宿类别)
INSERT INTO product_category (category_id, parent_id, category_name, category_level, access_status, category_discard,
                              allow_access_scope, price_limit, sort_order)
VALUES (8001001, 8001000, '经济型酒店', 3, 1, false, '住宿+选填（酒店内餐饮/酒店内权益或硬件服务）', NULL, 1),
       (8001002, 8001000, '舒适型酒店', 3, 1, false, '住宿+选填（酒店内餐饮/酒店内权益或硬件服务）', NULL, 2),
       (8001003, 8001000, '高档型酒店', 3, 1, false, '住宿+选填（酒店内餐饮/酒店内权益或硬件服务）', 500000.00, 3),
       (8001004, 8001000, '豪华型酒店', 3, 1, false, '住宿+选填（酒店内餐饮/酒店内权益或硬件服务）', 500000.00, 4),
       (8002001, 8002000, '客栈民宿', 3, 1, false, '住宿+选填（酒店内餐饮/酒店内权益或硬件服务）', 500000.00, 1),
       (8005001, 8005000, '境内酒店套餐', 3, 1, false,
        '酒店住宿+景点门票/游玩项目+选填（酒店内餐饮/酒店内权益或硬件服务）', NULL, 1);