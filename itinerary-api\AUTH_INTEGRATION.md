# IAM 鉴权系统集成文档

本文档说明如何在 itinerary-api 中集成 IAM 鉴权系统，实现统一的身份认证和权限管理。

## 系统架构

```
┌─────────────────┐    JWT Token    ┌─────────────────┐
│   itinerary-api │ ──────────────→ │     iam-api     │
│                 │                 │                 │
│ 1. 获取Token    │                 │ 1. 验证Token    │
│ 2. 权限注解     │ ←────────────── │ 2. 权限检查     │
│ 3. 中间件鉴权   │   用户信息+权限   │ 3. 返回结果     │
└─────────────────┘                 └─────────────────┘
```

## 功能特性

### 1. JWT Token 鉴权
- 支持从 Authorization 头获取 JWT token
- 自动验证 token 有效性和过期时间
- 支持 token 刷新机制

### 2. 权限注解系统
- 使用 PHP 8 Attribute 语法定义权限
- 支持方法级别的权限控制
- 支持多权限组合

### 3. 统一鉴权中间件
- 自动拦截需要鉴权的请求
- 优先使用 IAM API 鉴权，失败时回退到 SSO
- 自动注入用户信息到请求上下文

## 配置说明

### 环境变量配置

在 `.env` 文件中配置以下参数：

```env
# IAM API 配置
AUTH.IAM_API_DOMAIN=http://localhost:10000
AUTH.SSO_DOMAIN=http://sso.sznits.com
AUTH.SSO_APPID=your_app_id
AUTH.APP_SECRET=your_app_secret
```

### 依赖注入配置

在 `config/dependence.php` 中已配置：

```php
'iam_api_domain' => env('AUTH.IAM_API_DOMAIN', 'http://localhost:10000'),
'sso_domain' => env('AUTH.SSO_DOMAIN', 'http://sso.sznits.com'),
'appid' => env('AUTH.SSO_APPID'),
'app_secret' => env('AUTH.APP_SECRET'),
```

## 使用方法

### 1. 在控制器中使用权限注解

```php
<?php

namespace app\controller;

use base\auth\Auth;
use base\auth\UserInfo;
use support\Request;
use support\Response;

class ExampleController
{
    /**
     * 需要读取权限的接口
     */
    #[Auth('itinerary:example:read', '示例读取权限')]
    public function read(Request $request): Response
    {
        // 获取当前用户信息
        $userInfo = UserInfo::get();
        
        return response()->json([
            'code' => 200,
            'data' => [
                'user_id' => $userInfo['userId'],
                'username' => $userInfo['username'],
                'message' => '读取成功'
            ]
        ]);
    }

    /**
     * 需要多个权限的接口
     */
    #[Auth('itinerary:example:read', '示例读取权限')]
    #[Auth('itinerary:example:write', '示例写入权限')]
    public function update(Request $request): Response
    {
        $userInfo = UserInfo::get();
        
        return response()->json([
            'code' => 200,
            'data' => [
                'user_id' => $userInfo['userId'],
                'message' => '更新成功'
            ]
        ]);
    }

    /**
     * 不需要鉴权的公开接口
     */
    public function public(Request $request): Response
    {
        return response()->json([
            'code' => 200,
            'data' => ['message' => '公开接口']
        ]);
    }
}
```

### 2. 权限命名规范

建议使用以下格式：`应用名:模块:操作`

```
itinerary:order:read      # 订单读取权限
itinerary:order:write     # 订单写入权限
itinerary:order:delete    # 订单删除权限
itinerary:inventory:read  # 库存读取权限
itinerary:inventory:write # 库存写入权限
itinerary:admin:manage    # 管理员权限
```

### 3. 获取用户信息

在需要鉴权的方法中，可以通过 `UserInfo` 类获取当前用户信息：

```php
$userInfo = UserInfo::get();

// 用户信息包含以下字段：
// - userId: 用户ID
// - username: 用户名
// - realName: 真实姓名
// - tenantId: 租户ID
// - tenantName: 租户名称
// - appid: 应用ID
```

## IAM API 接口

### 1. 获取用户信息

```http
GET /api/sso/userinfo
Authorization: Bearer {jwt_token}
```

响应：
```json
{
    "code": 200,
    "message": "获取用户信息成功",
    "data": {
        "user_id": "user123",
        "username": "admin",
        "real_name": "管理员",
        "avatar": "avatar.jpg",
        "email": "<EMAIL>",
        "phone": "13800138000",
        "tenant_id": "default",
        "tenant_name": "默认租户",
        "status": 1
    }
}
```

### 2. 权限鉴权

```http
POST /api/sso/enforce
Authorization: Bearer {jwt_token}
Content-Type: application/json

{
    "actionList": [
        {
            "action": "itinerary:test:read",
            "description": "测试读取权限"
        }
    ]
}
```

响应：
```json
{
    "code": 200,
    "message": "鉴权成功",
    "data": {
        "authorized": true,
        "userinfo": {
            "userId": "user123",
            "username": "admin",
            "realName": "管理员",
            "tenantId": "default",
            "tenantName": "默认租户"
        },
        "permissions": [
            {
                "action": "itinerary:test:read",
                "description": "测试读取权限"
            }
        ]
    }
}
```

### 3. 验证令牌

```http
POST /api/sso/verify-token
Authorization: Bearer {jwt_token}
```

## 测试方法

### 1. 运行测试脚本

```bash
cd itinerary-api
php test_auth.php
```

### 2. 手动测试

1. 首先在 IAM API 中登录获取 token：
```bash
curl -X POST http://localhost:10000/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "admin",
    "password": "admin123",
    "tenant_id": "default"
  }'
```

2. 使用 token 调用需要鉴权的接口：
```bash
curl -X GET http://localhost:8787/api/test-auth/read \
  -H "Authorization: Bearer {your_jwt_token}"
```

## 错误处理

### 常见错误码

- `401`: 未授权，token 无效或已过期
- `403`: 权限不足
- `404`: 用户不存在
- `500`: 服务器内部错误

### 错误响应格式

```json
{
    "code": 401,
    "message": "令牌已过期，请重新登录",
    "data": null
}
```

## 注意事项

1. **Token 安全**: 确保 JWT token 在传输过程中使用 HTTPS
2. **权限粒度**: 合理设计权限粒度，避免过于细化或粗糙
3. **缓存策略**: 考虑对用户权限信息进行适当缓存以提高性能
4. **日志记录**: 重要的鉴权操作应记录审计日志
5. **错误处理**: 提供友好的错误提示信息

## 扩展功能

### 1. 权限缓存
可以在 Redis 中缓存用户权限信息，减少对 IAM API 的调用频率。

### 2. 动态权限
支持运行时动态修改用户权限，无需重启应用。

### 3. 权限继承
支持角色权限继承，简化权限管理。

### 4. 审计日志
记录所有鉴权操作的详细日志，便于安全审计。
