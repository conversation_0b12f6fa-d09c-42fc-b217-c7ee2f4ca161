<?php

namespace app\controller;

use app\model\Itinerary;
use app\model\ItinerarySchedule;
use app\model\ProductCategoryModel;
use think\facade\Db;
use think\Request;

/**
 * 行程控制器
 */
class ItineraryController
{
    /**
     * 创建行程
     * @param Request $request
     * @return \think\response\Json
     */
    public function create(Request $request)
    {
        $data = $request->post();
        
        // 开启事务
        Db::startTrans();
        try {
            // 处理行程基本信息
            $itineraryData = [
                'title' => $data['title'],
                'price' => $data['price'],
                'original_price' => $data['originalPrice'],
                'days' => $data['days'],
                'nights' => $data['nights'],
                'destination' => $data['destination'],
                'departure_city' => $data['departureCity'],
                'product_type' => $data['productType'],
                'cover_image' => $data['coverImage'],
                'images' => json_encode($data['images']),
                'image_description' => isset($data['imageDescription']) ? json_encode($data['imageDescription']) : null,
                'description' => $data['description'],
                'features' => isset($data['features']) ? json_encode($data['features']) : null,
                'status' => $data['status'],
                'travel_mode' => $data['travelMode'],
                'group_type' => $data['groupType'],
                'min_group_size' => $data['minGroupSize'],
                'max_group_size' => $data['maxGroupSize'],
                'collect_type' => $data['collectType'] === 'capitation' ? '按人收费' : $data['collectType'],
                'deposit' => $data['deposit'],
                'promotion_price' => $data['promotionPrice'],
                'category_id' => $data['categoryId'],
                'category_path' => json_encode($data['categoryPath']),
                'is_long_term' => $data['isLongTerm'] ? 1 : 0,
                'single_room_surcharge' => $data['singleRoomSurcharge'] ?? 0,
                'room_upgrade_surcharge' => $data['roomUpgradeSurcharge'] ?? 0,
                'create_time' => time(),
                'update_time' => time()
            ];
            
            // 创建行程
            $itinerary = Itinerary::create($itineraryData);
            $itineraryId = $itinerary->id;
            
            // 处理行程安排
            if (!empty($data['schedule'])) {
                foreach ($data['schedule'] as $schedule) {
                    $scheduleData = [
                        'itinerary_id' => $itineraryId,
                        'day' => $schedule['day'],
                        'title' => $schedule['title'],
                        'description' => $schedule['description'] ?? '',
                        'activities' => json_encode($schedule['activities'] ?? []),
                        'meals' => json_encode($schedule['meals'] ?? []),
                        'accommodation' => $schedule['accommodation'] ?? '',
                        'images' => isset($schedule['images']) ? json_encode($schedule['images']) : null,
                        'type' => $schedule['type'],
                        'meeting_point' => $schedule['meetingPoint'] ?? null,
                        'meeting_time' => isset($schedule['meetingTime']) ? date('H:i:s', strtotime($schedule['meetingTime'])) : null,
                        'start_time' => isset($schedule['startTime']) ? date('H:i:s', strtotime($schedule['startTime'])) : null,
                        'meal_type' => isset($schedule['mealType']) ? json_encode($schedule['mealType']) : null,
                        'adult_meal_included' => $schedule['adultMealIncluded'] ?? null,
                        'child_meal_included' => $schedule['childMealIncluded'] ?? null,
                        'meal_duration_hours' => $schedule['mealDuration']['hours'] ?? 0,
                        'meal_duration_minutes' => $schedule['mealDuration']['minutes'] ?? 0,
                        'meal_price' => $schedule['mealPrice'] ?? null,
                        'transport_type' => $schedule['transportType'] ?? null,
                        'flight_number' => $schedule['flightNumber'] ?? null,
                        'departure_city' => $schedule['departureCity'] ?? null,
                        'arrival_city' => $schedule['arrivalCity'] ?? null,
                        'estimated_duration_hours' => $schedule['estimatedDuration']['hours'] ?? 0,
                        'estimated_duration_minutes' => $schedule['estimatedDuration']['minutes'] ?? 0,
                        'stay_type' => $schedule['stayType'] ?? null,
                        'hotel_name' => $schedule['hotelName'] ?? null,
                        'activity_duration_hours' => $schedule['activityDuration']['hours'] ?? 0,
                        'activity_duration_minutes' => $schedule['activityDuration']['minutes'] ?? 0,
                        'remark' => $schedule['remark'] ?? '',
                        'create_time' => time(),
                        'update_time' => time()
                    ];
                    
                    ItinerarySchedule::create($scheduleData);
                }
            }
            
            Db::commit();
            return json(['code' => 0, 'msg' => '创建成功', 'data' => ['id' => $itineraryId]]);
        } catch (\Exception $e) {
            Db::rollback();
            return json(['code' => 1, 'msg' => '创建失败：' . $e->getMessage()]);
        }
    }
    
    /**
     * 获取行程详情
     * @param int $id 行程ID
     * @return \think\response\Json
     */
    public function detail($id)
    {
        try {
            // 获取行程基本信息
            $itinerary = Itinerary::find($id);
            if (!$itinerary) {
                return json(['code' => 1, 'msg' => '行程不存在']);
            }
            
            // 处理JSON字段
            $itinerary['images'] = json_decode($itinerary['images'], true);
            $itinerary['image_description'] = json_decode($itinerary['image_description'], true);
            $itinerary['features'] = json_decode($itinerary['features'], true);
            $itinerary['category_path'] = json_decode($itinerary['category_path'], true);
            
            // 获取行程安排
            $schedules = ItinerarySchedule::where('itinerary_id', $id)
                ->where('delete_time', 0)
                ->order('day', 'asc')
                ->select()
                ->toArray();
            
            // 处理行程安排中的JSON字段
            foreach ($schedules as &$schedule) {
                $schedule['activities'] = json_decode($schedule['activities'], true);
                $schedule['meals'] = json_decode($schedule['meals'], true);
                $schedule['images'] = json_decode($schedule['images'], true);
                $schedule['meal_type'] = json_decode($schedule['meal_type'], true);
                
                // 转换为前端期望的格式
                $schedule['mealDuration'] = [
                    'hours' => $schedule['meal_duration_hours'],
                    'minutes' => $schedule['meal_duration_minutes']
                ];
                $schedule['estimatedDuration'] = [
                    'hours' => $schedule['estimated_duration_hours'],
                    'minutes' => $schedule['estimated_duration_minutes']
                ];
                $schedule['activityDuration'] = [
                    'hours' => $schedule['activity_duration_hours'],
                    'minutes' => $schedule['activity_duration_minutes']
                ];
                
                // 移除原始字段
                unset($schedule['meal_duration_hours']);
                unset($schedule['meal_duration_minutes']);
                unset($schedule['estimated_duration_hours']);
                unset($schedule['estimated_duration_minutes']);
                unset($schedule['activity_duration_hours']);
                unset($schedule['activity_duration_minutes']);
            }
            
            $itinerary['schedule'] = $schedules;
            
            return json(['code' => 0, 'msg' => '获取成功', 'data' => $itinerary]);
        } catch (\Exception $e) {
            return json(['code' => 1, 'msg' => '获取失败：' . $e->getMessage()]);
        }
    }

} 