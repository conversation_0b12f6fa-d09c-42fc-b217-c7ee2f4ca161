<?php

namespace base\helper;

class Snowflake {
    private     $epoch;
    private int $workerIdBits = 5;
    private int $maxWorkerId;
    private int $sequenceBits = 12;
    private int $workerIdShift;
    private int $timestampLeftShift;
    private int $sequenceMask;
    private int $lastTimestamp = -1;
    private int $workerId;
    private int $sequence = 0;

    public function __construct(int $workerId, $epoch = 1745547036000) {
        $this->maxWorkerId = -1 ^ (-1 << $this->workerIdBits);
        $this->workerIdShift = $this->sequenceBits;
        $this->timestampLeftShift = $this->sequenceBits + $this->workerIdBits;
        $this->sequenceMask = -1 ^ (-1 << $this->sequenceBits);

        if ($workerId > $this->maxWorkerId || $workerId < 0) {
            throw new \RuntimeException("Worker ID 超出范围");
        }

        $this->workerId = $workerId;
        $this->epoch = $epoch;
    }

    public function generateId(): int
    {
        $timestamp = $this->getCurrentTimestamp();

        if ($timestamp < $this->lastTimestamp) {
            throw new \RuntimeException("时钟回拨，无法生成ID");
        }

        if ($timestamp == $this->lastTimestamp) {
            $this->sequence = ($this->sequence + 1) & $this->sequenceMask;
            if ($this->sequence === 0) {
                $timestamp = $this->tilNextMillis($this->lastTimestamp);
            }
        } else {
            $this->sequence = 0;
        }

        $this->lastTimestamp = $timestamp;

        return (($timestamp - $this->epoch) << $this->timestampLeftShift)
               | ($this->workerId << $this->workerIdShift)
               | $this->sequence;
    }

    private function getCurrentTimestamp(): float
    {
        return floor(microtime(true) * 1000);
    }

    private function tilNextMillis($lastTimestamp): float
    {
        $timestamp = $this->getCurrentTimestamp();
        while ($timestamp <= $lastTimestamp) {
            $timestamp = $this->getCurrentTimestamp();
        }
        return $timestamp;
    }
}
