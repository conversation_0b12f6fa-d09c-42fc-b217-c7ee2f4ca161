<?php

declare(strict_types=1);

namespace app\model;

use base\base\BaseModel;
use base\helper\Password;

class UserModel extends BaseModel
{
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'users';

    /**
     * The attributes that should be hidden for arrays.
     *
     * @var array
     */
    protected $hidden = ['password', 'create_time', 'update_time', 'delete_time'];

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'username', 'password', 'nickname', 'avatar', 'gender',
        'email', 'phone', 'status', 'last_login_time', 'last_login_ip'
    ];

    /**
     * Register a new user
     *
     * @param array $data
     * @return UserModel|\think\model\contract\Modelable
     */
    public static function register(array $data) {
        $data['password'] = Password::hash($data['password']);
        return self::create($data);
    }

    /**
     * Verify user password
     *
     * @param string $password
     * @return bool
     */
    public function verifyPassword(string $password): bool
    {
        return Password::verify($password, $this->password);
    }

    /**
     * Update login information
     *
     * @param string $ip
     * @return bool
     */
    public function updateLoginInfo(string $ip): bool
    {
        return $this->save([
            'last_login_time' => date('Y-m-d H:i:s'),
            'last_login_ip' => $ip
        ]);
    }

    /**
     * Get user by username
     *
     * @param string $username
     * @return UserModel|null
     */
    public static function getByUsername(string $username): ?self
    {
        return self::where('username', $username)->find();
    }

    /**
     * Get user by email
     *
     * @param string $email
     * @return UserModel|null
     */
    public static function getByEmail(string $email): ?self
    {
        return self::where('email', $email)->find();
    }

    /**
     * Get user by phone
     *
     * @param string $phone
     * @return UserModel|null
     */
    public static function getByPhone(string $phone): ?self
    {
        return self::where('phone', $phone)->find();
    }

    /**
     * Get user authorizations
     *
     * @return \think\model\relation\HasMany
     */
    public function auths()
    {
        return $this->hasMany(UserAuthModel::class, 'user_id', 'id');
    }

    /**
     * 获取用户列表
     * @param array $where 查询条件
     * @param int $page 页码
     * @param int $pageSize 每页数量
     * @return array
     */
    public function getList(array $where = [], int $page = 1, int $pageSize = 10): array
    {
        $query = $this->where($where);

        $total = $query->count();
        $list = $query->page($page, $pageSize)
            ->order('id desc')
            ->select()
            ->toArray();

        return [
            'list' => $list,
            'total' => $total,
            'page' => $page,
            'page_size' => $pageSize
        ];
    }
}
