<?php

namespace base\AuditLog\traits;


use think\helper\Str;

trait Field
{
    /**
     * 字段
     *
     * @var array
     */
    protected array $field = [];

    /**
     * 字段别名映射
     *
     * @var array
     */
    protected array $alias = [];

    /**
     * 字段别名映射
     *
     * @var array
     */
    private array $aliasTitle = [];

    /**
     * 当前日志使用的字段
     *
     * @var array
     */
    protected array $only = [];
    /**
     * 场景需要移除的字段
     *
     * @var array
     */
    protected array $remove = [];

    /**
     * 场景需要追加的字段
     *
     * @var array
     */
    protected array $append = [];


    /**
     * 索引集合
     *
     * @var array
     */
    protected array $indexList = [];

    /**
     * 索引集合2
     *
     * @var array
     */
    protected array $indexList2 = [];



    /**
     * 获取字段别名
     *
     * @param string $field 字段
     *
     * @return mixed
     */
    public function getAlias(string $field)
    {
        return $this->alias[$field] ?? false;
    }

    /**
     * 获取字段对应的属性名称
     *
     * @param string $alias 字段别名
     *
     * @return mixed
     */
    public function getTitle(string $alias)
    {
        return $this->aliasTitle[$alias] ?? $this->getAlias2Original($alias);
    }

    /**
     * 获取别名映射字段
     *
     * @param string $alias 字段别名
     *
     * @return false|int|string
     */
    private function getAlias2Original(string $alias)
    {
        $original = array_search($alias, $this->alias, true);
        if (empty($original)) {
            $original = $alias;
        }

        return $original;
    }

    /**
     * 添加字段规则
     *
     * @access protected
     *
     * @param string|array $field 字段名称或者规则数组
     * @param mixed $title 字段描述
     *
     * @return $this
     */
    public function field($field, $title = ''): self
    {
        if (is_array($field)) {
            $this->field = array_merge($this->field, $field);
        } else {
            $this->field[$field] = $title;
        }

        return $this;
    }

    /**
     * 设置字段别名
     *
     * @access protected
     *
     * @param string|array $field 字段名称或者字段数组
     * @param mixed $alias 别名
     *
     * @return $this
     */
    public function alias($field, $alias = ''): self
    {
        if (is_array($field)) {
            $this->alias = array_merge($this->field, $field);
        } else {
            $this->alias[$field] = $alias;
        }

        return $this;
    }

    /**
     * 设置字段描述
     *
     * @access protected
     *
     * @param string|array $alias 字段名称|别名
     * @param mixed $title 字段描述
     *
     * @return $this
     */
    public function title($alias, $title = ''): self
    {
        if (is_array($alias)) {
            $this->aliasTitle = array_merge($this->aliasTitle, $alias);
        } else {
            $this->aliasTitle[$alias] = $title;
        }

        return $this;
    }

    /**
     * 指定需要记录的字段列表
     *
     * @access public
     *
     * @param array $fields 字段名|字段别名
     *
     * @return $this
     */
    public function only(array $fields): self
    {
        $this->only = $fields;

        return $this;
    }

    /**
     * 移除某个字段
     *
     * @access public
     *
     * @param string|array $field 字段名或者字段别名
     *
     * @return $this
     */
    public function remove($field): self
    {
        if (is_array($field)) {
            foreach ($field as $rule) {
                $this->remove($rule);
            }
        } else {
            $this->remove[] = $field;
        }

        return $this;
    }

    /**
     * 追加某个字段
     *
     * @access public
     *
     * @param string|array $field 字段名
     * @param mixed $fieldName 字段描述
     *
     * @return $this
     */
    public function append($field, $fieldName = ''): self
    {
        if (is_array($field)) {
            foreach ($field as $key => $name) {
                $this->append($key, $name);
            }
        } else {
            $this->append[$field] = $fieldName;
        }

        return $this;
    }


    public function initField(): void
    {
        $fields = $this->field;

        foreach ($this->append as $key => $field) {
            if (is_numeric($key)) {
               $fields[] = $field;
            } else {
                if ( ! isset($fields[$key])) {
                    $fields[$key] = $field;
                }
            }
        }

        foreach ($fields as $field => $title) {
            if (is_numeric($field)) {
                $field = $title;
            }
            // 字段|别名
            $explode_arr = explode('|', $field);
            $alias_key   = 0;
            if (count($explode_arr) === 2) {
                $alias_key = 1;
            }
            $field = $explode_arr[0];
            $alias = Str::camel($explode_arr[$alias_key]);
            // 移除相关字段
            if ( ! in_array($field, $this->remove, true) || ! in_array($alias, $this->remove, true)) {
                $this->alias($field, $alias);
                $this->title($alias, $title);
            }
        }
    }

    public function getAllField(): array
    {
        return [
            'field'      => $this->field,
            'alias'      => $this->alias,
            'aliasTitle' => $this->aliasTitle,
            'indexList' => $this->indexList,
            'indexList2' => $this->indexList2,
        ];
    }
}
