<?php

namespace app\website\controller;

use app\model\ItineraryInventoryModel;
use app\model\ItineraryModel;
use support\Request;
use support\Response;

class Inventory
{
    /**
     * 获取行程指定月份的库存数据
     * @param Request $request
     * @return Response
     */
    public function month(Request $request): Response
    {

        $itineraryId = $request->input('itineraryId');
        $month = $request->input('month'); // 格式：YYYY-MM
        
        if (!$itineraryId || !$month) {
            return error(400, '参数错误');
        }
        
        try {
            // 检查行程是否存在
            $itineraryModel = new ItineraryModel();
            $itinerary = $itineraryModel->where('id', $itineraryId)->where('delete_time', 0)->find();
            
            if (!$itinerary) {
                return error(404, '行程不存在');
            }
            
            $model = new ItineraryInventoryModel();
            $items = $model->getMonthInventory($itineraryId, $month);
            
            return success([
                'items' => $items
            ]);
        } catch (\Exception $e) {
            return error(500, '获取失败：' . $e->getMessage());
        }
    }
    

    /**
     * 查询单个日期库存详情
     * @param Request $request
     * @return Response
     */
    public function date(Request $request): Response
    {
        $itineraryId = $request->input('itineraryId');
        $date = $request->input('date');
        
        if (!$itineraryId || !$date) {
            return error(400, '参数错误');
        }
        
        try {
            // 检查行程是否存在
            $itineraryModel = new ItineraryModel();
            $itinerary = $itineraryModel->where('id', $itineraryId)->where('delete_time', 0)->find();
            
            if (!$itinerary) {
                return error(404, '行程不存在');
            }
            
            // 检查日期是否在有效期内
            if (!$this->isDateInValidRange($date, $itinerary)) {
                return error(400, '所查询日期不在行程有效期内');
            }
            
            $model = new ItineraryInventoryModel();
            $inventory = $model->getDateInventory($itineraryId, $date);
            
            return success($inventory);
        } catch (\Exception $e) {
            return error(500, '获取失败：' . $e->getMessage());
        }
    }
    

    /**
     * 检查日期是否在行程有效期范围内
     * @param string $date 日期
     * @param array|object $itinerary 行程信息
     * @return bool
     */
    private function isDateInValidRange(string $date, $itinerary): bool
    {
        // 如果是长期有效，则不需要检查有效期
        if (!empty($itinerary['is_long_term'])) {
            return true;
        }
        
        $checkDate = strtotime($date);
        $validFrom = !empty($itinerary['valid_from']) ? strtotime($itinerary['valid_from']) : null;
        $validTo = !empty($itinerary['valid_to']) ? strtotime($itinerary['valid_to']) : null;
        
        // 没有设置有效期，则默认有效
        if ($validFrom === null && $validTo === null) {
            return true;
        }
        
        // 只设置了开始日期
        if ($validFrom !== null && $validTo === null) {
            return $checkDate >= $validFrom;
        }
        
        // 只设置了结束日期
        if ($validFrom === null && $validTo !== null) {
            return $checkDate <= $validTo;
        }
        
        // 两个日期都设置了
        return $checkDate >= $validFrom && $checkDate <= $validTo;
    }
} 