-- 直播间表
CREATE TABLE `live_room` (
    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `title` varchar(100) NOT NULL COMMENT '直播间标题',
    `description` text COMMENT '直播间描述',
    `cover_image` varchar(255) DEFAULT NULL COMMENT '直播间封面图',
    `anchor_name` varchar(50) DEFAULT NULL COMMENT '主播名称',
    `anchor_avatar` varchar(255) DEFAULT NULL COMMENT '主播头像',
    `live_url` varchar(255) DEFAULT NULL COMMENT '直播流地址',
    `playback_url` varchar(255) DEFAULT NULL COMMENT '回放地址',
    `status` enum('upcoming','live','ended','cancelled') DEFAULT 'upcoming' COMMENT '直播状态：upcoming-即将开始，live-直播中，ended-已结束，cancelled-已取消',
    `scheduled_start_time` datetime DEFAULT NULL COMMENT '预定开始时间',
    `scheduled_end_time` datetime DEFAULT NULL COMMENT '预定结束时间',
    `actual_start_time` datetime DEFAULT NULL COMMENT '实际开始时间',
    `actual_end_time` datetime DEFAULT NULL COMMENT '实际结束时间',
    `viewer_count` int(11) DEFAULT '0' COMMENT '观看人数',
    `max_viewer_count` int(11) DEFAULT '0' COMMENT '最高观看人数',
    `like_count` int(11) DEFAULT '0' COMMENT '点赞数',
    `share_count` int(11) DEFAULT '0' COMMENT '分享数',
    `is_featured` tinyint(1) DEFAULT '0' COMMENT '是否推荐',
    `sort_order` int(11) DEFAULT '0' COMMENT '排序权重',
    `tags` json DEFAULT NULL COMMENT '标签',
    `notice` text COMMENT '直播公告',
    `create_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
    `update_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
    `delete_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '删除时间',
    PRIMARY KEY (`id`),
    KEY `idx_status` (`status`),
    KEY `idx_scheduled_start_time` (`scheduled_start_time`),
    KEY `idx_is_featured` (`is_featured`),
    KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='直播间表';

-- 直播商品关联表
CREATE TABLE `live_room_product` (
    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `live_room_id` bigint(20) unsigned NOT NULL COMMENT '直播间ID',
    `product_id` bigint(20) unsigned NOT NULL COMMENT '商品ID（关联itinerary表）',
    `product_type` varchar(20) DEFAULT 'itinerary' COMMENT '商品类型',
    `special_price` decimal(10,2) DEFAULT NULL COMMENT '直播间特价',
    `discount_rate` decimal(5,2) DEFAULT NULL COMMENT '折扣率',
    `stock_limit` int(11) DEFAULT NULL COMMENT '直播间库存限制',
    `sold_count` int(11) DEFAULT '0' COMMENT '已售数量',
    `is_featured` tinyint(1) DEFAULT '0' COMMENT '是否推荐商品',
    `sort_order` int(11) DEFAULT '0' COMMENT '排序权重',
    `status` enum('active','inactive') DEFAULT 'active' COMMENT '状态',
    `create_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
    `update_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
    `delete_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '删除时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_live_product` (`live_room_id`,`product_id`),
    KEY `idx_live_room_id` (`live_room_id`),
    KEY `idx_product_id` (`product_id`),
    KEY `idx_status` (`status`),
    KEY `idx_is_featured` (`is_featured`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='直播商品关联表';

-- 直播间统计表
CREATE TABLE `live_room_stats` (
    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `live_room_id` bigint(20) unsigned NOT NULL COMMENT '直播间ID',
    `date` date NOT NULL COMMENT '统计日期',
    `total_viewers` int(11) DEFAULT '0' COMMENT '总观看人数',
    `peak_viewers` int(11) DEFAULT '0' COMMENT '峰值观看人数',
    `total_likes` int(11) DEFAULT '0' COMMENT '总点赞数',
    `total_shares` int(11) DEFAULT '0' COMMENT '总分享数',
    `total_orders` int(11) DEFAULT '0' COMMENT '总订单数',
    `total_sales` decimal(12,2) DEFAULT '0.00' COMMENT '总销售额',
    `duration_minutes` int(11) DEFAULT '0' COMMENT '直播时长（分钟）',
    `create_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
    `update_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_live_date` (`live_room_id`,`date`),
    KEY `idx_date` (`date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='直播间统计表';
