<?php

namespace app\api\controller;

use app\model\CitiesModel;
use app\model\CityInfoModel;
use app\model\ItineraryDayModel;
use app\model\ItineraryModel;
use app\model\ItineraryScheduleModel;
use app\model\ProductCategoryModel;
use base\auth\Auth;
use support\Request;
use support\Response;
use think\facade\Db;

class ItineraryForUser {
    /**
     * 获取行程列表
     * @param Request $request
     * @return Response
     */
    public function list(Request $request): Response
    {
        $page = $request->input('page', 1);
        $pageSize = $request->input('pageSize', 10);
        $status = $request->input('status');
        $keyword = $request->input('keyword');
        $destination = $request->input('destination');
        $departureCity = $request->input('departureCity');
        $productType = $request->input('productType');
        $categoryId = $request->input('categoryId');

        // 构建查询条件
        $query = ItineraryModel::mk()
            ->field([
                'id', 'title', 'price', 'original_price', 'days', 'nights', 'destination', 'departure_city',
                'product_type', 'cover_image', 'status', 'travel_mode', 'group_type', 'min_group_size',
                'max_group_size', 'collect_type', 'deposit', 'promotion_price', 'category_id', 'category_path',
                'valid_from', 'valid_to', 'is_long_term', 'single_room_surcharge', 'room_upgrade_surcharge',
                'create_time', 'update_time',
                // 主题相关字段
                'themes_ids',
                // 交易规则相关字段
                'consumable_date_type', 'consumable_days', 'consumable_dates',
                // 限购规则
                'purchase_limit_single', 'purchase_limit_per_order', 'purchase_limit_rules', 'purchase_limit_details',
                // 预约规则
                'earliest_booking_days', 'advance_booking_required', 'advance_booking_range',
                'advance_booking_time', 'advance_booking_time_unit',
                // 取消规则
                'has_no_cancellation_after_appointment', 'refund_before_appointment', 'auto_refund_after_expiration',
                'has_breach_of_contract_terms', 'breach_of_contract_terms', 'breach_of_contract_base'
            ])
            ->where('delete_time', 0);

        // 应用筛选条件
        if (!empty($status)) {
            $query = $query->where('status', $status);
        }
        if (!empty($keyword)) {
            $query = $query->where('title', 'like', "%$keyword%");
        }
        if (!empty($destination)) {
            $query = $query->where('destination', $destination);
        }
        if (!empty($departureCity)) {
            $query = $query->whereFindInSet('departure_city', $departureCity);
        }
        if (!empty($categoryId)) {
            $query = $query->where('category_id', $categoryId);
        }
        if (!empty($productType)) {
            $query = $query->where('product_type', $productType);
        }

        // 执行查询
        $total = $query->count();
        $list = $query->page($page, $pageSize)
        ->order('id', 'desc')->append(['id'])
            ->withAttr('themes_ids', function ($value) {
                return str2Arr($value);
            })
        ->select()->toArray();

        foreach ($list as &$item) {
            $item['destinationText'] = CitiesModel::getCityInfoByIds($item['destination']);
            $item['departureCityText'] = CityInfoModel::getCityInfoByIds($item['departureCity']);
        }

        return success([
            'list' => $list,
            'total' => $total,
            'total2' => $total,
            'page' => (int)$page,
            'pageSize' => (int)$pageSize,
        ]);
    }

    /**
     * 获取行程详情
     * @param Request $request
     * @param string $id
     * @return Response
     */
    public function detail(Request $request, string $id = ''): Response
    {
        // 尝试从路径获取ID，如果没有则从请求参数中获取
        if (empty($id)) {
            $id = $request->input('id');
        }

        if (!$id) {
            return error(400, '参数错误');
        }

        // 查询行程基本信息
        $detail = ItineraryModel::mk()
            ->with(['scheduleDays'=> ['schedules']])
            ->field([
                'id', 'title', 'price', 'original_price', 'days', 'nights', 'destination', 'departure_city',
                'product_type', 'cover_image', 'images', 'image_description', 'description', 'features', 'status',
                'travel_mode', 'group_type', 'min_group_size', 'max_group_size', 'collect_type', 'deposit',
                'promotion_price', 'category_id', 'category_path', 'valid_from', 'valid_to', 'is_long_term',
                'single_room_surcharge', 'room_upgrade_surcharge', 'create_time', 'update_time',
                // 主题相关字段
                'themes_ids',
                // 交易规则相关字段
                'consumable_date_type', 'consumable_days', 'consumable_dates',
                // 限购规则
                'purchase_limit_single', 'purchase_limit_per_order', 'purchase_limit_rules', 'purchase_limit_details',
                // 预约规则
                'earliest_booking_days', 'advance_booking_required', 'advance_booking_range',
                'advance_booking_time', 'advance_booking_time_unit',
                // 取消规则
                'has_no_cancellation_after_appointment', 'refund_before_appointment', 'auto_refund_after_expiration',
                'has_breach_of_contract_terms', 'breach_of_contract_terms', 'breach_of_contract_base'
            ])
            ->where('id', $id)
            ->append(['id'])
            ->withAttr('themes_ids', function ($value) {
                return str2Arr($value);
            })
            ->find();

        if (!$detail) {
            return error(400, '行程不存在');
        }

        $result = $detail->toArray();

        // 处理是否长期有效
        $result['is_long_term'] = (bool)($result['isLongTerm'] ?? false);

        return success($result);
    }
}
