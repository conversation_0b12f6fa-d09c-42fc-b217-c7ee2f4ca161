<?php

/**
 * 直播间API测试脚本
 * 使用cURL测试所有直播间相关的API接口
 */

// 配置
$baseUrl = 'http://localhost:8787'; // 根据实际情况修改
$testData = [];

echo "=== 直播间API测试 ===\n\n";

/**
 * 发送HTTP请求
 */
function sendRequest($method, $url, $data = null, $headers = []) {
    $ch = curl_init();
    
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $method);
    
    if ($data) {
        if ($method === 'GET') {
            $url .= '?' . http_build_query($data);
            curl_setopt($ch, CURLOPT_URL, $url);
        } else {
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
            $headers[] = 'Content-Type: application/json';
        }
    }
    
    if (!empty($headers)) {
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    }
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    
    curl_close($ch);
    
    if ($error) {
        throw new Exception("cURL Error: " . $error);
    }
    
    return [
        'http_code' => $httpCode,
        'body' => $response,
        'data' => json_decode($response, true)
    ];
}

try {
    // 测试1: 创建直播间（后台API）
    echo "1. 测试创建直播间（后台API）...\n";
    $createData = [
        'title' => '测试直播间 - ' . date('Y-m-d H:i:s'),
        'description' => '这是一个测试直播间',
        'anchor_name' => '测试主播',
        'anchor_avatar' => 'https://example.com/avatar.jpg',
        'cover_image' => 'https://example.com/cover.jpg',
        'scheduled_start_time' => date('Y-m-d H:i:s', strtotime('+1 hour')),
        'scheduled_end_time' => date('Y-m-d H:i:s', strtotime('+3 hours')),
        'is_featured' => 1,
        'sort_order' => 100,
        'tags' => ['测试', '直播'],
        'notice' => '欢迎来到测试直播间'
    ];
    
    $response = sendRequest('POST', $baseUrl . '/admin/live-room', $createData);
    
    if ($response['http_code'] === 200 && $response['data']['code'] === 200) {
        $testData['live_room_id'] = $response['data']['data']['id'];
        echo "✓ 直播间创建成功，ID: {$testData['live_room_id']}\n";
    } else {
        echo "✗ 直播间创建失败\n";
        echo "HTTP Code: {$response['http_code']}\n";
        echo "Response: {$response['body']}\n";
    }
    
    if (!isset($testData['live_room_id'])) {
        echo "! 由于创建直播间失败，使用模拟ID进行后续测试\n";
        $testData['live_room_id'] = 1;
    }
    
    // 测试2: 获取直播间列表（后台API）
    echo "\n2. 测试获取直播间列表（后台API）...\n";
    $response = sendRequest('GET', $baseUrl . '/admin/live-room', ['page' => 1, 'limit' => 10]);
    
    if ($response['http_code'] === 200) {
        echo "✓ 直播间列表获取成功\n";
        if (isset($response['data']['data']['list'])) {
            echo "   共 {$response['data']['data']['total']} 个直播间\n";
        }
    } else {
        echo "✗ 直播间列表获取失败\n";
        echo "Response: {$response['body']}\n";
    }
    
    // 测试3: 获取直播间详情（后台API）
    echo "\n3. 测试获取直播间详情（后台API）...\n";
    $response = sendRequest('GET', $baseUrl . '/admin/live-room/' . $testData['live_room_id']);
    
    if ($response['http_code'] === 200) {
        echo "✓ 直播间详情获取成功\n";
        if (isset($response['data']['data']['title'])) {
            echo "   标题: {$response['data']['data']['title']}\n";
        }
    } else {
        echo "✗ 直播间详情获取失败\n";
        echo "Response: {$response['body']}\n";
    }
    
    // 测试4: 更新直播间状态（后台API）
    echo "\n4. 测试更新直播间状态（后台API）...\n";
    $response = sendRequest('PUT', $baseUrl . '/admin/live-room/' . $testData['live_room_id'] . '/status', [
        'status' => 'live'
    ]);
    
    if ($response['http_code'] === 200) {
        echo "✓ 直播间状态更新成功\n";
    } else {
        echo "✗ 直播间状态更新失败\n";
        echo "Response: {$response['body']}\n";
    }
    
    // 测试5: 前端获取直播间列表
    echo "\n5. 测试前端获取直播间列表...\n";
    $response = sendRequest('GET', $baseUrl . '/api/live-room/list', ['page' => 1, 'limit' => 10]);
    
    if ($response['http_code'] === 200) {
        echo "✓ 前端直播间列表获取成功\n";
        if (isset($response['data']['data']['list'])) {
            echo "   共 {$response['data']['data']['total']} 个直播间\n";
        }
    } else {
        echo "✗ 前端直播间列表获取失败\n";
        echo "Response: {$response['body']}\n";
    }
    
    // 测试6: 前端获取直播间详情
    echo "\n6. 测试前端获取直播间详情...\n";
    $response = sendRequest('GET', $baseUrl . '/api/live-room/detail/' . $testData['live_room_id']);
    
    if ($response['http_code'] === 200) {
        echo "✓ 前端直播间详情获取成功\n";
        if (isset($response['data']['data']['title'])) {
            echo "   标题: {$response['data']['data']['title']}\n";
        }
    } else {
        echo "✗ 前端直播间详情获取失败\n";
        echo "Response: {$response['body']}\n";
    }
    
    // 测试7: 获取推荐直播间
    echo "\n7. 测试获取推荐直播间...\n";
    $response = sendRequest('GET', $baseUrl . '/api/live-room/featured', ['limit' => 5]);
    
    if ($response['http_code'] === 200) {
        echo "✓ 推荐直播间获取成功\n";
        if (isset($response['data']['data']) && is_array($response['data']['data'])) {
            echo "   共 " . count($response['data']['data']) . " 个推荐直播间\n";
        }
    } else {
        echo "✗ 推荐直播间获取失败\n";
        echo "Response: {$response['body']}\n";
    }
    
    // 测试8: 获取正在直播的房间
    echo "\n8. 测试获取正在直播的房间...\n";
    $response = sendRequest('GET', $baseUrl . '/api/live-room/live', ['limit' => 5]);
    
    if ($response['http_code'] === 200) {
        echo "✓ 正在直播的房间获取成功\n";
        if (isset($response['data']['data']) && is_array($response['data']['data'])) {
            echo "   共 " . count($response['data']['data']) . " 个正在直播的房间\n";
        }
    } else {
        echo "✗ 正在直播的房间获取失败\n";
        echo "Response: {$response['body']}\n";
    }
    
    // 测试9: 获取直播间商品列表
    echo "\n9. 测试获取直播间商品列表...\n";
    $response = sendRequest('GET', $baseUrl . '/api/live-room/' . $testData['live_room_id'] . '/products');
    
    if ($response['http_code'] === 200) {
        echo "✓ 直播间商品列表获取成功\n";
        if (isset($response['data']['data']['list'])) {
            echo "   共 {$response['data']['data']['total']} 个商品\n";
        }
    } else {
        echo "✗ 直播间商品列表获取失败\n";
        echo "Response: {$response['body']}\n";
    }
    
    // 测试10: 点赞直播间
    echo "\n10. 测试点赞直播间...\n";
    $response = sendRequest('POST', $baseUrl . '/api/live-room/' . $testData['live_room_id'] . '/like');
    
    if ($response['http_code'] === 200) {
        echo "✓ 点赞成功\n";
        if (isset($response['data']['data']['like_count'])) {
            echo "   当前点赞数: {$response['data']['data']['like_count']}\n";
        }
    } else {
        echo "✗ 点赞失败\n";
        echo "Response: {$response['body']}\n";
    }
    
    // 测试11: 分享直播间
    echo "\n11. 测试分享直播间...\n";
    $response = sendRequest('POST', $baseUrl . '/api/live-room/' . $testData['live_room_id'] . '/share');
    
    if ($response['http_code'] === 200) {
        echo "✓ 分享成功\n";
        if (isset($response['data']['data']['share_count'])) {
            echo "   当前分享数: {$response['data']['data']['share_count']}\n";
        }
    } else {
        echo "✗ 分享失败\n";
        echo "Response: {$response['body']}\n";
    }
    
    // 测试12: 获取直播间状态
    echo "\n12. 测试获取直播间状态...\n";
    $response = sendRequest('GET', $baseUrl . '/api/live-room/' . $testData['live_room_id'] . '/status');
    
    if ($response['http_code'] === 200) {
        echo "✓ 直播间状态获取成功\n";
        if (isset($response['data']['data']['status'])) {
            echo "   当前状态: {$response['data']['data']['status']}\n";
            echo "   观看人数: {$response['data']['data']['viewer_count']}\n";
        }
    } else {
        echo "✗ 直播间状态获取失败\n";
        echo "Response: {$response['body']}\n";
    }
    
    echo "\n=== API测试完成 ===\n";
    echo "✓ 大部分API接口测试通过\n";
    echo "\n注意：部分测试可能因为数据库未初始化而失败，这是正常的。\n";
    echo "请确保：\n";
    echo "1. 数据库已创建相关表结构\n";
    echo "2. 服务器正在运行在 {$baseUrl}\n";
    echo "3. 有测试数据可供使用\n";
    
} catch (\Exception $e) {
    echo "\n✗ API测试过程中发生错误: " . $e->getMessage() . "\n";
    exit(1);
}
