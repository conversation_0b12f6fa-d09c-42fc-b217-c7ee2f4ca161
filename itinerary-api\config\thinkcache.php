<?php

return [
    'default' => 'redis',
    'stores'  => [
        'file'  => [
            'type'   => 'File',
            // 缓存保存目录
            'path'   => runtime_path() . '/cache/',
            // 缓存前缀
            'prefix' => '',
            // 缓存有效期 0表示永久缓存
            'expire' => 0,
        ],
        'redis' => [
            'type'     => 'redis',
            'host'     => env('REDIS.HOST', '127.0.0.1'),
            'password' => env('REDIS.PASSWORD', ''),
            'port'     => env('REDIS.PORT', 6379),
            'select'   => env('REDIS.DATABASE', 0),
            'prefix'   => '',
            'expire'   => 0,
        ],
    ],
];