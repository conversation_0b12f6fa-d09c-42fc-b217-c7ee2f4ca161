<?php

namespace base\auth;

use DI\Attribute\Inject;
use GuzzleHttp\Client;
use support\Log;
use base\exception\BaseException;

/**
 * 通过 Token 实例化 userinfo()
 * 获取策略
 * 本地鉴权
 */
class AuthHttpClient
{
    #[Inject]
    private Client $client;

    #[Inject("iam_api_domain")]
    private string $iamApiDomain;

    #[Inject("appid")]
    private string $appid;

    #[Inject("app_secret")]
    private string $appSecret;

    /**
     * 通过 iam-api 验证 JWT 令牌并获取用户信息
     *
     * @param string $token JWT 令牌
     * @return array 用户信息
     * @throws BaseException
     * @throws \JsonException
     */
    public function verifyTokenAndGetUserInfo(string $token): array
    {
        $path = '/api/v1/auth/userinfo';
        return $this->httpSendToIamApi($path, [], $token)['data'];
    }

    /**
     * 通过 iam-api 进行权限鉴权
     *
     * @param array $actionList 权限动作列表
     * @param string $token JWT 令牌
     * @return array 鉴权结果，包含用户信息
     * @throws BaseException
     * @throws \JsonException
     */
    public function enforceWithIamApi(array $actionList, string $token): array
    {
        $path = '/api/v1/auth/enforce';
        return $this->httpSendToIamApi($path, ['actionList' => $actionList], $token, 'POST')['data'];
    }

    /**
     * 发送数据
     *
     * @param string $path
     * @param array $body
     * @param bool $isNeedToken
     *
     * @return array
     * @throws BaseException
     * @throws \JsonException
     * @date 2022-08-22 15:26:58
     */
    public function httpSend(string $path, array $body = [], bool $isNeedToken = true): array
    {
        Log::log('debug', 'request', ['path' => $path, 'body' => $body]);

        $body['appid'] = $this->appid;
        $body['appSecret'] = $this->appSecret;

        $headers = [
            'Content-Type' => 'multipart/form-data',
            'Accept' => 'application/json',
        ];
        if ($isNeedToken) {
            $headers['Authorization'] = self::getTokenFromHeaders();
        }
        try {
            $response = $this->client->request(
                'POST',
                $this->domain . $path,
                [
                    'form_params' => $body,
                    'headers' => $headers,
                ],
            );
        } catch (\Throwable $th) {
            Log::log('debug', 'exception', ['code' => $th->getCode(), 'msg' => $th->getMessage()]);

            throw new BaseException($th->getMessage(), $th->getCode());
        }

        $status = $response->getStatusCode();
        $responseBody = json_decode($response->getBody(), true, 512, JSON_THROW_ON_ERROR);

        if ($status !== 200 || $responseBody['code'] !== 200) {

            $body = (string)($response->getBody());
            Log::log('debug', 'request exception', ['code' => $status, 'msg' => $body]);

            throw new BaseException($responseBody['message'] ?? ($responseBody['msg'] ?? ''), $responseBody['code'], $status);
        } else {
            Log::log('debug', 'response', ['path' => $path, 'body' => $responseBody]);
        }

        return $responseBody;
    }

    /**
     * 向 iam-api 发送请求
     *
     * @param string $path 请求路径
     * @param array $body 请求体
     * @param string $token JWT 令牌
     * @param string $method HTTP方法
     * @return array 响应数据
     * @throws BaseException
     * @throws \JsonException
     */
    private function httpSendToIamApi(string $path, array $body = [], string $token = '', string $method = 'GET'): array
    {
        Log::log('debug', 'iam-api request', ['path' => $path, 'body' => $body]);

        $headers = [
            'Content-Type' => 'application/json',
            'Accept' => 'application/json',
        ];

        if (!empty($token)) {
            // 确保令牌格式正确
            if (!str_starts_with($token, 'Bearer ')) {
                $token = 'Bearer ' . $token;
            }
            $headers['Authorization'] = $token;
        }

        try {
            $requestOptions = [
                'headers' => $headers,
            ];

            // 根据HTTP方法设置请求体
            if ($method === 'POST' && !empty($body)) {
                $requestOptions['json'] = $body;
            } elseif ($method === 'GET' && !empty($body)) {
                $requestOptions['query'] = $body;
            }

            $response = $this->client->request(
                $method,
                $this->iamApiDomain . $path,
                $requestOptions
            );
        } catch (\Throwable $th) {
            Log::log('debug', 'iam-api exception', ['code' => $th->getCode(), 'msg' => $th->getMessage()]);
            throw new BaseException('IAM API 请求失败: ' . $th->getMessage(), $th->getCode());
        }

        $status = $response->getStatusCode();
        $responseBody = json_decode($response->getBody(), true, 512, JSON_THROW_ON_ERROR);

        if ($status !== 200) {
            $body = (string)($response->getBody());
            Log::log('debug', 'iam-api request exception', ['code' => $status, 'msg' => $body]);

            throw new BaseException(
                $responseBody['message'] ?? ($responseBody['msg'] ?? 'IAM API 请求失败'),
                $responseBody['code'] ?? $status,
                $status
            );
        }

        // 检查业务状态码
        if (isset($responseBody['code']) && $responseBody['code'] !== 200) {
            throw new BaseException(
                $responseBody['message'] ?? ($responseBody['msg'] ?? 'IAM API 业务错误'),
                $responseBody['code']
            );
        }

        Log::log('debug', 'iam-api response', ['path' => $path, 'body' => $responseBody]);
        return $responseBody;
    }

    /**
     * 获取Header头部authorization令牌
     *
     * @throws BaseException
     */
    private static function getTokenFromHeaders(): string
    {
        $authorization = request()?->header('authorization');

        if (empty($authorization)) {
            throw new BaseException('authorization not found');
        }

        return $authorization;
    }

    public static function start($worker)
    {
    }

}
