<?php

declare(strict_types=1);

namespace app\admin\controller;

use app\model\LiveRoomModel;
use app\model\LiveRoomProductModel;
use app\model\LiveRoomStatsModel;
use app\model\ItineraryModel;
use app\model\User;
use app\model\UserModel;
use support\Request;
use support\Response;

/**
 * 用户管理控制器
 */
class UserController extends BaseController
{
    /**
     * 获取直播间列表
     * @param Request $request
     * @return Response
     */
    public function index(Request $request): Response
    {
        try {
            $params = $this->getPaginationParams($request);
            $where = [];

            // 搜索条件
            $nickName = $request->input('nickname');
            if ($nickName) {
                $where[] = ['nickname', 'like', "%{$nickName}%"];
            }

            $phone = $request->input('phone');
            if ($phone) {
                $where[] = ['phone', 'like', "%{$phone}%"];
            }

            // 添加软删除条件
            $where['delete_time'] = null;

            $model = new UserModel();
            $result = $model->getList($where, $params['page'], $params['limit']);

            return success($result);
        } catch (\Exception $e) {
            return $this->handleException($e, '获取用户列表');
        }
    }

    public function disabled(Request $request, int $id): Response
    {
        try {
            $model = new UserModel();
            if (!$user = $model->find($id)) {
                return error(500, '用户不存在');
            }

            $user->status = (int)$request->post('status', 0);
            $user->save();
            return success($user->toArray());
        } catch (\Exception $e) {
            return $this->handleException($e, '禁用用户');
        }
    }
}
