<?php
/**
 * 鉴权功能测试脚本
 * 
 * 使用方法：
 * 1. 首先在iam-api中登录获取JWT token
 * 2. 使用token调用itinerary-api的鉴权接口
 */

// 配置
$iamApiUrl = 'http://localhost:10000'; // IAM API地址
$itineraryApiUrl = 'http://localhost:8787'; // Itinerary API地址

// 测试用户登录信息
$loginData = [
    'username' => 'admin',
    'password' => 'admin123',
    'tenant_id' => 'default'
];

echo "=== 鉴权功能测试 ===\n\n";

// 步骤1: 登录获取JWT token
echo "1. 登录获取JWT token...\n";
$loginResponse = httpRequest('POST', $iamApiUrl . '/api/v1/auth/login', $loginData);

if (!$loginResponse || !isset($loginResponse['data']['access_token'])) {
    echo "登录失败: " . json_encode($loginResponse) . "\n";
    exit(1);
}

$accessToken = $loginResponse['data']['access_token'];
echo "登录成功，获取到token: " . substr($accessToken, 0, 50) . "...\n\n";

// 步骤2: 测试IAM API的用户信息接口
echo "2. 测试IAM API用户信息接口...\n";
$userinfoResponse = httpRequest('GET', $iamApiUrl . '/api/sso/userinfo', [], [
    'Authorization: Bearer ' . $accessToken
]);

if ($userinfoResponse && $userinfoResponse['code'] == 200) {
    echo "用户信息获取成功: " . $userinfoResponse['data']['username'] . "\n\n";
} else {
    echo "用户信息获取失败: " . json_encode($userinfoResponse) . "\n\n";
}

// 步骤3: 测试IAM API的令牌验证接口
echo "3. 测试IAM API令牌验证接口...\n";
$verifyResponse = httpRequest('POST', $iamApiUrl . '/api/sso/verify-token', [], [
    'Authorization: Bearer ' . $accessToken,
    'Content-Type: application/json'
]);

if ($verifyResponse && $verifyResponse['code'] == 200 && $verifyResponse['data']['valid']) {
    echo "令牌验证成功，用户: " . $verifyResponse['data']['username'] . "，剩余时间: " . $verifyResponse['data']['expires_in'] . "秒\n\n";
} else {
    echo "令牌验证失败: " . json_encode($verifyResponse) . "\n\n";
}

// 步骤4: 测试IAM API的鉴权接口（使用UnifiedAuthMiddleware方式）
echo "4. 测试IAM API鉴权接口（使用UnifiedAuthMiddleware方式）...\n";
$enforceData = [
    'actionList' => [
        ['action' => 'itinerary:test:read', 'description' => '测试读取权限']
    ]
];

$enforceResponse = httpRequest('POST', $iamApiUrl . '/api/sso/enforce', $enforceData, [
    'Authorization: Bearer ' . $accessToken,
    'Content-Type: application/json'
]);

if ($enforceResponse && $enforceResponse['code'] == 200) {
    echo "鉴权成功，用户: " . $enforceResponse['data']['userinfo']['username'] . "\n\n";
} else {
    echo "鉴权失败: " . json_encode($enforceResponse) . "\n\n";
}

// 步骤5: 测试itinerary-api的鉴权功能
echo "5. 测试itinerary-api鉴权功能...\n";

// 测试需要鉴权的接口
$testEndpoints = [
    ['GET', '/api/test-auth/read', '测试读取权限'],
    ['POST', '/api/test-auth/write', '测试写入权限'],
    ['GET', '/api/test-auth/admin', '测试管理员权限'],
    ['POST', '/api/test-auth/multiple', '测试多权限'],
    ['GET', '/api/test-auth/public', '测试公开接口（无需鉴权）']
];

foreach ($testEndpoints as $endpoint) {
    [$method, $path, $description] = $endpoint;
    echo "测试: $description ($method $path)\n";
    
    $headers = [];
    if ($path !== '/api/test-auth/public') {
        $headers[] = 'Authorization: Bearer ' . $accessToken;
    }
    
    $response = httpRequest($method, $itineraryApiUrl . $path, [], $headers);
    
    if ($response && $response['code'] == 200) {
        echo "✓ 成功: " . $response['message'] . "\n";
    } else {
        echo "✗ 失败: " . json_encode($response) . "\n";
    }
    echo "\n";
}

echo "=== 测试完成 ===\n";

/**
 * 发送HTTP请求
 */
function httpRequest($method, $url, $data = [], $headers = []) {
    $ch = curl_init();
    
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    
    // 设置请求方法
    if ($method === 'POST') {
        curl_setopt($ch, CURLOPT_POST, true);
        if (!empty($data)) {
            // 检查是否有JSON Content-Type头
            $isJson = false;
            foreach ($headers as $header) {
                if (stripos($header, 'Content-Type: application/json') !== false) {
                    $isJson = true;
                    break;
                }
            }
            
            if ($isJson) {
                curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
            } else {
                curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
            }
        }
    } elseif ($method === 'GET' && !empty($data)) {
        $url .= '?' . http_build_query($data);
        curl_setopt($ch, CURLOPT_URL, $url);
    }
    
    // 设置请求头
    if (!empty($headers)) {
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    }
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    
    if (curl_error($ch)) {
        echo "CURL错误: " . curl_error($ch) . "\n";
        curl_close($ch);
        return null;
    }
    
    curl_close($ch);
    
    $decoded = json_decode($response, true);
    if (json_last_error() !== JSON_ERROR_NONE) {
        echo "JSON解析错误: " . json_last_error_msg() . "\n";
        echo "原始响应: " . $response . "\n";
        return null;
    }
    
    return $decoded;
}
