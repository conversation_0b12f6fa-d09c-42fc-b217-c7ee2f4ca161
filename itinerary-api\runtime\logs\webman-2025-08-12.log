[2025-08-12 11:24:32] default.ERROR: 127.0.0.1 POST 127.0.0.1:8899/api/booking/order
TypeError: app\service\BookingService::createOrder(): Argument #2 ($userId) must be of type int, string given, called in D:\work\hotel\all\itinerary-api\app\api\controller\Booking.php on line 54 and defined in D:\work\hotel\all\itinerary-api\app\service\BookingService.php:26
Stack trace:
#0 D:\work\hotel\all\itinerary-api\app\api\controller\Booking.php(54): app\service\BookingService->createOrder('20', '2')
#1 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\App.php(336): app\api\controller\Booking->createOrder(Object(support\Request))
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\App.php(359): Webman\App::Webman\{closure}(Object(support\Request))
#3 D:\work\hotel\all\itinerary-api\app\middleware\UserAuth.php(64): Webman\App::Webman\{closure}(Object(support\Request))
#4 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\App.php(352): app\middleware\UserAuth->process(Object(support\Request), Object(Closure))
#5 D:\work\hotel\all\itinerary-api\base\middleware\AcceptLanguage.php(81): Webman\App::Webman\{closure}(Object(support\Request))
#6 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\App.php(352): base\middleware\AcceptLanguage->process(Object(support\Request), Object(Closure))
#7 D:\work\hotel\all\itinerary-api\base\middleware\AccessControlTest.php(13): Webman\App::Webman\{closure}(Object(support\Request))
#8 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\App.php(352): base\middleware\AccessControlTest->process(Object(support\Request), Object(Closure))
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\App.php(150): Webman\App::Webman\{closure}(Object(support\Request))
#10 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Connection\TcpConnection.php(750): Webman\App->onMessage(Object(Workerman\Connection\TcpConnection), Object(support\Request))
#11 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Events\Select.php(406): Workerman\Connection\TcpConnection->baseRead(Resource id #343)
#12 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1613): Workerman\Events\Select->run()
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#14 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#15 D:\work\hotel\all\itinerary-api\runtime\windows\start_webman.php(33): Workerman\Worker::runAll()
#16 {main} [] []
