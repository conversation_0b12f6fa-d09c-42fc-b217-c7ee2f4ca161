<?php

namespace app\api\controller;

use app\model\ItineraryInventoryModel;
use app\model\ItineraryModel;
use app\model\ItineraryOrderModel;
use support\Request;
use support\Response;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\facade\Db;

class Order
{
    /**
     * 获取可用线路列表（用于订单创建时的下拉选择）
     * @param Request $request
     * @return Response
     */
    public function itineraryOptions(Request $request): Response
    {
        $keyword = $request->input('keyword', '');
        $destination = $request->input('destination', '');
        $departureCity = $request->input('departureCity', '');
        $date = $request->input('date', ''); // 可以根据日期筛选有库存的线路
        $page = (int)$request->input('page', 1);
        $limit = (int)$request->input('limit', 10);
        
        // 构建查询条件
        $where = [
            'status' => 'online', // 只查找上架状态的线路
            'delete_time' => 0
        ];
        
        // 添加目的地过滤
        if ($destination) {
            $where['destination'] = $destination;
        }
        
        // 添加出发城市过滤
        if ($departureCity) {
            $where['departure_city'] = $departureCity;
        }
        
        // 添加关键字搜索
        if ($keyword) {
            $where[] = ['title', 'LIKE', "%{$keyword}%"];
        }

        try {
            $model = new ItineraryModel();
            $list = $model->where($where)
                ->field('id, title, price, days, nights, destination, departure_city, cover_image')
                ->order('create_time', 'desc')
                ->append(['id'])
                ->page($page, $limit)
                ->select()
                ->toArray();
            $total = $model->where($where)->count();
            // 如果指定了日期，则需要额外筛选在此日期有库存的线路
            if ($date) {
                $validItineraryIds = [];
                $inventoryModel = new ItineraryInventoryModel();
                
                foreach ($list as $itinerary) {
                    // 检查该日期是否有可用库存
                    $inventory = $inventoryModel->where([
                        'itinerary_id' => $itinerary['id'],
                        'inventory_date' => $date,
                        'status' => '1', // 可预约状态
                        'delete_time' => 0
                    ])->find();
                    
                    if ($inventory) {
                        // 检查库存是否足够
                        if ($inventory['total_amount'] === null || $inventory['total_amount'] > $inventory['booked_amount']) {
                            $validItineraryIds[] = $itinerary['id'];
                        }
                    }
                }
                
                // 只保留有库存的线路
                if (!empty($validItineraryIds)) {
                    $filteredList = [];
                    foreach ($list as $itinerary) {
                        if (in_array($itinerary['id'], $validItineraryIds)) {
                            $filteredList[] = $itinerary;
                        }
                    }
                    $list = $filteredList;
                } else {
                    $list = []; // 如果没有线路有库存，则返回空数组
                }
            }

            // 对列表进行处理，添加一些显示信息
            foreach ($list as &$item) {
                $item['label'] = $item['title'] . ' (' . $item['destination'] . ', ' . $item['days'] . '天' . $item['nights'] . '晚)';
                $item['value'] = $item['id'];
            }
            
            return success([
                'list' => $list,
                'total' => $total
            ]);
        } catch (\Exception $e) {
            return error(500, '获取失败：' . $e->getMessage());
        }
    }

    /**
     * 获取订单列表
     * @param Request $request
     * @return Response
     */
    public function list(Request $request): Response
    {
        $page = $request->input('page', 1);
        $pageSize = $request->input('pageSize', 10);
        $status = $request->input('status', '');
        $itineraryId = $request->input('itineraryId', '');
        $userId = $request->input('userId', '');
        $keyword = $request->input('keyword', ''); // 搜索订单号、用户名、电话
        $startDate = $request->input('startDate', '');
        $endDate = $request->input('endDate', '');

        $where = [];
        
        // 状态筛选
        if ($status) {
            $where['status'] = $status;
        }
        
        // 行程筛选
        if ($itineraryId) {
            $where['itinerary_id'] = $itineraryId;
        }
        
        // 用户筛选
        if ($userId) {
            $where['user_id'] = $userId;
        }
        
        // 日期范围筛选
        if ($startDate) {
            $where[] = ['travel_date', '>=', $startDate];
        }
        if ($endDate) {
            $where[] = ['travel_date', '<=', $endDate];
        }
        
        // 关键字搜索
        if ($keyword) {
            $where[] = ['order_no|name|phone', 'LIKE', "%{$keyword}%"];
        }

        $model = new ItineraryOrderModel();
        $result = $model->getList($where, $page, $pageSize);

        return success($result);
    }

    /**
     * 获取订单详情
     * @param Request $request
     * @param string $id
     * @return Response
     */
    public function detail(Request $request, string $id = ''): Response
    {
        // 尝试从路径获取ID，如果没有则从请求参数中获取
        if (empty($id)) {
            $id = $request->input('id');
        }

        if (!$id) {
            return error(400, '参数错误');
        }

        $model = new ItineraryOrderModel();
        $detail = $model->getDetail($id);

        if (!$detail) {
            return error(404, '订单不存在');
        }

        return success($detail);
    }

    /**
     * 创建订单
     * @param Request $request
     * @return Response
     */
    public function create(Request $request): Response
    {
        $data = $request->post();

        // 验证参数
        if (empty($data['itineraryId']) ||
            empty($data['name']) || empty($data['phone']) || 
            empty($data['travelDate']) || !isset($data['adultCount'])) {
            return error(400, '参数错误');
        }

        // 开启事务
        Db::startTrans();
        try {
            // 验证行程是否存在
            $itineraryModel = new ItineraryModel();
            $itinerary = $itineraryModel->where('id', $data['itineraryId'])->where('delete_time', 0)->find();
            
            if (!$itinerary) {
                return error(404, '行程不存在');
            }
            
            // 验证行程状态是否为上架状态
            if ($itinerary['status'] !== 'online') {
                return error(400, '该行程未上架，无法预订');
            }
            
            // 检查库存
            $inventoryModel = new ItineraryInventoryModel();
            $inventory = $inventoryModel->where('itinerary_id', $data['itineraryId'])
                ->where('inventory_date', $data['travelDate'])
                ->where('status', '1') // 可预约状态
                ->find();
                
            if (!$inventory) {
                return error(400, '所选日期不可预订');
            }
            
            // 检查库存是否足够
            $totalPeople = ($data['adultCount']) + ($data['childCount'] ?? 0);
            if ($inventory['total_amount'] !== null) { // 如果不是无限库存
                $availableQuantity = $inventory['total_amount'] - $inventory['booked_amount'];
                if ($availableQuantity < $totalPeople) {
                    return error(400, '库存不足，剩余' . $availableQuantity . '个位置');
                }
            }
            
            // 准备订单数据
            $orderData = [
                'itinerary_id' => $data['itineraryId'],
                'user_id' => $data['userId'],
                'name' => $data['name'],
                'phone' => $data['phone'],
                'travel_date' => $data['travelDate'],
                'adult_count' => $data['adultCount'],
                'child_count' => $data['childCount'] ?? 0,
                'base_price' => $itinerary['price'],
                // 新增房间相关字段
                'room_count' => $data['roomCount'] ?? 0,
                'single_room_count' => $data['singleRoomCount'] ?? 0,
                'upgrade_room_count' => $data['upgradeRoomCount'] ?? 0,
                'single_room_surcharge' => $data['singleRoomSurcharge'] ?? 0,
                'upgrade_room_surcharge' => $data['upgradeRoomSurcharge'] ?? 0,
                'remark' => $data['remark'] ?? '',
            ];
            
            // 处理加价信息
            // 允许自定义热门日期加价
            $orderData['hot_date_surcharge'] = isset($data['hotDateSurcharge']) ? 
                (float)$data['hotDateSurcharge'] : 
                ($inventory['has_hot_date_surcharge'] ? (float)$inventory['hot_date_surcharge_amount'] : 0);
            
            // 允许自定义成人和儿童加价
            $orderData['adult_surcharge'] = isset($data['adultSurcharge']) ? 
                (float)$data['adultSurcharge'] : 
                ($inventory['has_traveler_surcharge'] ? (float)$inventory['adult_surcharge_amount'] : 0);
                
            $orderData['child_surcharge'] = isset($data['childSurcharge']) ? 
                (float)$data['childSurcharge'] : 
                ($inventory['has_traveler_surcharge'] ? (float)$inventory['child_surcharge_amount'] : 0);
            
            // 如果前端直接提供了总金额，则使用前端提供的金额
            if (isset($data['totalAmount']) && $data['totalAmount'] > 0) {
                $orderData['total_amount'] = (float)$data['totalAmount'];
            } else {
                // 否则自动计算总金额
                $totalAmount = $orderData['base_price'] * $orderData['adult_count'];
                
                // 添加儿童基础费用（如果不同的话应该另外处理）
                $totalAmount += $orderData['base_price'] * $orderData['child_count'];
                
                // 添加热门日期加价
                $totalAmount += $orderData['hot_date_surcharge'];
                
                // 添加成人和儿童加价
                $totalAmount += $orderData['adult_surcharge'] * ($orderData['adult_count'] -1 );
                $totalAmount += $orderData['child_surcharge'] * $orderData['child_count'];
                
                // 添加房间相关的费用
                $totalAmount += $orderData['single_room_count'] * $orderData['single_room_surcharge'];
                $totalAmount += $orderData['upgrade_room_count'] * $orderData['upgrade_room_surcharge'];
                
                $orderData['total_amount'] = $totalAmount;
            }
            
            // 创建订单
            $orderModel = new ItineraryOrderModel();
            $orderId = $orderModel->createOrder($orderData);
            
            // 更新库存
            if ($inventory['total_amount'] !== null) { // 如果不是无限库存
                $inventory->save([
                    'booked_amount' => $inventory['booked_amount'] + $totalPeople,
                    'update_time' => time()
                ]);
            }
            
            Db::commit();
            
            return success([
                'id' => $orderId,
                'orderNo' => $orderModel->where('id', $orderId)->value('order_no')
            ], '创建成功');
        } catch (\Exception $e) {
            Db::rollback();
            return error(500, '创建失败：' . $e->getMessage());
        }
    }

    /**
     * 更新订单状态
     * @param Request $request
     * @return Response
     */
    public function updateStatus(Request $request): Response
    {
        $id = $request->input('id');
        $status = $request->input('status');

        if (!$id || !$status) {
            return error(400, '参数错误');
        }

        // 验证状态是否合法
        $validStatus = ['pending', 'paid', 'cancelled', 'completed'];
        if (!in_array($status, $validStatus)) {
            return error(400, '状态值无效');
        }

        // 获取订单信息
        $model = new ItineraryOrderModel();
        $order = $model->where('id', $id)->where('delete_time', 0)->find();
        
        if (!$order) {
            return error(404, '订单不存在');
        }
        
        // 开启事务
        Db::startTrans();
        try {
            // 特殊状态处理
            if ($status === 'cancelled' && $order['status'] === 'pending') {
                // 如果是取消未支付订单，需要返还库存
                $totalPeople = $order['adult_count'] + $order['child_count'];
                
                $inventoryModel = new ItineraryInventoryModel();
                $inventory = $inventoryModel->where('itinerary_id', $order['itinerary_id'])
                    ->where('inventory_date', $order['travel_date'])
                    ->where('delete_time', 0)
                    ->find();
                    
                if ($inventory && $inventory['total_amount'] !== null) {
                    // 更新库存
                    $inventory->save([
                        'booked_amount' => max(0, $inventory['booked_amount'] - $totalPeople),
                        'update_time' => time()
                    ]);
                }
            }
            
            // 更新订单状态
            $result = $model->updateStatus($id, $status);
            
            Db::commit();
            
            if ($result) {
                return success([], '状态更新成功');
            } else {
                return error(500, '状态更新失败');
            }
        } catch (\Exception $e) {
            Db::rollback();
            return error(500, '更新失败：' . $e->getMessage());
        }
    }

    /**
     * 删除订单
     * @param Request $request
     * @param string $id
     * @return Response
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function delete(Request $request, string $id = ''): Response
    {
        // 尝试从路径获取ID，如果没有则从请求参数中获取
        if (empty($id)) {
            $id = $request->input('id');
        }

        if (!$id) {
            return error(400, '参数错误');
        }

        // 验证订单是否存在
        $model = new ItineraryOrderModel();
        $order = $model->where('id', $id)->where('delete_time', 0)->find();
        
        if (!$order) {
            return error(404, '订单不存在');
        }
        
        // 逻辑删除
        $result = $model->where('id', $id)->update([
            'delete_time' => time()
        ]);

        if ($result) {
            return success([], '删除成功');
        } else {
            return error(500, '删除失败');
        }
    }

    /**
     * 批量删除订单
     * @param Request $request
     * @return Response
     */
    public function batchDelete(Request $request): Response
    {
        $ids = $request->post('ids', []);

        if (empty($ids) || !is_array($ids)) {
            return error(400, '参数错误');
        }

        $model = new ItineraryOrderModel();
        
        // 逻辑删除
        $result = $model->whereIn('id', $ids)->update([
            'delete_time' => time()
        ]);

        return success(['count' => $result], '批量删除成功');
    }

    /**
     * 取消订单
     * @param Request $request
     * @param string $id
     * @return Response
     */
    public function cancel(Request $request, string $id = ''): Response
    {
        // 尝试从路径获取ID，如果没有则从请求参数中获取
        if (empty($id)) {
            $id = $request->input('id');
        }

        if (!$id) {
            return error(400, '参数错误');
        }

        // 获取订单信息
        $model = new ItineraryOrderModel();
        $order = $model->where('id', $id)->where('delete_time', 0)->find();
        
        if (!$order) {
            return error(404, '订单不存在');
        }
        
        // 只有待支付或已支付的订单才能取消
        if (!in_array($order['status'], ['pending', 'paid'])) {
            return error(400, '当前状态不允许取消订单');
        }
        
        // 开启事务
        Db::startTrans();
        try {
            // 归还库存
            $totalPeople = $order['adult_count'] + $order['child_count'];
            
            $inventoryModel = new ItineraryInventoryModel();
            $inventory = $inventoryModel->where('itinerary_id', $order['itinerary_id'])
                ->where('inventory_date', $order['travel_date'])
                ->where('delete_time', 0)
                ->find();
                
            if ($inventory && $inventory['total_amount'] !== null) {
                // 更新库存
                $inventory->save([
                    'booked_amount' => max(0, $inventory['booked_amount'] - $totalPeople),
                    'update_time' => time()
                ]);
            }
            
            // 更新订单状态
            $result = $model->updateStatus($id, 'cancelled');
            
            Db::commit();
            
            if ($result) {
                return success([], '订单已取消');
            } else {
                return error(500, '取消订单失败');
            }
        } catch (\Exception $e) {
            Db::rollback();
            return error(500, '取消失败：' . $e->getMessage());
        }
    }

    /**
     * 完成订单
     * @param Request $request
     * @param string $id
     * @return Response
     */
    public function complete(Request $request, string $id = ''): Response
    {
        // 尝试从路径获取ID，如果没有则从请求参数中获取
        if (empty($id)) {
            $id = $request->input('id');
        }

        if (!$id) {
            return error(400, '参数错误');
        }

        // 获取订单信息
        $model = new ItineraryOrderModel();
        $order = $model->where('id', $id)->where('delete_time', 0)->find();
        
        if (!$order) {
            return error(404, '订单不存在');
        }
        
        // 只有已支付的订单才能标记为完成
        if ($order['status'] !== 'paid') {
            return error(400, '只有已支付的订单才能标记为完成');
        }
        
        // 更新订单状态
        $result = $model->updateStatus($id, 'completed');
        
        if ($result) {
            return success([], '订单已完成');
        } else {
            return error(500, '完成订单失败');
        }
    }

    /**
     * 支付订单
     * @param Request $request
     * @param string $id
     * @return Response
     */
    public function pay(Request $request, string $id = ''): Response
    {
        // 尝试从路径获取ID，如果没有则从请求参数中获取
        if (empty($id)) {
            $id = $request->input('id');
        }

        if (!$id) {
            return error(400, '参数错误');
        }

        // 获取订单信息
        $model = new ItineraryOrderModel();
        $order = $model->where('id', $id)->where('delete_time', 0)->find();
        
        if (!$order) {
            return error(404, '订单不存在');
        }
        
        // 只有待支付的订单才能支付
        if ($order['status'] !== 'pending') {
            return error(400, '只有待支付的订单才能进行支付');
        }
        
        // 更新订单状态
        $result = $model->updateStatus($id, 'paid');
        
        if ($result) {
            return success([], '订单支付成功');
        } else {
            return error(500, '订单支付失败');
        }
    }
} 