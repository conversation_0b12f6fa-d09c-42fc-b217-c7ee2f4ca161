[2025-08-13 16:37:07] default.ERROR: 127.0.0.1 POST 127.0.0.1:8899/api/booking/order
TypeError: bcmul(): Argument #1 ($num1) must be of type string, float given in D:\work\hotel\all\itinerary-api\app\service\BookingService.php:41
Stack trace:
#0 D:\work\hotel\all\itinerary-api\app\service\BookingService.php(41): bcmul(17999.0, 2, 2)
#1 D:\work\hotel\all\itinerary-api\app\api\controller\Booking.php(58): app\service\BookingService->createOrder('16', 2, 2)
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\App.php(336): app\api\controller\Booking->createOrder(Object(support\Request))
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\App.php(359): Webman\App::Webman\{closure}(Object(support\Request))
#4 D:\work\hotel\all\itinerary-api\app\middleware\UserAuth.php(64): Webman\App::Webman\{closure}(Object(support\Request))
#5 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\App.php(352): app\middleware\UserAuth->process(Object(support\Request), Object(Closure))
#6 D:\work\hotel\all\itinerary-api\base\middleware\AcceptLanguage.php(81): Webman\App::Webman\{closure}(Object(support\Request))
#7 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\App.php(352): base\middleware\AcceptLanguage->process(Object(support\Request), Object(Closure))
#8 D:\work\hotel\all\itinerary-api\base\middleware\AccessControlTest.php(13): Webman\App::Webman\{closure}(Object(support\Request))
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\App.php(352): base\middleware\AccessControlTest->process(Object(support\Request), Object(Closure))
#10 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\App.php(150): Webman\App::Webman\{closure}(Object(support\Request))
#11 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Connection\TcpConnection.php(750): Webman\App->onMessage(Object(Workerman\Connection\TcpConnection), Object(support\Request))
#12 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Events\Select.php(406): Workerman\Connection\TcpConnection->baseRead(Resource id #348)
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1613): Workerman\Events\Select->run()
#14 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#15 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#16 D:\work\hotel\all\itinerary-api\runtime\windows\start_webman.php(33): Workerman\Worker::runAll()
#17 {main} [] []
[2025-08-13 16:37:37] default.ERROR: 127.0.0.1 POST 127.0.0.1:8899/api/booking/order
TypeError: bcmul(): Argument #1 ($num1) must be of type string, float given in D:\work\hotel\all\itinerary-api\app\service\BookingService.php:41
Stack trace:
#0 D:\work\hotel\all\itinerary-api\app\service\BookingService.php(41): bcmul(17999.0, '2', 2)
#1 D:\work\hotel\all\itinerary-api\app\api\controller\Booking.php(58): app\service\BookingService->createOrder('16', 2, 2)
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\App.php(336): app\api\controller\Booking->createOrder(Object(support\Request))
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\App.php(359): Webman\App::Webman\{closure}(Object(support\Request))
#4 D:\work\hotel\all\itinerary-api\app\middleware\UserAuth.php(64): Webman\App::Webman\{closure}(Object(support\Request))
#5 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\App.php(352): app\middleware\UserAuth->process(Object(support\Request), Object(Closure))
#6 D:\work\hotel\all\itinerary-api\base\middleware\AcceptLanguage.php(81): Webman\App::Webman\{closure}(Object(support\Request))
#7 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\App.php(352): base\middleware\AcceptLanguage->process(Object(support\Request), Object(Closure))
#8 D:\work\hotel\all\itinerary-api\base\middleware\AccessControlTest.php(13): Webman\App::Webman\{closure}(Object(support\Request))
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\App.php(352): base\middleware\AccessControlTest->process(Object(support\Request), Object(Closure))
#10 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\App.php(663): Webman\App::Webman\{closure}(Object(support\Request))
#11 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\App.php(158): Webman\App::findRoute(Object(Workerman\Connection\TcpConnection), '/api/booking/or...', 'POST/api/bookin...', Object(support\Request), 200)
#12 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Connection\TcpConnection.php(750): Webman\App->onMessage(Object(Workerman\Connection\TcpConnection), Object(support\Request))
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Events\Select.php(406): Workerman\Connection\TcpConnection->baseRead(Resource id #334)
#14 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1613): Workerman\Events\Select->run()
#15 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#16 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#17 D:\work\hotel\all\itinerary-api\runtime\windows\start_webman.php(33): Workerman\Worker::runAll()
#18 {main} [] []
[2025-08-13 16:38:03] default.ERROR: 127.0.0.1 POST 127.0.0.1:8899/api/booking/order
TypeError: bcmul(): Argument #2 ($num2) must be of type string, int given in D:\work\hotel\all\itinerary-api\app\service\BookingService.php:41
Stack trace:
#0 D:\work\hotel\all\itinerary-api\app\service\BookingService.php(41): bcmul('17999', 2, 2)
#1 D:\work\hotel\all\itinerary-api\app\api\controller\Booking.php(58): app\service\BookingService->createOrder('16', 2, 2)
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\App.php(336): app\api\controller\Booking->createOrder(Object(support\Request))
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\App.php(359): Webman\App::Webman\{closure}(Object(support\Request))
#4 D:\work\hotel\all\itinerary-api\app\middleware\UserAuth.php(64): Webman\App::Webman\{closure}(Object(support\Request))
#5 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\App.php(352): app\middleware\UserAuth->process(Object(support\Request), Object(Closure))
#6 D:\work\hotel\all\itinerary-api\base\middleware\AcceptLanguage.php(81): Webman\App::Webman\{closure}(Object(support\Request))
#7 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\App.php(352): base\middleware\AcceptLanguage->process(Object(support\Request), Object(Closure))
#8 D:\work\hotel\all\itinerary-api\base\middleware\AccessControlTest.php(13): Webman\App::Webman\{closure}(Object(support\Request))
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\App.php(352): base\middleware\AccessControlTest->process(Object(support\Request), Object(Closure))
#10 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\App.php(663): Webman\App::Webman\{closure}(Object(support\Request))
#11 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\App.php(158): Webman\App::findRoute(Object(Workerman\Connection\TcpConnection), '/api/booking/or...', 'POST/api/bookin...', Object(support\Request), 200)
#12 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Connection\TcpConnection.php(750): Webman\App->onMessage(Object(Workerman\Connection\TcpConnection), Object(support\Request))
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Events\Select.php(406): Workerman\Connection\TcpConnection->baseRead(Resource id #334)
#14 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1613): Workerman\Events\Select->run()
#15 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#16 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#17 D:\work\hotel\all\itinerary-api\runtime\windows\start_webman.php(33): Workerman\Worker::runAll()
#18 {main} [] []
[2025-08-13 16:43:00] default.ERROR: 127.0.0.1 POST 127.0.0.1:8899/api/booking/reservation
TypeError: count(): Argument #1 ($value) must be of type Countable|array, int given in D:\work\hotel\all\itinerary-api\app\api\controller\Booking.php:144
Stack trace:
#0 D:\work\hotel\all\itinerary-api\app\api\controller\Booking.php(144): count(1)
#1 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\App.php(336): app\api\controller\Booking->createReservation(Object(support\Request))
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\App.php(359): Webman\App::Webman\{closure}(Object(support\Request))
#3 D:\work\hotel\all\itinerary-api\app\middleware\UserAuth.php(64): Webman\App::Webman\{closure}(Object(support\Request))
#4 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\App.php(352): app\middleware\UserAuth->process(Object(support\Request), Object(Closure))
#5 D:\work\hotel\all\itinerary-api\base\middleware\AcceptLanguage.php(81): Webman\App::Webman\{closure}(Object(support\Request))
#6 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\App.php(352): base\middleware\AcceptLanguage->process(Object(support\Request), Object(Closure))
#7 D:\work\hotel\all\itinerary-api\base\middleware\AccessControlTest.php(13): Webman\App::Webman\{closure}(Object(support\Request))
#8 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\App.php(352): base\middleware\AccessControlTest->process(Object(support\Request), Object(Closure))
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\App.php(663): Webman\App::Webman\{closure}(Object(support\Request))
#10 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\App.php(158): Webman\App::findRoute(Object(Workerman\Connection\TcpConnection), '/api/booking/re...', 'POST/api/bookin...', Object(support\Request), 200)
#11 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Connection\TcpConnection.php(750): Webman\App->onMessage(Object(Workerman\Connection\TcpConnection), Object(support\Request))
#12 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Events\Select.php(406): Workerman\Connection\TcpConnection->baseRead(Resource id #334)
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1613): Workerman\Events\Select->run()
#14 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#15 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#16 D:\work\hotel\all\itinerary-api\runtime\windows\start_webman.php(33): Workerman\Worker::runAll()
#17 {main} [] []
[2025-08-13 17:22:02] default.ERROR: 127.0.0.1 GET 127.0.0.1:8899/admin/booking/9
TypeError: app\admin\controller\BookingController::show(): Return value must be of type support\Response, none returned in D:\work\hotel\all\itinerary-api\app\admin\controller\BookingController.php:303
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\App.php(336): app\admin\controller\BookingController->show(Object(support\Request), '9')
#1 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\App.php(359): Webman\App::Webman\{closure}(Object(support\Request), '9')
#2 D:\work\hotel\all\itinerary-api\base\middleware\AcceptLanguage.php(81): Webman\App::Webman\{closure}(Object(support\Request))
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\App.php(352): base\middleware\AcceptLanguage->process(Object(support\Request), Object(Closure))
#4 D:\work\hotel\all\itinerary-api\base\middleware\AccessControlTest.php(13): Webman\App::Webman\{closure}(Object(support\Request))
#5 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\App.php(352): base\middleware\AccessControlTest->process(Object(support\Request), Object(Closure))
#6 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\App.php(663): Webman\App::Webman\{closure}(Object(support\Request))
#7 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\App.php(158): Webman\App::findRoute(Object(Workerman\Connection\TcpConnection), '/admin/booking/...', 'GET/admin/booki...', Object(support\Request), 200)
#8 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Connection\TcpConnection.php(750): Webman\App->onMessage(Object(Workerman\Connection\TcpConnection), Object(support\Request))
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Events\Select.php(406): Workerman\Connection\TcpConnection->baseRead(Resource id #334)
#10 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1613): Workerman\Events\Select->run()
#11 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#12 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#13 D:\work\hotel\all\itinerary-api\runtime\windows\start_webman.php(33): Workerman\Worker::runAll()
#14 {main} [] []
[2025-08-13 17:37:28] default.ERROR: 接单失败 {"error":"Undefined array key \"order_id\"","file":"D:\\work\\hotel\\all\\itinerary-api\\app\\service\\BookingService.php","line":493,"trace":"#0 D:\\work\\hotel\\all\\itinerary-api\\app\\service\\BookingService.php(493): {closure}(2, 'Undefined array...', 'D:\\\\work\\\\hotel\\\\a...', 493)
#1 D:\\work\\hotel\\all\\itinerary-api\\app\\admin\\controller\\BookingController.php(323): app\\service\\BookingService->receiveBooking(3)
#2 D:\\work\\hotel\\all\\itinerary-api\\vendor\\workerman\\webman-framework\\src\\App.php(336): app\\admin\\controller\\BookingController->receiveBooking(Object(support\\Request))
#3 D:\\work\\hotel\\all\\itinerary-api\\vendor\\workerman\\webman-framework\\src\\App.php(359): Webman\\App::Webman\\{closure}(Object(support\\Request))
#4 D:\\work\\hotel\\all\\itinerary-api\\base\\middleware\\AcceptLanguage.php(81): Webman\\App::Webman\\{closure}(Object(support\\Request))
#5 D:\\work\\hotel\\all\\itinerary-api\\vendor\\workerman\\webman-framework\\src\\App.php(352): base\\middleware\\AcceptLanguage->process(Object(support\\Request), Object(Closure))
#6 D:\\work\\hotel\\all\\itinerary-api\\base\\middleware\\AccessControlTest.php(13): Webman\\App::Webman\\{closure}(Object(support\\Request))
#7 D:\\work\\hotel\\all\\itinerary-api\\vendor\\workerman\\webman-framework\\src\\App.php(352): base\\middleware\\AccessControlTest->process(Object(support\\Request), Object(Closure))
#8 D:\\work\\hotel\\all\\itinerary-api\\vendor\\workerman\\webman-framework\\src\\App.php(663): Webman\\App::Webman\\{closure}(Object(support\\Request))
#9 D:\\work\\hotel\\all\\itinerary-api\\vendor\\workerman\\webman-framework\\src\\App.php(158): Webman\\App::findRoute(Object(Workerman\\Connection\\TcpConnection), '/admin/booking/...', 'POST/admin/book...', Object(support\\Request), 200)
#10 D:\\work\\hotel\\all\\itinerary-api\\vendor\\workerman\\workerman\\src\\Connection\\TcpConnection.php(750): Webman\\App->onMessage(Object(Workerman\\Connection\\TcpConnection), Object(support\\Request))
#11 D:\\work\\hotel\\all\\itinerary-api\\vendor\\workerman\\workerman\\src\\Events\\Select.php(406): Workerman\\Connection\\TcpConnection->baseRead(Resource id #334)
#12 D:\\work\\hotel\\all\\itinerary-api\\vendor\\workerman\\workerman\\src\\Worker.php(1613): Workerman\\Events\\Select->run()
#13 D:\\work\\hotel\\all\\itinerary-api\\vendor\\workerman\\workerman\\src\\Worker.php(1528): Workerman\\Worker::forkWorkersForWindows()
#14 D:\\work\\hotel\\all\\itinerary-api\\vendor\\workerman\\workerman\\src\\Worker.php(594): Workerman\\Worker::forkWorkers()
#15 D:\\work\\hotel\\all\\itinerary-api\\runtime\\windows\\start_webman.php(33): Workerman\\Worker::runAll()
#16 {main}"} []
[2025-08-13 17:39:04] default.ERROR: 接单失败 {"error":"预约已接单，无需重复操作","file":"D:\\work\\hotel\\all\\itinerary-api\\app\\service\\BookingService.php","line":480,"trace":"#0 D:\\work\\hotel\\all\\itinerary-api\\app\\admin\\controller\\BookingController.php(323): app\\service\\BookingService->receiveBooking(3)
#1 D:\\work\\hotel\\all\\itinerary-api\\vendor\\workerman\\webman-framework\\src\\App.php(336): app\\admin\\controller\\BookingController->receiveBooking(Object(support\\Request))
#2 D:\\work\\hotel\\all\\itinerary-api\\vendor\\workerman\\webman-framework\\src\\App.php(359): Webman\\App::Webman\\{closure}(Object(support\\Request))
#3 D:\\work\\hotel\\all\\itinerary-api\\base\\middleware\\AcceptLanguage.php(81): Webman\\App::Webman\\{closure}(Object(support\\Request))
#4 D:\\work\\hotel\\all\\itinerary-api\\vendor\\workerman\\webman-framework\\src\\App.php(352): base\\middleware\\AcceptLanguage->process(Object(support\\Request), Object(Closure))
#5 D:\\work\\hotel\\all\\itinerary-api\\base\\middleware\\AccessControlTest.php(13): Webman\\App::Webman\\{closure}(Object(support\\Request))
#6 D:\\work\\hotel\\all\\itinerary-api\\vendor\\workerman\\webman-framework\\src\\App.php(352): base\\middleware\\AccessControlTest->process(Object(support\\Request), Object(Closure))
#7 D:\\work\\hotel\\all\\itinerary-api\\vendor\\workerman\\webman-framework\\src\\App.php(663): Webman\\App::Webman\\{closure}(Object(support\\Request))
#8 D:\\work\\hotel\\all\\itinerary-api\\vendor\\workerman\\webman-framework\\src\\App.php(158): Webman\\App::findRoute(Object(Workerman\\Connection\\TcpConnection), '/admin/booking/...', 'POST/admin/book...', Object(support\\Request), 200)
#9 D:\\work\\hotel\\all\\itinerary-api\\vendor\\workerman\\workerman\\src\\Connection\\TcpConnection.php(750): Webman\\App->onMessage(Object(Workerman\\Connection\\TcpConnection), Object(support\\Request))
#10 D:\\work\\hotel\\all\\itinerary-api\\vendor\\workerman\\workerman\\src\\Events\\Select.php(406): Workerman\\Connection\\TcpConnection->baseRead(Resource id #334)
#11 D:\\work\\hotel\\all\\itinerary-api\\vendor\\workerman\\workerman\\src\\Worker.php(1613): Workerman\\Events\\Select->run()
#12 D:\\work\\hotel\\all\\itinerary-api\\vendor\\workerman\\workerman\\src\\Worker.php(1528): Workerman\\Worker::forkWorkersForWindows()
#13 D:\\work\\hotel\\all\\itinerary-api\\vendor\\workerman\\workerman\\src\\Worker.php(594): Workerman\\Worker::forkWorkers()
#14 D:\\work\\hotel\\all\\itinerary-api\\runtime\\windows\\start_webman.php(33): Workerman\\Worker::runAll()
#15 {main}"} []
[2025-08-13 17:58:03] default.ERROR: 接单失败 {"error":"预约已处理","file":"D:\\work\\hotel\\all\\itinerary-api\\app\\service\\BookingService.php","line":474,"trace":"#0 D:\\work\\hotel\\all\\itinerary-api\\app\\admin\\controller\\BookingController.php(329): app\\service\\BookingService->receiveBooking(3, 'refused', '\\xE8\\xBA\\xAB\\xE4\\xBB\\xBD\\xE8\\xAF\\x81\\xE4\\xB8\\x8D\\xE7\\xAC\\xA6...')
#1 D:\\work\\hotel\\all\\itinerary-api\\vendor\\workerman\\webman-framework\\src\\App.php(336): app\\admin\\controller\\BookingController->receiveBooking(Object(support\\Request))
#2 D:\\work\\hotel\\all\\itinerary-api\\vendor\\workerman\\webman-framework\\src\\App.php(359): Webman\\App::Webman\\{closure}(Object(support\\Request))
#3 D:\\work\\hotel\\all\\itinerary-api\\base\\middleware\\AcceptLanguage.php(81): Webman\\App::Webman\\{closure}(Object(support\\Request))
#4 D:\\work\\hotel\\all\\itinerary-api\\vendor\\workerman\\webman-framework\\src\\App.php(352): base\\middleware\\AcceptLanguage->process(Object(support\\Request), Object(Closure))
#5 D:\\work\\hotel\\all\\itinerary-api\\base\\middleware\\AccessControlTest.php(13): Webman\\App::Webman\\{closure}(Object(support\\Request))
#6 D:\\work\\hotel\\all\\itinerary-api\\vendor\\workerman\\webman-framework\\src\\App.php(352): base\\middleware\\AccessControlTest->process(Object(support\\Request), Object(Closure))
#7 D:\\work\\hotel\\all\\itinerary-api\\vendor\\workerman\\webman-framework\\src\\App.php(663): Webman\\App::Webman\\{closure}(Object(support\\Request))
#8 D:\\work\\hotel\\all\\itinerary-api\\vendor\\workerman\\webman-framework\\src\\App.php(158): Webman\\App::findRoute(Object(Workerman\\Connection\\TcpConnection), '/admin/booking/...', 'POST/admin/book...', Object(support\\Request), 200)
#9 D:\\work\\hotel\\all\\itinerary-api\\vendor\\workerman\\workerman\\src\\Connection\\TcpConnection.php(750): Webman\\App->onMessage(Object(Workerman\\Connection\\TcpConnection), Object(support\\Request))
#10 D:\\work\\hotel\\all\\itinerary-api\\vendor\\workerman\\workerman\\src\\Events\\Select.php(406): Workerman\\Connection\\TcpConnection->baseRead(Resource id #334)
#11 D:\\work\\hotel\\all\\itinerary-api\\vendor\\workerman\\workerman\\src\\Worker.php(1613): Workerman\\Events\\Select->run()
#12 D:\\work\\hotel\\all\\itinerary-api\\vendor\\workerman\\workerman\\src\\Worker.php(1528): Workerman\\Worker::forkWorkersForWindows()
#13 D:\\work\\hotel\\all\\itinerary-api\\vendor\\workerman\\workerman\\src\\Worker.php(594): Workerman\\Worker::forkWorkers()
#14 D:\\work\\hotel\\all\\itinerary-api\\runtime\\windows\\start_webman.php(33): Workerman\\Worker::runAll()
#15 {main}"} []
