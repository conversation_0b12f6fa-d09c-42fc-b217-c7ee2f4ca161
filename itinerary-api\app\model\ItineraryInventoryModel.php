<?php

declare(strict_types=1);

namespace app\model;

use base\base\BaseModel;

class ItineraryInventoryModel extends BaseModel
{
    /**
     * 设置表名
     * @var string
     */
    protected $name = 'itinerary_inventory';

    /**
     * 设置主键
     * @var string
     */
    protected $pk = 'id';

    /**
     * 允许写入的字段
     * @var array
     */
    protected $field = [
        'id',
        'itinerary_id',
        'inventory_date',
        'status',
        'total_amount',
        'booked_amount',
        'rule_id',
        'has_hot_date_surcharge',
        'hot_date_surcharge_amount',
        'has_traveler_surcharge',
        'adult_surcharge_amount',
        'child_surcharge_amount',
        'create_time',
        'update_time',
        'delete_time'
    ];

    /**
     * 类型转换
     * @var array
     */
    protected $type = [
        'id' => 'integer',
        'inventory_date' => 'date',
        'status' => 'string',
        'total_amount' => 'integer',
        'booked_amount' => 'integer',
        'has_hot_date_surcharge' => 'boolean',
        'hot_date_surcharge_amount' => 'float',
        'has_traveler_surcharge' => 'boolean',
        'adult_surcharge_amount' => 'float',
        'child_surcharge_amount' => 'float',
        'create_time' => 'integer',
        'update_time' => 'integer',
        'delete_time' => 'integer'
    ];

    /**
     * 获取指定行程指定月份的库存数据
     * @param string $itineraryId 行程ID
     * @param string $month 月份，格式：YYYY-MM
     * @return array
     */
    public function getMonthInventory(string $itineraryId, string $month): array
    {
        // 月份起始日期和结束日期
        $startDate = $month . '-01';
        $endDate = date('Y-m-t', strtotime($startDate));
        
        // 查询该月的库存数据
        $inventoryList = $this->where('itinerary_id', $itineraryId)
            ->where('inventory_date', '>=', $startDate)
            ->where('inventory_date', '<=', $endDate)
            ->where('delete_time', 0)
            ->select()
            ->toArray();
        
        // 将查询结果转换为以日期为键的数组，方便后续处理
        $inventoryMap = [];
        foreach ($inventoryList as $inventory) {
            $date = $inventory['inventoryDate'];
            $inventoryMap[$date] = $inventory;
        }
        
        // 生成该月的所有日期，并填充库存数据
        $items = [];
        $currentDate = $startDate;
        while (strtotime($currentDate) <= strtotime($endDate)) {
            if (isset($inventoryMap[$currentDate])) {
                $inventory = $inventoryMap[$currentDate];
                $hasAdditionalPrice = ($inventory['hasHotDateSurcharge'] || $inventory['hasTravelerSurcharge']);
                
                $available = $inventory['totalAmount'] === null ? '不限' : 
                    max(0, $inventory['totalAmount'] - $inventory['bookedAmount']);
                
                $items[] = [
                    'date' => $currentDate,
                    'status' => (int)$inventory['status'],
                    'total' => $inventory['totalAmount'] === null ? '不限' : $inventory['totalAmount'],
                    'booked' => $inventory['bookedAmount'],
                    'available' => $available,
                    'hasAdditionalPrice' => $hasAdditionalPrice,
                    'hotDateSurcharge' => $inventory['hasHotDateSurcharge'] ? $inventory['hotDateSurchargeAmount'] : null,
                    'adultSurcharge' => $inventory['hasTravelerSurcharge'] ? $inventory['adultSurchargeAmount'] : null,
                    'childSurcharge' => $inventory['hasTravelerSurcharge'] ? $inventory['childSurchargeAmount'] : null,
                ];
            } else {
                // 如果没有库存数据，创建默认的库存数据
                $items[] = [
                    'date' => $currentDate,
                    'status' => 0,
                    'total' => '不限',
                    'booked' => 0,
                    'available' => '不限',
                    'hasAdditionalPrice' => false,
                ];
            }
            
            $currentDate = date('Y-m-d', strtotime($currentDate . ' +1 day'));
        }
        
        return $items;
    }

    /**
     * 获取指定日期的库存详情
     * @param string $itineraryId 行程ID
     * @param string $date 日期
     * @return array|null
     */
    public function getDateInventory(string $itineraryId, string $date): ?array
    {
        $inventory = $this->where('itinerary_id', $itineraryId)
            ->where('inventory_date', $date)
            ->where('delete_time', 0)
            ->find();
            
        if (!$inventory) {
            return [
                'date' => $date,
                'status' => 0,
                'total' => '不限',
                'booked' => 0,
                'available' => '不限',
                'hasAdditionalPrice' => false,
            ];
        }
        
        $hasAdditionalPrice = ($inventory['has_hot_date_surcharge'] || $inventory['has_traveler_surcharge']);
        $available = $inventory['total_amount'] === null ? '不限' : 
            max(0, $inventory['total_amount'] - $inventory['booked_amount']);
            
        return [
            'date' => $date,
            'status' => (int)$inventory['status'],
            'total' => $inventory['total_amount'] === null ? '不限' : $inventory['total_amount'],
            'booked' => $inventory['booked_amount'],
            'available' => $available,
            'hasAdditionalPrice' => $hasAdditionalPrice,
            'hotDateSurcharge' => $inventory['has_hot_date_surcharge'] ? $inventory['hot_date_surcharge_amount'] : null,
            'adultSurcharge' => $inventory['has_traveler_surcharge'] ? $inventory['adult_surcharge_amount'] : null,
            'childSurcharge' => $inventory['has_traveler_surcharge'] ? $inventory['child_surcharge_amount'] : null,
        ];
    }

    /**
     * 批量更新库存
     * @param string $itineraryId 行程ID
     * @param array $dates 日期列表
     * @param int $status 状态
     * @param int|null $totalAmount 总库存
     * @param array|null $surcharges 加价信息
     * @return bool
     */
    public function batchUpdate(string $itineraryId, array $dates, int $status, ?int $totalAmount, ?array $surcharges = null): bool
    {
        $now = time();
        
        foreach ($dates as $date) {
            // 查询该日期是否已有库存记录
            $inventory = $this->where('itinerary_id', $itineraryId)
                ->where('inventory_date', $date)
                ->where('delete_time', 0)
                ->find();
                
            $hasHotDateSurcharge = false;
            $hotDateSurchargeAmount = null;
            $hasTravelerSurcharge = false;
            $adultSurchargeAmount = null;
            $childSurchargeAmount = null;
            
            // 处理加价信息
            if ($surcharges) {
                if (isset($surcharges['hotDate']) && $surcharges['hotDate'] !== null) {
                    $hasHotDateSurcharge = true;
                    $hotDateSurchargeAmount = $surcharges['hotDate'];
                }
                
                if ((isset($surcharges['adult']) && $surcharges['adult'] !== null) ||
                    (isset($surcharges['child']) && $surcharges['child'] !== null)) {
                    $hasTravelerSurcharge = true;
                    $adultSurchargeAmount = $surcharges['adult'] ?? null;
                    $childSurchargeAmount = $surcharges['child'] ?? null;
                }
            }
            
            // 准备库存数据
            $inventoryData = [
                'itinerary_id' => $itineraryId,
                'inventory_date' => $date,
                'status' => (string)$status,
                'total_amount' => $totalAmount,
                'has_hot_date_surcharge' => $hasHotDateSurcharge,
                'hot_date_surcharge_amount' => $hotDateSurchargeAmount,
                'has_traveler_surcharge' => $hasTravelerSurcharge,
                'adult_surcharge_amount' => $adultSurchargeAmount,
                'child_surcharge_amount' => $childSurchargeAmount,
                'update_time' => $now,
            ];
            
            if ($inventory) {
                // 更新现有库存
                $inventory->save($inventoryData);
            } else {
                // 创建新库存
                $inventoryData['booked_amount'] = 0;
                $inventoryData['create_time'] = $now;
                $this->create($inventoryData);
            }
        }
        
        return true;
    }
} 