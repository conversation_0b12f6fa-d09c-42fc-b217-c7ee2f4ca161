<?php

namespace tests;

use base\AuditLog\AirParse;
use service\log\AuditLogDemo;

class TestConfig extends TestCase
{
    public function testAppConfig()
    {
        $config = config('app');
        self::assertIsArray($config);
        self::assertArray<PERSON><PERSON><PERSON>ey('debug', $config);
        self::assertIsBool($config['debug']);
        self::assertArrayHasKey('default_timezone', $config);
        self::assertIsString($config['default_timezone']);
    }

    public function testDate()
    {
      $TYPE =  env('DATABASE.TYPE');
      var_dump($TYPE);
        $newData = [
            'orderSn' => 'ASDASF' ,
            'type' => 2,
            'abc' => 'ERT',
            'items' => [[
                'orderItemSn' => 1,
                'num' => 2,
                'type' => 1,
            ]]
        ];
        $oldData = [
            'orderSn' => 123456,
            'type' => 1,
            'items' => [[
                'orderItemSn' => 2,
                'num' => 2,
                'type' => 1,
            ]]
        ];
        $info = AuditLogDemo::make()
            ->newData($newData)
            ->oldData($oldData)
            ->scene('update')
            ->setTraceId(123456)
            ->fetch();
//        var_dump($info);
        var_dump(
            (new AirParse($info))->get()
        );
    }
}