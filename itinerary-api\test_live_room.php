<?php

require_once __DIR__ . '/vendor/autoload.php';

use app\model\LiveRoomModel;
use app\model\LiveRoomProductModel;
use app\model\LiveRoomStatsModel;
use app\model\ItineraryModel;

// 初始化数据库连接
$config = require __DIR__ . '/config/think-orm.php';
\think\facade\Db::setConfig($config);

echo "=== 直播间功能测试 ===\n\n";

try {
    // 测试1: 创建直播间
    echo "1. 测试创建直播间...\n";
    $liveRoomModel = new LiveRoomModel();
    
    $liveRoomData = [
        'title' => '巴厘岛6天5晚纯玩无购物直播间',
        'description' => '专业导游带您体验巴厘岛最美风光，无购物纯玩团',
        'cover_image' => 'https://example.com/cover.jpg',
        'anchor_name' => '小美导游',
        'anchor_avatar' => 'https://example.com/avatar.jpg',
        'status' => LiveRoomModel::STATUS_UPCOMING,
        'scheduled_start_time' => date('Y-m-d H:i:s', strtotime('+1 hour')),
        'scheduled_end_time' => date('Y-m-d H:i:s', strtotime('+3 hours')),
        'is_featured' => 1,
        'sort_order' => 100,
        'tags' => ['巴厘岛', '海岛游', '纯玩团'],
        'notice' => '欢迎大家来到直播间，有任何问题可以随时提问！',
        'create_time' => time(),
        'update_time' => time()
    ];
    
    $liveRoomId = $liveRoomModel->insertGetId($liveRoomData);
    
    if ($liveRoomId) {
        echo "✓ 直播间创建成功，ID: {$liveRoomId}\n";
    } else {
        echo "✗ 直播间创建失败\n";
        exit(1);
    }
    
    // 测试2: 获取直播间详情
    echo "\n2. 测试获取直播间详情...\n";
    $liveRoom = $liveRoomModel->getDetail($liveRoomId);
    
    if ($liveRoom && $liveRoom['title'] === $liveRoomData['title']) {
        echo "✓ 直播间详情获取成功\n";
        echo "   标题: {$liveRoom['title']}\n";
        echo "   主播: {$liveRoom['anchor_name']}\n";
        echo "   状态: {$liveRoom['status']}\n";
    } else {
        echo "✗ 直播间详情获取失败\n";
    }
    
    // 测试3: 获取行程商品（假设存在ID为1的行程）
    echo "\n3. 测试添加商品到直播间...\n";
    $itineraryModel = new ItineraryModel();
    $itinerary = $itineraryModel->where('delete_time', 0)->find();
    
    if ($itinerary) {
        $productModel = new LiveRoomProductModel();
        $productData = [
            'live_room_id' => $liveRoomId,
            'product_id' => $itinerary->id,
            'product_type' => 'itinerary',
            'special_price' => $itinerary->price * 0.8, // 8折特价
            'discount_rate' => 80,
            'stock_limit' => 10,
            'is_featured' => 1,
            'sort_order' => 100,
            'status' => LiveRoomProductModel::STATUS_ACTIVE,
            'create_time' => time(),
            'update_time' => time()
        ];
        
        $productId = $productModel->insertGetId($productData);
        
        if ($productId) {
            echo "✓ 商品添加成功，ID: {$productId}\n";
            echo "   商品名称: {$itinerary->title}\n";
            echo "   原价: {$itinerary->price}\n";
            echo "   特价: {$productData['special_price']}\n";
        } else {
            echo "✗ 商品添加失败\n";
        }
    } else {
        echo "! 没有找到可用的行程商品，跳过商品测试\n";
    }
    
    // 测试4: 获取直播间商品列表
    echo "\n4. 测试获取直播间商品列表...\n";
    if (isset($productModel)) {
        $products = $productModel->getListByLiveRoom($liveRoomId);
        
        if (!empty($products['list'])) {
            echo "✓ 商品列表获取成功，共 {$products['total']} 个商品\n";
            foreach ($products['list'] as $product) {
                echo "   - 商品ID: {$product['product_id']}, 特价: {$product['special_price']}\n";
            }
        } else {
            echo "! 商品列表为空\n";
        }
    }
    
    // 测试5: 更新直播间状态
    echo "\n5. 测试更新直播间状态...\n";
    $result = $liveRoomModel->startLive($liveRoomId);
    
    if ($result) {
        echo "✓ 直播间状态更新成功（开始直播）\n";
        
        // 验证状态更新
        $updatedRoom = $liveRoomModel->find($liveRoomId);
        if ($updatedRoom->status === LiveRoomModel::STATUS_LIVE) {
            echo "✓ 状态验证成功: {$updatedRoom->status}\n";
        }
    } else {
        echo "✗ 直播间状态更新失败\n";
    }
    
    // 测试6: 更新观看人数
    echo "\n6. 测试更新观看人数...\n";
    $result = $liveRoomModel->updateViewerCount($liveRoomId, 1500);
    
    if ($result) {
        echo "✓ 观看人数更新成功\n";
        
        $updatedRoom = $liveRoomModel->find($liveRoomId);
        echo "   当前观看人数: {$updatedRoom->viewer_count}\n";
        echo "   最高观看人数: {$updatedRoom->max_viewer_count}\n";
    } else {
        echo "✗ 观看人数更新失败\n";
    }
    
    // 测试7: 点赞和分享
    echo "\n7. 测试点赞和分享功能...\n";
    $liveRoomModel->addLikeCount($liveRoomId, 100);
    $liveRoomModel->addShareCount($liveRoomId, 20);
    
    $updatedRoom = $liveRoomModel->find($liveRoomId);
    echo "✓ 点赞数: {$updatedRoom->like_count}\n";
    echo "✓ 分享数: {$updatedRoom->share_count}\n";
    
    // 测试8: 获取推荐直播间
    echo "\n8. 测试获取推荐直播间...\n";
    $featuredRooms = $liveRoomModel->getFeatured(5);
    
    if (!empty($featuredRooms)) {
        echo "✓ 推荐直播间获取成功，共 " . count($featuredRooms) . " 个\n";
        foreach ($featuredRooms as $room) {
            echo "   - {$room['title']} (观看: {$room['viewer_count']})\n";
        }
    } else {
        echo "! 没有推荐直播间\n";
    }
    
    // 测试9: 创建统计数据
    echo "\n9. 测试创建统计数据...\n";
    $statsModel = new LiveRoomStatsModel();
    $statsData = [
        'total_viewers' => 2000,
        'peak_viewers' => 1500,
        'total_likes' => 100,
        'total_shares' => 20,
        'total_orders' => 5,
        'total_sales' => 7500.00,
        'duration_minutes' => 120
    ];
    
    $result = $statsModel->createOrUpdate($liveRoomId, date('Y-m-d'), $statsData);
    
    if ($result) {
        echo "✓ 统计数据创建成功\n";
        
        // 获取统计汇总
        $summary = $statsModel->getSummary($liveRoomId);
        if (!empty($summary)) {
            echo "   总观看人数: {$summary['total_viewers']}\n";
            echo "   总销售额: {$summary['total_sales']}\n";
        }
    } else {
        echo "✗ 统计数据创建失败\n";
    }
    
    // 测试10: 结束直播
    echo "\n10. 测试结束直播...\n";
    $result = $liveRoomModel->endLive($liveRoomId);
    
    if ($result) {
        echo "✓ 直播结束成功\n";
        
        $updatedRoom = $liveRoomModel->find($liveRoomId);
        echo "   最终状态: {$updatedRoom->status}\n";
        echo "   结束时间: {$updatedRoom->actual_end_time}\n";
    } else {
        echo "✗ 直播结束失败\n";
    }
    
    echo "\n=== 所有测试完成 ===\n";
    echo "✓ 直播间功能测试通过\n";
    
} catch (\Exception $e) {
    echo "\n✗ 测试过程中发生错误: " . $e->getMessage() . "\n";
    echo "错误文件: " . $e->getFile() . "\n";
    echo "错误行号: " . $e->getLine() . "\n";
    exit(1);
}
