<?php

use GuzzleHttp\Client;
use tests\TestCase;

class TestHotelBeds extends TestCase
{
    public function testHeaders()
    {
        $apiKey = '46eaf6a122faea8e4ebbf0c58c8ba86a';
        $apiSecret = '6c9840c3de';
        $sign = hash('sha256', $apiKey . $apiSecret . time());
        $client = new Client([
            'verify' => false,
            'http_errors' => false,
        ]);

        $response = $client->get('https://api.test.hotelbeds.com/activity-booking-api/1.0/activities', [
            'query' => [
                "from" => "2025-05-15",
                "to" => "2025-05-16",
            ],
            'headers' => [
                'Accept' => 'application/json',
                'Accept-Encoding' => 'gzip',
                'Content-Type' => 'application/json',
                'Api-key' => $apiKey,
                'X-Signature' => $sign,
            ]
        ]);
        var_dump(json2Arr($response->getBody()));
    }
}