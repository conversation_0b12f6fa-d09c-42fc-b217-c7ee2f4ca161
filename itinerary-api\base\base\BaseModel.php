<?php

declare (strict_types=1);

namespace base\base;

use think\facade\Db;
use think\Model;

class BaseModel extends Model
{
    /**
     * 转数组时，全部转首字母小写驼峰
     * @var bool
     */

    protected $hidden = ['id','create_time', 'update_time', 'delete_time'];

    /**
     * @return BaseModel|Db
     */
    public static function mk(): self
    {
        return new static();
    }

    protected function getOptions(): array
    {
        return [
            'convertNameToCamel' => true, // 驼峰转换
            'autoWriteTimestamp' => true,
            'deleteTime' => 'delete_time',
            'defaultSoftDelete'    => 0, // 默认软删除字段的值
            'jsonAssoc'    => true,  // 设置JSON数据返回数组
        ];
    }
}