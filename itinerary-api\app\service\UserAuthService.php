<?php

declare(strict_types=1);

namespace app\service;

use app\model\UserModel;
use app\model\UserAuthModel;
use base\exception\BaseException;
use Firebase\JWT\JWT;

class UserAuthService
{
    /**
     * Register a new user
     *
     * @param array $data
     * @return array
     * @throws BaseException
     */
    public function register(array $data): array
    {
        // Validate required fields
        if (empty($data['username']) || empty($data['password'])) {
            throw new BaseException('用户名和密码不能为空', 400);
        }

        // Check if username already exists
        if (UserModel::getByUsername($data['username'])) {
            throw new BaseException('用户名已存在', 400);
        }

        // Check if email already exists
        if (!empty($data['email']) && UserModel::getByEmail($data['email'])) {
            throw new BaseException('邮箱已被使用', 400);
        }

        // Check if phone already exists
        if (!empty($data['phone']) && UserModel::getByPhone($data['phone'])) {
            throw new BaseException('手机号已被使用', 400);
        }

        // Register new user
        $user = UserModel::register($data);

        // Generate JWT token
        $token = $this->generateToken((int)$user->id);

        // Update login info
        $user->updateLoginInfo(request()->getRealIp());

        return [
            'token' => $token,
            'user' => $user
        ];
    }

    /**
     * Login with username and password
     *
     * @param string $username
     * @param string $password
     * @return array
     * @throws BaseException
     */
    public function login(string $username, string $password): array
    {
        // Find user by username, email or phone
        $user = UserModel::getByUsername($username);
        if (!$user) {
            $user = UserModel::getByEmail($username);
        }
        if (!$user) {
            $user = UserModel::getByPhone($username);
        }

        if (!$user) {
            throw new BaseException('用户不存在', 400);
        }

        // Check if account is active
        if ($user->status !== 1) {
            throw new BaseException('账号已被禁用', 400);
        }

        // Verify password
        if (!$user->verifyPassword($password)) {
            throw new BaseException('密码错误', 400);
        }

        // Generate JWT token
        $token = $this->generateToken($user->id);

        // Update login info
        $user->updateLoginInfo(request()->getRealIp());

        return [
            'token' => $token,
            'user' => $user
        ];
    }

    /**
     * Login or register with third-party auth
     *
     * @param string $platform
     * @param string $platformId
     * @param array $authData
     * @return array
     * @throws BaseException
     */
    public function authLogin(string $platform, string $platformId, array $authData): array
    {
        // Check if platform account exists
        $auth = UserAuthModel::getByPlatformAccount($platform, $platformId);

        if ($auth) {
            // Login with existing auth
            $user = UserModel::find($auth->user_id);
            if (!$user || $user->status !== 1) {
                throw new BaseException('账号已被禁用', 400);
            }

            // Update auth data
            $auth->save([
                'nickname' => $authData['nickname'] ?? $auth->nickname,
                'avatar' => $authData['avatar'] ?? $auth->avatar,
                'credentials' => isset($authData['credentials']) ? json_encode($authData['credentials']) : $auth->credentials,
            ]);
        } else {
            // Create new user from auth data
            $userData = [
                'username' => $platform . '_' . $platformId . '_' . time(),
                'password' => bin2hex(random_bytes(8)), // Random password
                'nickname' => $authData['nickname'] ?? '',
                'avatar' => $authData['avatar'] ?? '',
                'gender' => $authData['gender'] ?? 0,
            ];

            $user = UserModel::register($userData);

            // Create auth record
            UserAuthModel::create([
                'user_id' => $user->id,
                'platform' => $platform,
                'platform_id' => $platformId,
                'nickname' => $authData['nickname'] ?? '',
                'avatar' => $authData['avatar'] ?? '',
                'credentials' => isset($authData['credentials']) ? json_encode($authData['credentials']) : null,
            ]);
        }

        // Generate JWT token
        $token = $this->generateToken($user->id);

        // Update login info
        $user->updateLoginInfo(request()->getRealIp());

        return [
            'token' => $token,
            'user' => $user
        ];
    }

    /**
     * Generate JWT token
     *
     * @param int $userId
     * @return string
     */
    private function generateToken($userId): string
    {
        $now = time();
        $payload = [
            'iss' => 'itinerary-api', // Issuer
            'aud' => 'itinerary-app', // Audience
            'iat' => $now, // Issued at time
            'nbf' => $now, // Not before time
            'exp' => $now + config('app.jwt_ttl', 86400), // Expiration time
            'sub' => $userId, // Subject (user ID)
        ];

        return JWT::encode($payload, config('app.jwt_secret'), 'HS256');
    }
}
