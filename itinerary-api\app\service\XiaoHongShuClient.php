<?php
namespace app\service;

use GuzzleHttp\Client;
use support\Log;
use support\Redis;
use think\Exception;

class XiaoHongShuClient {
    // 小红书API基础URL
    const XHS_API_BASE_URL = 'https://miniapp.xiaohongshu.com/';
    const XHS_SANDBOX_URL = 'https://miniapp-sandbox.xiaohongshu.com/';

    // 获取token
    const API_RMP_TOKEN = 'api/rmp/token';
    // 授权登陆
    const API_RMP_SESSION = 'api/rmp/session';

    protected $appId;
    protected $appSecret;
    protected $isDebug = false; // 是否使用沙箱环境

    public function __construct() {
        $this->appId = env('XHS_APPID');
        $this->appSecret = env('XHS_SECRET');
        $this->isDebug = env('APP_DEBUG');
    }

    /**
     * 获取API基础URL
     * @return string
     */
    private function getApiBaseUrl()
    {
        return $this->isDebug ? self::XHS_SANDBOX_URL : self::XHS_API_BASE_URL;
    }

    /**
     * 发送HTTP请求到小红书API
     * @param string $endpoint API端点
     * @param array $params 请求参数
     * @param string $method 请求方法
     * @return array
     * @throws Exception
     */
    public function sendRequest($endpoint, $params = [], $method = 'POST')
    {
        $url = $this->getApiBaseUrl() . $endpoint;

        // 添加公共参数
        if (!in_array($endpoint, [self::API_RMP_TOKEN])) {
            $params['appid'] = $this->appId;
            $params['access_token'] = $this->getAccessToken();
        }

        $client = new Client([
            'verify'  => false,
            'timeout' => 3,
            'http_errors' => false,
        ]);
        try {
            if ($method === 'POST') {
                $response = $client->post($url, [
                    'headers' => ['Content-Type' => 'application/json', 'charset' => 'UTF-8'],
                    'json' => $params
                ]);
                Log::info("小红书API响应:" . json_encode([
                        'url' => $url,
                        'params' => json_encode($params),
                        'code' => $response->getStatusCode(),
                        'body' => json_decode($response->getBody(), true, 512, JSON_BIGINT_AS_STRING)
                    ], JSON_UNESCAPED_UNICODE));

                $result = $response->getBody();
                return json_decode($result, true);
            } else {
                // GET请求
                $response = $client->get($url . '?' . http_build_query($params));
                Log::info("小红书API(GET)响应:" . json_encode([
                        'url' => $url,
                        'params' => json_encode($params),
                        'code' => $response->getStatusCode(),
                        'body' => json_decode($response->getBody(), true, 512, JSON_BIGINT_AS_STRING)
                    ], JSON_UNESCAPED_UNICODE));
                $result = json_decode($response->getBody(), true);
                return $result ?: [];
            }

        } catch (Exception $e) {
            Log::error("小红书API请求异常: {$url}" . var_export([
                    'error' => $e->getMessage(),
                    'params' => $params,
                    'trace' => $e->getTraceAsString()
                ]));
            throw $e;
        }
    }

    /**
     * 获取access_token（带缓存）
     * @return string
     * @throws Exception
     */
    public function getAccessToken()
    {
        if (empty($this->appId) || empty($this->appSecret)) {
            throw new Exception("请正确配置小红书应用信息");
        }

        // 缓存key
        $cacheKey = 'xhs_access_token' . md5($this->appId);

        try {
            // 先尝试从缓存读取
            $cachedToken = Redis::get($cacheKey);
            if ($cachedToken) {
                Log::info('小红书access_token从缓存获取成功:' . $cachedToken);
                return $cachedToken;
            }

            // 缓存不存在，请求新的access_token
            Log::info('小红书access_token缓存不存在，重新获取');

            // 构建请求参数 - 根据小红书API文档调整
            $params = [
                'appid' => $this->appId,
                'secret' => $this->appSecret
            ];

            // 使用统一的请求方法
            $result = $this->sendRequest(self::API_RMP_TOKEN, $params, 'POST');

            if (!$result || (isset($result['success']) && $result['success'] == false)) {
                $error_msg = isset($result['error_description']) ? $result['error_description'] : '获取小红书access_token失败';
                throw new Exception($error_msg);
            }

            $accessToken = $result['data']['access_token'] ?? '';
            $expiresIn = $result['data']['expires_in'] ?? 7200;

            if (empty($accessToken)) {
                throw new Exception("获取到的access_token为空");
            }

            // 缓存access_token，提前60秒过期以避免边界问题
            $cacheTime = max($expiresIn - 60, 60);
            Redis::set($cacheKey, $accessToken, $cacheTime);

            Log::info('小红书access_token获取并缓存成功，有效期：' . $cacheTime . '秒');
            return $accessToken;

        } catch (Exception $e) {
            Log::error('获取小红书access_token失败：' . $e->getMessage());
            throw $e;
        } catch (\Exception $e) {
            Log::error('获取小红书access_token异常：' . $e->getMessage());
            throw new Exception("获取小红书access_token失败");
        }
    }
}
