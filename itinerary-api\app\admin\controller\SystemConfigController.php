<?php

namespace app\admin\controller;

use app\service\SystemConfigService;
use app\model\SystemConfig;
use support\Request;
use support\Response;

/**
 * 系统配置控制器
 * 对标携程EBooking的系统配置管理功能
 */
class SystemConfigController extends BaseController
{
    /**
     * 系统配置服务
     *
     * @var SystemConfigService
     */
    private $systemConfigService;

    public function __construct()
    {
        $this->systemConfigService = new SystemConfigService();
    }

    /**
     * 获取所有配置
     *
     * @param Request $request
     * @return Response
     */
    public function index(Request $request)
    {
        try {
            $params = $this->getInput($request);
            $includeEncrypted = $params['include_encrypted'] ?? false;

            $result = $this->systemConfigService->getAllConfigs($includeEncrypted);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '获取系统配置');
        }
    }

    /**
     * 获取分组配置
     *
     * @param Request $request
     * @param string $group
     * @return Response
     */
    public function getGroupConfigs(Request $request, $group)
    {
        try {
            if (empty($group)) {
                return error('配置分组不能为空');
            }

            $result = $this->systemConfigService->getGroupConfigs($group);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '获取分组配置');
        }
    }

    /**
     * 获取单个配置
     *
     * @param Request $request
     * @param string $key
     * @return Response
     */
    public function show(Request $request, $key)
    {
        try {
            if (empty($key)) {
                return error('配置键不能为空');
            }

            $result = $this->systemConfigService->getConfig($key);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '获取配置');
        }
    }

    /**
     * 创建配置
     *
     * @param Request $request
     * @return Response
     */
    public function store(Request $request)
    {
        try {
            $data = $this->getInput($request);

            // 验证必需参数
            $errors = $this->validateRequired($data, [
                'config_key', 'config_name', 'config_type', 'config_group'
            ]);
            if ($errors) {
                return error('参数验证失败', 400, $errors);
            }

            // 验证配置键格式
            if (!preg_match('/^[a-zA-Z][a-zA-Z0-9_\.]*$/', $data['config_key'])) {
                return error('配置键格式不正确，只能包含字母、数字、下划线和点号，且必须以字母开头');
            }

            // 验证配置类型
            $validTypes = [
                SystemConfig::TYPE_STRING, SystemConfig::TYPE_INTEGER, SystemConfig::TYPE_FLOAT,
                SystemConfig::TYPE_BOOLEAN, SystemConfig::TYPE_JSON, SystemConfig::TYPE_ARRAY,
                SystemConfig::TYPE_EMAIL, SystemConfig::TYPE_URL, SystemConfig::TYPE_PASSWORD,
                SystemConfig::TYPE_TEXT, SystemConfig::TYPE_SELECT, SystemConfig::TYPE_MULTISELECT,
                SystemConfig::TYPE_RADIO, SystemConfig::TYPE_CHECKBOX, SystemConfig::TYPE_DATE,
                SystemConfig::TYPE_DATETIME, SystemConfig::TYPE_TIME, SystemConfig::TYPE_FILE,
                SystemConfig::TYPE_IMAGE
            ];

            if (!in_array($data['config_type'], $validTypes)) {
                return error('无效的配置类型');
            }

            // 验证配置分组
            $validGroups = [
                SystemConfig::GROUP_SYSTEM, SystemConfig::GROUP_DATABASE, SystemConfig::GROUP_CACHE,
                SystemConfig::GROUP_EMAIL, SystemConfig::GROUP_SMS, SystemConfig::GROUP_PAYMENT,
                SystemConfig::GROUP_BOOKING, SystemConfig::GROUP_HOTEL, SystemConfig::GROUP_INVENTORY,
                SystemConfig::GROUP_PRICING, SystemConfig::GROUP_OTA, SystemConfig::GROUP_SUPPLIER,
                SystemConfig::GROUP_NOTIFICATION, SystemConfig::GROUP_SECURITY, SystemConfig::GROUP_API,
                SystemConfig::GROUP_LOGGING, SystemConfig::GROUP_MONITORING, SystemConfig::GROUP_BACKUP,
                SystemConfig::GROUP_MAINTENANCE
            ];

            if (!in_array($data['config_group'], $validGroups)) {
                return error('无效的配置分组');
            }

            // 设置默认值
            $data = array_merge([
                'is_encrypted' => false,
                'is_public' => false,
                'is_editable' => true,
                'sort_order' => 0
            ], $data);

            $userId = $this->getCurrentUserId($request);
            $result = $this->systemConfigService->createConfig($data, $userId);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '创建配置');
        }
    }

    /**
     * 更新配置
     *
     * @param Request $request
     * @param string $key
     * @return Response
     */
    public function update(Request $request, $key)
    {
        try {
            if (empty($key)) {
                return error('配置键不能为空');
            }

            $data = $this->getInput($request);

            if (!isset($data['config_value'])) {
                return error('配置值不能为空');
            }

            $userId = $this->getCurrentUserId($request);
            $result = $this->systemConfigService->updateConfig($key, $data['config_value'], $userId);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '更新配置');
        }
    }

    /**
     * 批量更新配置
     *
     * @param Request $request
     * @return Response
     */
    public function batchUpdate(Request $request)
    {
        try {
            $data = $this->getInput($request);

            if (empty($data['configs']) || !is_array($data['configs'])) {
                return error('配置数据不能为空且必须是数组格式');
            }

            $userId = $this->getCurrentUserId($request);
            $result = $this->systemConfigService->batchUpdateConfigs($data['configs'], $userId);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '批量更新配置');
        }
    }

    /**
     * 重置配置
     *
     * @param Request $request
     * @param string $key
     * @return Response
     */
    public function reset(Request $request, $key)
    {
        try {
            if (empty($key)) {
                return error('配置键不能为空');
            }

            $userId = $this->getCurrentUserId($request);
            $result = $this->systemConfigService->resetConfig($key, $userId);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '重置配置');
        }
    }

    /**
     * 删除配置
     *
     * @param Request $request
     * @param string $key
     * @return Response
     */
    public function destroy(Request $request, $key)
    {
        try {
            if (empty($key)) {
                return error('配置键不能为空');
            }

            $userId = $this->getCurrentUserId($request);
            $result = $this->systemConfigService->deleteConfig($key, $userId);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '删除配置');
        }
    }

    /**
     * 获取配置分组列表
     *
     * @param Request $request
     * @return Response
     */
    public function getGroups(Request $request)
    {
        try {
            $result = $this->systemConfigService->getConfigGroups();
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '获取配置分组');
        }
    }

    /**
     * 获取配置类型列表
     *
     * @param Request $request
     * @return Response
     */
    public function getTypes(Request $request)
    {
        try {
            $types = [
                ['value' => SystemConfig::TYPE_STRING, 'label' => '字符串'],
                ['value' => SystemConfig::TYPE_INTEGER, 'label' => '整数'],
                ['value' => SystemConfig::TYPE_FLOAT, 'label' => '浮点数'],
                ['value' => SystemConfig::TYPE_BOOLEAN, 'label' => '布尔值'],
                ['value' => SystemConfig::TYPE_JSON, 'label' => 'JSON'],
                ['value' => SystemConfig::TYPE_ARRAY, 'label' => '数组'],
                ['value' => SystemConfig::TYPE_EMAIL, 'label' => '邮箱'],
                ['value' => SystemConfig::TYPE_URL, 'label' => 'URL'],
                ['value' => SystemConfig::TYPE_PASSWORD, 'label' => '密码'],
                ['value' => SystemConfig::TYPE_TEXT, 'label' => '文本'],
                ['value' => SystemConfig::TYPE_SELECT, 'label' => '下拉选择'],
                ['value' => SystemConfig::TYPE_MULTISELECT, 'label' => '多选'],
                ['value' => SystemConfig::TYPE_RADIO, 'label' => '单选'],
                ['value' => SystemConfig::TYPE_CHECKBOX, 'label' => '复选框'],
                ['value' => SystemConfig::TYPE_DATE, 'label' => '日期'],
                ['value' => SystemConfig::TYPE_DATETIME, 'label' => '日期时间'],
                ['value' => SystemConfig::TYPE_TIME, 'label' => '时间'],
                ['value' => SystemConfig::TYPE_FILE, 'label' => '文件'],
                ['value' => SystemConfig::TYPE_IMAGE, 'label' => '图片']
            ];

            return success([
                'types' => $types,
                'total' => count($types)
            ]);

        } catch (\Exception $e) {
            return $this->handleException($e, '获取配置类型');
        }
    }

    /**
     * 获取公开配置（无需认证）
     *
     * @param Request $request
     * @return Response
     */
    public function getPublicConfigs(Request $request)
    {
        try {
            $configs = SystemConfig::getPublicConfigs();

            return success([
                'configs' => $configs,
                'total' => collect($configs)->flatten(1)->count()
            ]);

        } catch (\Exception $e) {
            return $this->handleException($e, '获取公开配置');
        }
    }

    /**
     * 验证配置值
     *
     * @param Request $request
     * @return Response
     */
    public function validateValue(Request $request)
    {
        try {
            $data = $this->getInput($request);

            // 验证必需参数
            $errors = $this->validateRequired($data, ['config_key', 'config_value']);
            if ($errors) {
                return error('参数验证失败', 400, $errors);
            }

            $config = SystemConfig::where('config_key', $data['config_key'])->first();
            if (!$config) {
                return error('配置不存在', 404);
            }

            $validation = $config->validateValue($data['config_value']);

            return success([
                'valid' => $validation['valid'],
                'message' => $validation['message'] ?? null
            ]);

        } catch (\Exception $e) {
            return $this->handleException($e, '验证配置值');
        }
    }

    /**
     * 获取当前用户ID
     */
    protected function getCurrentUserId(Request $request)
    {
        return $request->header('X-User-Id') ?? $request->session()->get('user_id') ?? null;
    }
}
