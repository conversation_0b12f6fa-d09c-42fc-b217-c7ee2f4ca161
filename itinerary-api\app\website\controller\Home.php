<?php

namespace app\website\controller;

use app\model\ItineraryModel;
use support\Cache;
use support\Request;
use support\Response;

class Home
{
    /**
     * 首页数据接口
     * @return \support\Response
     */
    public function index()
    {
        // 获取首页需要的所有数据
        $data = [
            'hotDepartureCities' => $this->getHotDepartureCities(),
            'hotDestinations' => $this->getHotDestinations(),
            'homepageRecommends' => $this->getHomepageRecommends(),
            'productCategories' => $this->getProductCategories(),
            'currentSeason' => $this->getCurrentSeasonRecommends(),
            'discountedItineraries' => $this->getDiscountedItineraries(),
            'hotSearchKeywords' => $this->getHotSearchKeywords(),
            'nearbyTravelRecommends' => $this->getNearbyTravelRecommends(),
            'domesticTravelRecommends' => $this->getDomesticTravelRecommends(),
            'overseasTravelRecommends' => $this->getOverseasTravelRecommends()
        ];
        
        return json(['code' => 200, 'msg' => 'success', 'data' => $data]);
    }
    
    /**
     * 获取所有出发城市
     * @return \support\Response
     */
    public function getDepartureCities()
    {
        $cities = Cache::get('departure_cities', []);
        return json(['code' => 200, 'msg' => 'success', 'data' => $cities]);
    }
        
    /**
     * 获取所有目的地
     * @return \support\Response
     */
    public function getDestinations()
    {
        $destinations = Cache::get('destinations', []);
        return json(['code' =>200, 'msg' => 'success', 'data' => $destinations]);
    }
    
    /**
     * 获取热门出发城市
     * @return array
     */
    public function getHotDepartureCities()
    {
        return Cache::get('hot_departure_cities', []);

    }
    
    /**
     * 获取按字母排序的城市列表
     * @return \support\Response
     */
    public function getAlphabetCities()
    {
        $cities = Cache::get('alphabet_departure_cities', []);
        return json(['code' =>200, 'msg' => 'success', 'data' => $cities]);
    }
    
    /**
     * 获取所有产品类别
     * @return array
     */
    public function getProductCategories()
    {
        return Cache::get('category_relationships', []);
    }

    public function productCategories()
    {
        return json(['code' =>200, 'msg' => 'success', 'data' =>  Cache::get('category_relationships', [])]);
    }

    /**
     * 获取首页推荐内容
     * @return array
     */
    public function getHomepageRecommends()
    {
        return Cache::get('homepage_recommends', []);
    }
    
    /**
     * 获取指定品类的推荐线路
     * @param int $categoryId 品类ID
     * @return \support\Response
     */
    public function getCategoryRecommends($categoryId)
    {
        $recommends = Cache::get('category_recommends_' . $categoryId, []);
        return json(['code' =>200, 'msg' => 'success', 'data' => $recommends]);
    }
    
    /**
     * 获取指定品类的热门目的地
     * @param int $categoryId 品类ID
     * @return \support\Response
     */
    public function getCategoryHotDestinations($categoryId)
    {
        $categoryId = (int)$categoryId;
        $destinations = Cache::get('category_hot_destinations_' . $categoryId, []);
        return json(['code' =>200, 'msg' => 'success', 'data' => $destinations]);
    }
    
    /**
     * 获取热门主题标签
     * @return \support\Response
     */
    public function getHotThemes()
    {
        $themes = Cache::get('hot_themes', []);
        return json(['code' =>200, 'msg' => 'success', 'data' => $themes]);
    }

    /**
     * 获取热门目的地
     * @return array
     */
    public function getHotDestinations()
    {
        return Cache::get('hot_destinations', []);
    }
    
    /**
     * 获取按区域分类的目的地
     * @return \support\Response
     */
    public function getDestinationsByRegion()
    {
        $destinations = Cache::get('destinations_by_region', []);
        return json(['code' =>200, 'msg' => 'success', 'data' => $destinations]);
    }
    
    /**
     * 获取指定目的地的推荐线路
     * @param string $destination 目的地名称
     * @return \support\Response
     */
    public function getDestinationRecommends($destination)
    {
        $cacheKey = 'destination_recommends_' . md5($destination);
        $recommends = Cache::get($cacheKey, []);
        return json(['code' =>200, 'msg' => 'success', 'data' => $recommends]);
    }
    
    /**
     * 获取旅行天数统计
     * @return \support\Response
     */
    public function getTravelDaysStats()
    {
        $stats = Cache::get('travel_days_stats', []);
        return json(['code' =>200, 'msg' => 'success', 'data' => $stats]);
    }
    
    /**
     * 获取按天数区间的线路推荐
     * @param string $rangeName 区间名称(短途/中途/长途/长线)
     * @return \support\Response
     */
    public function getDayRangeRecommends($rangeName)
    {
        $recommends = Cache::get('day_range_recommends_' . $rangeName, []);
        return json(['code' =>200, 'msg' => 'success', 'data' => $recommends]);
    }
    
    /**
     * 获取周末游推荐
     * @return \support\Response
     */
    public function getWeekendTravelRecommends()
    {
        $recommends = Cache::get('weekend_travel_recommends', []);
        return json(['code' =>200, 'msg' => 'success', 'data' => $recommends]);
    }
    
    /**
     * 获取小长假推荐
     * @return \support\Response
     */
    public function getShortHolidayRecommends()
    {
        $recommends = Cache::get('short_holiday_recommends', []);
        return json(['code' =>200, 'msg' => 'success', 'data' => $recommends]);
    }
    
    /**
     * 获取年假旅行推荐
     * @return \support\Response
     */
    public function getAnnualLeaveRecommends()
    {
        $recommends = Cache::get('annual_leave_recommends', []);
        return json(['code' =>200, 'msg' => 'success', 'data' => $recommends]);
    }
    
    /**
     * 获取价格区间统计
     * @return \support\Response
     */
    public function getPriceRangesStats()
    {
        $stats = Cache::get('price_ranges_stats', []);
        return json(['code' =>200, 'msg' => 'success', 'data' => $stats]);
    }
    
    /**
     * 获取指定价格区间的线路推荐
     * @param string $rangeName 区间名称
     * @return \support\Response
     */
    public function getPriceRangeRecommends($rangeName)
    {
        $safeRangeName = str_replace(['-', ' ', '+'], '_', $rangeName);
        $recommends = Cache::get('price_range_recommends_' . $safeRangeName, []);
        return json(['code' =>200, 'msg' => 'success', 'data' => $recommends]);
    }
    
    /**
     * 获取经济实惠线路推荐
     * @return \support\Response
     */
    public function getEconomyRecommends()
    {
        $recommends = Cache::get('economy_recommends', []);
        return json(['code' =>200, 'msg' => 'success', 'data' => $recommends]);
    }
    
    /**
     * 获取高端精品线路推荐
     * @return \support\Response
     */
    public function getLuxuryRecommends()
    {
        $recommends = Cache::get('luxury_recommends', []);
        return json(['code' =>200, 'msg' => 'success', 'data' => $recommends]);
    }
    
    /**
     * 获取当季推荐
     * @return array
     */
    public function getCurrentSeasonRecommends()
    {
        return Cache::get('current_season_recommends', []);
    }
    
    /**
     * 获取下季预热推荐
     * @return \support\Response
     */
    public function getNextSeasonRecommends()
    {
        $recommends = Cache::get('next_season_recommends', []);
        return json(['code' =>200, 'msg' => 'success', 'data' => $recommends]);
    }
    
    /**
     * 获取特价促销线路
     * @return array
     */
    private function getDiscountedItineraries()
    {
        return Cache::get('discounted_itineraries', []);
    }
    
    /**
     * 获取闪购限时特价
     * @return \support\Response
     */
    public function getFlashDeals()
    {
        $deals = Cache::get('flash_deals', []);
        return json(['code' =>200, 'msg' => 'success', 'data' => $deals]);
    }
    
    /**
     * 获取热门搜索关键词
     * @return array
     */
    public function getHotSearchKeywords()
    {
        return Cache::get('hot_search_keywords', []);
    }
    
    /**
     * 获取热搜词匹配的线路
     * @param string $keyword 关键词
     * @return \support\Response
     */
    public function getHotKeywordItineraries($keyword)
    {
        $cacheKey = 'hot_keyword_itineraries_' . md5($keyword);
        $itineraries = Cache::get($cacheKey, []);
        return json(['code' =>200, 'msg' => 'success', 'data' => $itineraries]);
    }
    
    /**
     * 搜索线路
     * @return \support\Response
     */
    public function search(Request $request): Response
    {
        // 获取搜索参数
        $keyword = $request->input('keyword', '');
        $departure = $request->input('departure', '');
        $destination = $request->input('destination', '');
        $productType = (int)$request->input('productType', 0);
        $days = (int)$request->input('days', 0);
        $minDays = (int)$request->input('minDays', 0);
        $maxDays = (int)$request->input('maxDays', 0);
        $minPrice = (float)$request->input('minPrice', 0);
        $maxPrice = (float)$request->input('maxPrice', 0);
        $tag = $request->input('tag', '');
        $region = $request->input('region', ''); // 新增区域参数

        // 分页和排序参数
        $sortField = $request->input('sortField', 'create_time');
        $sortOrder = $request->input('sortOrder', 'desc');
        $page = max(1, (int)$request->input('page', 1));
        $pageSize = min(50, max(1, (int)$request->input('pageSize', 10)));
        
        // 验证并修正排序字段
        $allowedSortFields = ['create_time', 'price', 'days', 'view_count', 'original_price'];
        if (!in_array($sortField, $allowedSortFields)) {
            $sortField = 'create_time';
        }
        
        // 验证排序方向
        $sortOrder = strtolower($sortOrder) === 'asc' ? 'asc' : 'desc';
        
        $where = [];
        
        // 构建查询条件
        if (!empty($keyword)) {
            $where[] = ['title|description', 'like', "%{$keyword}%"];
        }
        

        if ($productType > 0) {
            $where[] = ['category_id', '=', $productType];
        }
        
        if ($days > 0) {
            $where[] = ['days', '=', $days];
        }
        if ($minDays > 0) {
            $where[] = ['days', '>=', $minDays];
        }
        if ($maxDays > 0) {
            $where[] = ['days', '<=', $maxDays];
        }
        // 添加价格范围条件
        if ($minPrice > 0) {
            $where[] = ['price', '>=', $minPrice];
        }
        
        if ($maxPrice > 0) {
            $where[] = ['price', '<=', $maxPrice];
        }

        // 添加状态条件
        $where[] = ['status', '=', ItineraryModel::ONLINE_STATUS];
        
        // 执行查询
        $itineraryModel = ItineraryModel::mk();
        $query = $itineraryModel->where($where);

        if (!empty($departure)) {
            $query = $query->whereFindInSet('departure_city', $departure);
        }

        if (!empty($destination)) {
            $query = $query->whereFindInSet('destination', $destination);
        }

        // 处理区域搜索
        if (!empty($region)) {
            // 获取区域对应的省份编码前缀
            $provincePrefixes = $this->getProvincePrefixesByRegion($region);
            if (!empty($provincePrefixes)) {
                $destinationsByRegion = Cache::get('destinations_by_region', []);
                $destinationsInRegion = [];
                // 如果是国内区域
                if (isset($destinationsByRegion['国内'][$region])) {
                    foreach ($destinationsByRegion['国内'][$region] as $dest) {
                        if (isset($dest['cityCode'])) {
                            $destinationsInRegion[] = $dest['cityCode'];
                        }
                    }
                }
                
                if (!empty($destinationsInRegion)) {
                    $query = $query->where(function($q) use ($destinationsInRegion) {
                        foreach ($destinationsInRegion as $destCode) {
                            $q->whereOr('destination', 'like', "%{$destCode}%");
                        }
                    });
                }
            } else {
                // 检查是否是大洲搜索
                if (in_array($region, ['asia', 'europe', 'north_america', 'oceania', 'south_america', 'africa'])) {
                    // 从缓存中获取按区域分类的目的地
                    $destinationsByRegion = Cache::get('destinations_by_region', []);
                    $continentMap = [
                        'asia' => '亚洲',
                        'europe' => '欧洲',
                        'north_america' => '北美洲',
                        'oceania' => '大洋洲',
                        'south_america' => '南美洲',
                        'africa' => '非洲'
                    ];
                    
                    $continentName = $continentMap[$region] ?? '';
                    $destinationsInContinent = [];
                    
                    // 检查该大洲是否有目的地数据
                    if (!empty($continentName) && isset($destinationsByRegion[$continentName])) {
                        foreach ($destinationsByRegion[$continentName] as $dest) {
                            if (isset($dest['cityCode'])) {
                                $destinationsInContinent[] = $dest['cityCode'];
                            }
                        }
                    }
                    
                    // 如果找到了该大洲的目的地，添加到查询条件
                    if (!empty($destinationsInContinent)) {
                        $query = $query->where(function($q) use ($destinationsInContinent) {
                            foreach ($destinationsInContinent as $destCode) {
                                $q->whereOr('destination', 'like', "%{$destCode}%");
                            }
                        });
                    }
                }
            }
        }

        // 应用排序
        $query = $query->order($sortField, $sortOrder);
        
        // 如果按价格排序，添加第二排序条件
        if ($sortField === 'price') {
            $query = $query->order('view_count', 'desc');
        }
        if ($tag) {
            $query = $query->whereFindInSet('themes_ids', $tag);
        }

        $total = $query->count();
        $list = $query->page($page, $pageSize)
            ->append(['id'])
            ->select()
            ->toArray();

        // 构建返回数据
        $result = [
            'list' => $list,
            'total' => $total,
            'page' => $page,
            'pageSize' => $pageSize,
            'sortField' => $sortField,
            'sortOrder' => $sortOrder,
        ];
        
        return json(['code' => 200, 'msg' => 'success', 'data' => $result]);
    }

    /**
     * 根据区域名称获取对应的省份编码前缀
     * @param string $region 区域名称
     * @return array 省份编码前缀数组
     */
    private function getProvincePrefixesByRegion(string $region): array
    {
        $prefixMap = [
            '华东' => ['31', '32', '33', '34', '35', '36', '37'], // 上海、江苏、浙江、安徽、福建、江西、山东
            '华南' => ['44', '45', '46', '81', '82'], // 广东、广西、海南、香港、澳门
            '华中' => ['42', '43', '41'], // 湖北、湖南、河南
            '华北' => ['11', '12', '13', '14', '15'], // 北京、天津、河北、山西、内蒙古
            '西南' => ['50', '51', '52', '53', '54'], // 重庆、四川、贵州、云南、西藏
            '西北' => ['61', '62', '63', '64', '65'], // 陕西、甘肃、青海、宁夏、新疆
            '东北' => ['21', '22', '23'], // 辽宁、吉林、黑龙江
            '港澳台' => ['71', '81', '82'], // 台湾、香港、澳门
            'east_china' => ['31', '32', '33', '34', '35', '36', '37'],
            'south_china' => ['44', '45', '46', '81', '82'],
            'central_china' => ['42', '43', '41'],
            'north_china' => ['11', '12', '13', '14', '15'],
            'southwest' => ['50', '51', '52', '53', '54'],
            'northwest' => ['61', '62', '63', '64', '65'],
            'northeast' => ['21', '22', '23'],
            'hmt' => ['71', '81', '82'], // 港澳台
        ];
        
        return $prefixMap[$region] ?? [];
    }

    /**
     * 获取周边游推荐
     * @return array
     */
    public function getNearbyTravelRecommends()
    {
        return Cache::get('nearby_travel_recommends', []);
    }
    
    /**
     * 获取国内游推荐
     * @return array
     */
    public function getDomesticTravelRecommends()
    {
        return Cache::get('domestic_travel_recommends', []);
    }
    
    /**
     * 获取出境游推荐
     * @return array
     */
    public function getOverseasTravelRecommends()
    {
        return Cache::get('overseas_travel_recommends', []);
    }

    /**
     * 获取周边游页面完整数据
     * @return \support\Response
     */
    public function getNearbyTravelPage(): Response
    {
        $pageData = Cache::get('nearby_travel_page_data', []);
        return json(['code' => 200, 'msg' => 'success', 'data' => $pageData]);
    }
    
    /**
     * 获取国内游页面完整数据
     * @return \support\Response
     */
    public function getDomesticTravelPage(): Response
    {
        $pageData = Cache::get('domestic_travel_page_data', []);
        return json(['code' => 200, 'msg' => 'success', 'data' => $pageData]);
    }
    
    /**
     * 获取出境游页面完整数据
     * @return \support\Response
     */
    public function getOverseasTravelPage(): Response
    {
        $pageData = Cache::get('overseas_travel_page_data', []);
        return json(['code' => 200, 'msg' => 'success', 'data' => $pageData]);
    }
}