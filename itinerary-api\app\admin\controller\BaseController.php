<?php

namespace app\admin\controller;

use support\Request;
use support\Response;
use support\Log;

/**
 * 基础控制器类
 * 提供通用的控制器功能
 */
abstract class BaseController
{
    /**
     * 成功响应
     *
     * @param mixed $data
     * @param string $message
     * @param int $code
     * @return Response
     */
    protected function success($data = null, string $message = 'success', int $code = 200)
    {
        return $this->jsonResponse([
            'code' => $code,
            'message' => $message,
            'data' => $data,
            'timestamp' => time(),
            'request_id' => $this->getRequestId()
        ]);
    }

    /**
     * 错误响应
     *
     * @param string $message
     * @param int $code
     * @param mixed $errors
     * @return Response
     */
    protected function error(string $message, int $code = 400, $errors = null)
    {
        $response = [
            'code' => $code,
            'message' => $message,
            'timestamp' => time(),
            'request_id' => $this->getRequestId()
        ];

        if ($errors !== null) {
            $response['errors'] = $errors;
        }

        return $this->jsonResponse($response);
    }

    /**
     * JSON响应
     *
     * @param array $data
     * @param int $status
     * @return Response
     */
    protected function jsonResponse(array $data, int $status = 200)
    {
        return json($data, $status, [
            'Content-Type' => 'application/json; charset=utf-8'
        ]);
    }

    /**
     * 获取请求参数
     *
     * @param Request $request
     * @param string|null $key
     * @param mixed $default
     * @return mixed
     */
    protected function getInput(Request $request, string $key = null, $default = null)
    {
        if ($key === null) {
            return array_merge($request->get(), $request->post());
        }

        return $request->input($key, $default);
    }

    /**
     * 获取JSON请求数据
     *
     * @param Request $request
     * @return array
     */
    protected function getJsonData(Request $request): array
    {
        $contentType = $request->header('content-type', '');

        if (strpos($contentType, 'application/json') !== false) {
            $rawBody = $request->rawBody();
            $data = json_decode($rawBody, true);
            return is_array($data) ? $data : [];
        }

        return $request->post();
    }

    /**
     * 获取分页参数
     *
     * @param Request $request
     * @return array
     */
    protected function getPaginationParams(Request $request)
    {
        return [
            'page' => max(1, (int)$request->input('page', 1)),
            'limit' => min(100, max(1, (int)$request->input('limit', 20)))
        ];
    }

    /**
     * 验证必需参数
     *
     * @param array $data
     * @param array $required
     * @return array|null 返回错误信息数组，如果验证通过返回null
     */
    protected function validateRequired(array $data, array $required)
    {
        $errors = [];

        foreach ($required as $field) {
            if (!isset($data[$field]) || $data[$field] === '' || $data[$field] === null) {
                $errors[] = [
                    'field' => $field,
                    'message' => "字段 {$field} 是必需的"
                ];
            }
        }

        return empty($errors) ? null : $errors;
    }

    /**
     * 验证数据格式
     *
     * @param array $data
     * @param array $rules
     * @return array|null
     */
    protected function validateFormat(array $data, array $rules)
    {
        $errors = [];

        foreach ($rules as $field => $rule) {
            if (!isset($data[$field]) || $data[$field] === '' || $data[$field] === null) {
                continue;
            }

            $value = $data[$field];

            switch ($rule) {
                case 'email':
                    if (!filter_var($value, FILTER_VALIDATE_EMAIL)) {
                        $errors[] = [
                            'field' => $field,
                            'message' => "字段 {$field} 必须是有效的邮箱地址"
                        ];
                    }
                    break;

                case 'phone':
                    if (!preg_match('/^1[3-9]\d{9}$/', $value)) {
                        $errors[] = [
                            'field' => $field,
                            'message' => "字段 {$field} 必须是有效的手机号码"
                        ];
                    }
                    break;

                case 'date':
                    if (!$this->validateDate($value)) {
                        $errors[] = [
                            'field' => $field,
                            'message' => "字段 {$field} 必须是有效的日期格式(Y-m-d)"
                        ];
                    }
                    break;

                case 'integer':
                    if (!is_numeric($value) || (int)$value != $value) {
                        $errors[] = [
                            'field' => $field,
                            'message' => "字段 {$field} 必须是整数"
                        ];
                    }
                    break;

                case 'positive':
                    if (!is_numeric($value) || $value <= 0) {
                        $errors[] = [
                            'field' => $field,
                            'message' => "字段 {$field} 必须是正数"
                        ];
                    }
                    break;
            }
        }

        return empty($errors) ? null : $errors;
    }

    /**
     * 验证日期格式
     *
     * @param string $date
     * @param string $format
     * @return bool
     */
    protected function validateDate(string $date, string $format = 'Y-m-d')
    {
        $d = \DateTime::createFromFormat($format, $date);
        return $d && $d->format($format) === $date;
    }

    /**
     * 记录日志
     *
     * @param string $level
     * @param string $message
     * @param array $context
     */
    protected function log(string $level, string $message, array $context = [])
    {
        Log::$level($message, $context);
    }

    /**
     * 获取请求ID
     *
     * @return string
     */
    protected function getRequestId()
    {
        return 'req_' . uniqid();
    }

    /**
     * 获取当前用户
     *
     * @param Request $request
     * @return mixed
     */
    protected function getCurrentUser(Request $request)
    {
        // 这里应该从请求中获取当前用户信息
        // 临时返回null，实际应该根据认证系统实现
        return $request->user ?? null;
    }

    /**
     * 检查权限
     *
     * @param Request $request
     * @param string $permission
     * @return bool
     */
    protected function checkPermission(Request $request, string $permission)
    {
        $user = $this->getCurrentUser($request);

        if (!$user) {
            return false;
        }

        // 这里应该根据实际的权限系统实现
        if (method_exists($user, 'hasPermission')) {
            return $user->hasPermission($permission);
        }

        return true; // 临时返回true，实际应该根据权限系统实现
    }

    /**
     * 处理服务响应
     *
     * @param array $serviceResult
     * @return Response
     */
    protected function handleServiceResult(array $serviceResult)
    {
        if ($serviceResult['code'] === 200) {
            return $this->success(
                $serviceResult['data'] ?? null,
                $serviceResult['message'] ?? 'success'
            );
        } else {
            return $this->error(
                $serviceResult['message'] ?? 'error',
                $serviceResult['code'] ?? 400,
                $serviceResult['errors'] ?? null
            );
        }
    }

    /**
     * 异常处理
     *
     * @param \Exception $e
     * @param string $operation
     * @return Response
     */
    protected function handleException(\Exception $e, string $operation = 'operation')
    {
        $this->log('error', "{$operation}失败", [
            'error' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine(),
            'trace' => $e->getTraceAsString()
        ]);

        return $this->error("{$operation}失败: " . $e->getMessage(), 500);
    }

    /**
     * 获取当前用户ID
     *
     * @param Request $request
     * @return int|null
     */
    protected function getCurrentUserId(Request $request)
    {
        $user = $this->getCurrentUser($request);
        return $user ? $user->id : null;
    }

    /**
     * 使用ValidationService验证数据
     *
     * @param array $data
     * @param array $rules
     * @return array|null
     */
    protected function validate(array $data, array $rules)
    {
        $result = \app\service\ValidationService::validate($data, $rules);
        return $result['valid'] ? null : $result['errors'];
    }

    /**
     * 验证日期范围
     *
     * @param string $startDate
     * @param string $endDate
     * @return array|null
     */
    protected function validateDateRange(string $startDate, string $endDate)
    {
        $result = \app\service\ValidationService::validateDateRange($startDate, $endDate);
        return $result['valid'] ? null : $result['errors'];
    }

    /**
     * 验证价格
     *
     * @param mixed $price
     * @return array|null
     */
    protected function validatePrice($price)
    {
        $result = \app\service\ValidationService::validatePrice($price);
        return $result['valid'] ? null : $result['errors'];
    }

    /**
     * 验证数量
     *
     * @param mixed $quantity
     * @return array|null
     */
    protected function validateQuantity($quantity)
    {
        $result = \app\service\ValidationService::validateQuantity($quantity);
        return $result['valid'] ? null : $result['errors'];
    }

    /**
     * 获取客户端IP
     *
     * @param Request $request
     * @return string
     */
    protected function getClientIp(Request $request)
    {
        return $request->getRealIp();
    }

    /**
     * 获取用户代理
     *
     * @param Request $request
     * @return string
     */
    protected function getUserAgent(Request $request)
    {
        return $request->header('User-Agent', '');
    }

    /**
     * 生成随机字符串
     *
     * @param int $length
     * @return string
     */
    protected function generateRandomString(int $length = 32)
    {
        return bin2hex(random_bytes($length / 2));
    }

    /**
     * 格式化金额
     *
     * @param float $amount
     * @param int $decimals
     * @return string
     */
    protected function formatAmount(float $amount, int $decimals = 2)
    {
        return number_format($amount, $decimals, '.', '');
    }

    /**
     * 格式化日期
     *
     * @param string $date
     * @param string $format
     * @return string
     */
    protected function formatDate(string $date, string $format = 'Y-m-d H:i:s')
    {
        return date($format, strtotime($date));
    }
}
