<?php

declare(strict_types=1);

namespace app\service\payment;

interface PaymentInterface
{
    /**
     * 发起支付
     * @param array $payload 统一的支付请求参数
     * @return array 统一的支付响应数据
     */
    public function pay(array $payload): array;

    /**
     * 退款
     * @param array $payload
     * @return array
     */
    public function refund(array $payload): array;

    /**
     * 查询
     * @param array $payload
     * @return array
     */
    public function query(array $payload): array;

    /**
     * 异步通知处理
     * @param array $params 回调请求参数
     * @return array ['success'=>bool, 'out_trade_no'=>string, 'trade_no'=>string, 'raw'=>mixed]
     */
    public function notify(array $params): array;

    /**
     * 撤销/取消
     * @param array $payload
     * @return array
     */
    public function cancel(array $payload): array;
}

