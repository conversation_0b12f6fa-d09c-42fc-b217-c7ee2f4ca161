<?php

namespace base\AuditLog;

use base\base\DS;

class AirParse
{
    private DS $context;
    private DS $config;
    private string $locale;

    public function __construct(array $context, $locale = 'zh-CN')
    {
        $this->context = new DS($context);
        $this->config = new DS($context['config']);
        $this->locale = $locale;
    }

    public function get(): array
    {
        $detail = $this->context->get('desc');
        $newDetail = [];
        $arrayIndexSplitter = $this->config('highlight.array_index_splitter', '::');

        foreach ($detail as $key => $value) {
            $newKey = is_numeric($key) ? $key : $this->translateKeys($key, $arrayIndexSplitter);
            $newDetail[$newKey] = is_numeric($key)
                ? $this->infoParse($value['mode'], $value)
                : array_map(fn($item) => $this->infoParse($item['mode'], $item), $value);
        }

        return [
            'trace_id' => $this->context->get('trace_id'),
            'message' => $this->parse(
                $this->parseLang(
                    $this->context->get('message')
                )
            ),
            'old' => $this->context->get('old'),
            'new' => $this->context->get('new'),
            'diff' => $this->context->get('diff'),
            'desc' => $newDetail,
        ];
    }

    private function config($key, $default = '')
    {
        return $this->config->get($key, $default);
    }

    private function translateKeys($key, $splitter): string
    {
        return arr2Str(array_map(fn($valueKey) => $this->parseLang($valueKey), str2Arr($key, $splitter)), $splitter);
    }

    private function parse($message): string
    {
        preg_match_all("~#\[.[a-zA-Z0-9_.]{1,100}.]~", $message, $matches);
        if (!empty($matches[0])) {
            $preTags = $this->config('highlight.message.pre_tags', $this->config('highlight.pre_tags', ''));
            $postTags = $this->config('highlight.message.post_tags', $this->config('highlight.post_tags', ''));
            $valueDefault = $this->config('value.default', '');

            foreach ($matches[0] as $str) {
                $key = mb_substr($str, 2, -1, 'UTF-8');
                $value = $this->parseLang($this->context->get($key, $valueDefault));
                $message = str_replace($str, $preTags . $value . $postTags, $message);
            }
        }

        return $message;
    }

    private function parseLang($message): string
    {
        return $this->context->get('lang.' . $this->locale . '.' . $message, $message);
    }

    private function getModeLang($mode): string
    {
        $messageTemplate = $this->config("detail.$mode");
        return $this->config('lang.detail.' . $this->locale . '.' . $messageTemplate, $messageTemplate);
    }

    private function infoParse(string $mode, $data): string
    {
        $messageTemplate = $this->getModeLang($mode);
        $message = $this->parseLang($messageTemplate);

        $data = ds($data);
        preg_match_all("~%.[a-zA-Z0-9_.]{1,100}.%~", $message, $placeholders);
        if (empty($placeholders[0])) {
            return $message;
        }

        $valueDefault = $this->config->get('value.default', '');
        $preTags = $this->config->get('highlight.detail.pre_tags', $this->config->get('highlight.pre_tags', ''));
        $postTags = $this->config->get('highlight.detail.post_tags', $this->config->get('highlight.post_tags', ''));

        foreach ($placeholders[0] as $placeholder) {
            $key = mb_substr($placeholder, 1, -1, 'UTF-8');
            $value = $data->get($key, $valueDefault);
            $translatedValue = $this->parseLang($value);
            $message = str_replace($placeholder, $preTags . $translatedValue . $postTags, $message);
        }

        return $message;
    }
}