# AuditLog 操作手册

## 简介

`AuditLog` 是一个强大的审计日志记录框架，用于记录系统中的各种操作，包括数据创建、修改和删除等。它可以帮助管理员追踪系统中的变更历史，便于审计和问题排查。框架通过
`AirLog` 基类实现，提供了灵活且易于扩展的日志记录机制。

## 框架结构

AuditLog 框架主要由以下组件组成：

1. **AirLog**：基础日志类，提供核心功能
2. **AirField**：字段处理类，管理多维数组的字段定义和格式化
3. **AirParse**：数据解析类，处理数据转换和格式化

## 主要功能

1. 记录数据创建、修改、删除等操作
2. 支持新旧数据对比，突出显示变更字段
3. 支持自定义字段格式化
4. 支持复杂数据结构（包括嵌套和关联数据）
5. 灵活的场景定义和消息模板
6. 完整的用户和操作追踪

## 基本使用

### 创建日志类

要使用 AuditLog，首先需要创建一个继承自 `AirLog` 的日志类：

```php
<?php

namespace service\log;

use base\AuditLog\AirField;
use base\AuditLog\AirLog;

class YourLog extends AirLog
{
    // 定义日志表名
    protected string $tableName = 'audit_log_your_module';
    
    // 定义模块名
    protected string $module = 'your_module';
    
    // 定义消息模板
    protected string|array $message = [
        'create' => '创建了 #[new.id]',
        'update' => '更新了 #[new.id]',
        'delete' => '删除了 #[old.id]',
    ];
    
    // 定义字段映射
    protected array $field = [
        'id' => 'ID',
        'name' => '名称',
        'status' => '状态',
        // 其他字段...
    ];
    
    // 可选：自定义字段格式化方法
    public function parseStatus($value)
    {
        $statusMap = [
            0 => '禁用',
            1 => '启用',
        ];
        return $statusMap[$value] ?? $value;
    }
}
```

### 记录日志

```php
// 记录创建操作
YourLog::make()
    ->setTraceId($id)              // 设置追踪ID
    ->newData($newData)            // 设置新数据
    ->scene('create')              // 设置场景
    ->save();                      // 保存日志

// 记录更新操作
YourLog::make()
    ->setTraceId($id)
    ->oldData($oldData)            // 设置旧数据
    ->newData($newData)            // 设置新数据
    ->scene('update')              // 设置场景
    ->save();                      // 保存日志

// 记录删除操作
YourLog::make()
    ->setTraceId($id)
    ->oldData($oldData)            // 设置旧数据
    ->scene('delete')              // 设置场景
    ->save();                      // 保存日志

// 自定义主题及字段
YourLog::make()
    ->setTraceId($id)
    ->oldData($oldData)            // 设置旧数据
    ->message('自定义主题 #[old.id]') // 自定义主题
    ->field([
            'id' => '序号',
            'nameFaa|faa' => '名称',
            'price' => '价格',
            'quantity' => '数量',
        ]) // 添加字段规则
    ->remove('faa')     // 移除某个字段
    ->append('nameFaa|faa','名称')     // 追加某个字段
    ->only(['id,faa,price']) // 指定需要记录的字段列表
    ->save();                      // 保存日志
```

## 高级功能

### 自定义场景

如果需要针对特定的场景自定义字段映射，可以实现 `sceneXxx` 方法：

```php
/**
 * 自定义数据字段映射
 * @return void
 */
public function sceneItems(): void
{
    $this->message('自定义主题 #[old.id]') // 自定义主题
         ->field([
            'id' => '序号',
            'nameFaa|faa' => '名称',
            'price' => '价格',
            'quantity' => '数量',
        ]) // 添加字段规则
      
    ->remove('faa')     // 移除某个字段
    ->append('nameFaa|faa','名称')     // 追加某个字段
    ->only(['id,faa,price']) // 指定需要记录的字段列表
}
```

### 多维数组字段映射

需要实现 `getFieldXxx` 方法,当数组没有键，直接使用`getField`

```php
/**
 * 自定义子数据字段映射
 * @param AirField $field
 * @return AirField
 */
public function getFieldItems(AirField $field): AirField
{
    return $field->setIndex('id')
        ->field([
            'id' => '序号',
            'name' => '名称',
            'price' => '价格',
            'quantity' => '数量',
        ]);
}
```

### 处理复杂数据结构

AuditLog 支持处理复杂的数据结构，包括嵌套数据和关联数据：

```php
// 定义带有嵌套结构的字段映射
protected array $field = [
    'id' => 'ID',
    'name' => '名称',
    'relation.id' => '关联ID',
    'relation.name' => '关联名称',
    'nested.level1.level2' => '嵌套数据',
];
```

### 自定义解析方法

您可以为任何字段定义自定义解析方法，方法名称格式为 `parse字段名`：

```php
// 解析时间戳
public function parseCreateTime($value)
{
    return formatDateTime($value, 'Y-m-d H:i:s');
}

// 解析枚举值
public function parseType($value)
{
    $types = [1 => '类型一', 2 => '类型二', 3 => '类型三'];
    return $types[$value] ?? '未知类型';
}

// 解析关联数据
public function parseItems($value)
{
    if (empty($value)) {
        return '-';
    }
    $result = [];
    foreach ($value as $item) {
        $result[] = "ID:{$item['id']}, 名称:{$item['name']}";
    }
    return implode('; ', $result);
}
```

## 实现细节

### 消息模板

消息模板支持引用新旧数据中的字段值：

```php
protected string|array $message = [
    'create' => '创建了 #[new.name]',
    'update' => '更新了 #[new.name] 的信息',
    'delete' => '删除了 #[old.name]',
    'custom' => '将 #[old.status] 修改为 #[new.status]',
];
```

### 字段别名

可以使用 `|` 定义字段别名， ：

```php
protected array $field = [
    'longFieldName|short' => '字段说明',
];

// 然后可以使用 parseShort 方法来格式化
public function parseShort($value)
{
    // 格式化逻辑
}
```

## 使用场景

AuditLog 适用于各种需要记录操作历史的场景，包括但不限于：

1. 用户管理（创建、修改、删除用户）
2. 配置管理（系统配置变更）
3. 业务数据操作（订单、产品、客户等数据变更）
4. 敏感操作审计（权限变更、重要设置修改）

## 实际应用案例 - HotelLog

以下是 `HotelLog` 作为 AuditLog 框架的实际应用案例：

### HotelLog 类定义

```php
<?php

namespace service\log;

use base\AuditLog\AirField;
use base\AuditLog\AirLog;
use service\type\Dist;

class HotelLog extends AirLog
{
    protected string $tableName = 'audit_log_hotel';
    protected string $module = 'hotel';

    protected string|array $message = [
        'save' => '新增酒店 #[new.hotelCode]',
        'update' => '更新酒店 #[new.hotelCode] 信息',
        'updateBase' => '更新酒店 #[new.hotelCode] 基本信息',
        'updateNames' => '更新酒店 #[new.hotelCode] 名称信息',
        'updateLocation' => '更新酒店 #[new.hotelCode] 位置信息',
        'delete' => '删除酒店 #[old.hotelCode]',
    ];

    protected array $field = [
        'hotelCode' => '酒店代码',
        'active' => '是否启用',
        'chainCode' => '集团代码',
        // 其他字段...
    ];

    // 针对名称信息的字段映射
    public function getFieldNames(AirField $field): AirField
    {
        return $field->setIndex('id')
            ->field([
                'hotelCode' => '酒店代码',
                'languageCode|lang' => '语言',
                'content' => '酒店名称',
            ]);
    }

    // 自定义解析方法
    public function parseActive($value)
    {
        return $value ? '是' : '否';
    }

    public function parseLang($value)
    {
        return Dist::getInstance()->getOptionByValue('language', $value)['label'] ?? '';
    }
    
    // 其他解析方法...
}
```

### HotelLog 使用示例

#### 示例1: 新增酒店

```php
// 在 save 方法中
HotelLog::make()
    ->setTraceId($hotelBasic->hotel_code)
    ->newData($data)
    ->scene('save')
    ->save();
```

#### 示例2: 更新酒店信息

```php
// 在 update 方法中
// 先获取旧数据
$oldData = $hotelBasic->toArray();

// 操作完成后记录日志
HotelLog::make()
    ->setTraceId($hotelBasic->hotel_code)
    ->newData($data)
    ->oldData($oldData)
    ->scene('update')
    ->save();
```

#### 示例3: 删除酒店

```php
// 在 delete 方法中
// 先获取旧数据
$oldData = $hotel->toArray();

// 操作完成后记录日志
HotelLog::make()
    ->setTraceId($hotel->hotel_code)
    ->oldData($oldData)
    ->scene('delete')
    ->save();
```

## 最佳实践

1. **事务处理**：日志记录应在同一事务内进行，确保数据操作和日志记录的一致性

    ```php
    // 开始事务
    DB::beginTransaction();
    
    try {
        // 执行数据操作
        $model->save($data);
        
        // 记录日志
        YourLog::make()
            ->setTraceId($id)
            ->newData($data)
            ->scene('create')
            ->save();
        
        // 提交事务
        DB::commit();
    } catch (\Exception $e) {
        // 回滚事务
        DB::rollBack();
        throw $e;
    }
    ```

2. **完整性保证**：确保记录日志时包含所有必要的关联数据

    ```php
    // 加载完整的关联数据
    $fullData = Model::with(['relation1', 'relation2'])
        ->where('id', $id)
        ->find()
        ->toArray();
    ```

3. **关联ID处理**：使用 `append()` 方法确保关联表的 ID 字段被加载

    ```php
    $fullData = Model::with(['items'])
        ->append(['items.id'])
        ->where('id', $id)
        ->find()
        ->toArray();
    ```

4. **场景设计**：合理设计场景名称和消息模板，使日志易于理解和查询

5. **字段映射**：全面定义字段映射，提供完整的中文说明，便于日志查看

## 故障排查

如果日志记录不正确，请检查：

1. 是否正确设置了 `scene`（场景名称）
2. 是否正确传递了完整的数据结构
3. 是否正确加载了所有必要的关联数据
4. 是否使用了 `append` 方法加载关联表的 ID 字段
5. 是否在事务内正确提交了日志记录 