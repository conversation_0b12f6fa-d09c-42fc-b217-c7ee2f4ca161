<?php

declare(strict_types=1);

namespace app\admin\controller;

use app\model\UserModel;
use app\model\ItineraryOrderModel;
use app\model\ReservationModel;
use app\model\PaymentModel;
use support\Request;
use support\Response;

/**
 * 预约控制器
 */
class BookingController extends BaseController
{
    /**
     * 获取预约列表
     * @param Request $request
     * @return Response
     */
    public function index(Request $request): Response
    {
        try {
			$params = $this->getPaginationParams($request);
			$page = $params['page'];
			$limit = $params['limit'];

			// 查询参数
			$status = (string)$request->input('status', ''); // unreserved/pending/reserved/completed/cancel_refund/cancelled
			$orderNo = (string)$request->input('order_no', '');
			$productName = (string)$request->input('product_name', ''); // 行程名称
			$productType = (string)$request->input('product_type', '');
			$buyerPhone = (string)$request->input('buyer_phone', '');
			$travelStart = (string)$request->input('travel_start', '');
			$travelEnd = (string)$request->input('travel_end', '');

			// 基础查询：订单 o + 行程 i + 预约 r（左连接）
			$query = ItineraryOrderModel::mk()
				->alias('o')
				->leftJoin('itinerary i', 'i.id = o.itinerary_id and i.delete_time = 0')
				->leftJoin('reservations r', 'r.order_id = o.id and r.delete_time = 0')
				->where('o.delete_time', 0)
				->where('o.order_type', ItineraryOrderModel::ORDER_TYPE_RESERV);

			// 通用筛选
			if ($orderNo !== '') {
				$query = $query->whereLike('o.order_no', "%{$orderNo}%");
			}
			if ($productName !== '') {
				$query = $query->whereLike('i.title', "%{$productName}%");
			}
			if ($productType !== '') {
				$query = $query->where('i.product_type', $productType);
			}
			if ($buyerPhone !== '') {
				$query = $query->whereLike('o.phone', "%{$buyerPhone}%");
			}
			if ($travelStart !== '' && $travelEnd !== '') {
				$query = $query->whereBetween('o.travel_date', [$travelStart, $travelEnd]);
			} elseif ($travelStart !== '') {
				$query = $query->where('o.travel_date', '>=', $travelStart);
			} elseif ($travelEnd !== '') {
				$query = $query->where('o.travel_date', '<=', $travelEnd);
			}

			// 按标签筛选
			switch ($status) {
				case 'unreserved':
					$query = $query->where('o.status', ItineraryOrderModel::STATUS_PAID)->whereNull('r.id');
					break;
				case 'pending':
					$query = $query->where('r.status', ReservationModel::STATUS_PENDING);
					break;
				case 'reserved':
					$query = $query->where('r.status', ReservationModel::STATUS_CONFIRMED);
					break;
				case 'completed':
					$query = $query->where('r.status', ReservationModel::STATUS_COMPLETED);
					break;
				case 'cancel_refund':
					$query = $query->where(function($q){
						$q->where('o.status', ItineraryOrderModel::STATUS_REFUNDED)->whereOr('r.status', ReservationModel::STATUS_CANCELLED);
					});
					break;
				case 'cancelled':
					$query = $query->where('o.status', ItineraryOrderModel::STATUS_CANCELLED);
					break;
				default:
					// 默认不过滤标签，返回综合列表
					break;
			}

			// 统计各标签数量（用于页签数字）
			$baseForCount = ItineraryOrderModel::mk()
				->alias('o')
				->leftJoin('itinerary i', 'i.id = o.itinerary_id and i.delete_time = 0')
				->leftJoin('reservations r', 'r.order_id = o.id and r.delete_time = 0')
				->where('o.delete_time', 0)
                ->where('o.order_type', ItineraryOrderModel::ORDER_TYPE_RESERV);
			if ($orderNo !== '') { $baseForCount = $baseForCount->whereLike('o.order_no', "%{$orderNo}%"); }
			if ($productName !== '') { $baseForCount = $baseForCount->whereLike('i.title', "%{$productName}%"); }
			if ($productType !== '') { $baseForCount = $baseForCount->where('i.product_type', $productType); }
			if ($buyerPhone !== '') { $baseForCount = $baseForCount->whereLike('o.phone', "%{$buyerPhone}%"); }
			if ($travelStart !== '' && $travelEnd !== '') {
				$baseForCount = $baseForCount->whereBetween('o.travel_date', [$travelStart, $travelEnd]);
			} elseif ($travelStart !== '') {
				$baseForCount = $baseForCount->where('o.travel_date', '>=', $travelStart);
			} elseif ($travelEnd !== '') {
				$baseForCount = $baseForCount->where('o.travel_date', '<=', $travelEnd);
			}

			$counts = [
				'unreserved' => (clone $baseForCount)->where('o.status', ItineraryOrderModel::STATUS_PAID)->whereNull('r.id')->count(),
				'pending' => (clone $baseForCount)->where('r.status', ReservationModel::STATUS_PENDING)->count(),
				'RESERVED_KEY' => 0, // 占位，稍后填充
				'completed' => (clone $baseForCount)->where('r.status', ReservationModel::STATUS_COMPLETED)->count(),
				'cancel_refund' => (clone $baseForCount)->where(function($q){
					$q->where('o.status', ItineraryOrderModel::STATUS_REFUNDED)->whereOr('r.status', ReservationModel::STATUS_CANCELLED);
				})->count(),
				'cancelled' => (clone $baseForCount)->where('o.status', ItineraryOrderModel::STATUS_CANCELLED)->count(),
			];
			$counts['reserved'] = (clone $baseForCount)->where('r.status', ReservationModel::STATUS_CONFIRMED)->count();

			// 查询字段并分页
			$list = $query
				->field('o.*, i.title as itinerary_title, i.product_type, i.category_id, r.id as reservation_id, r.status as reservation_status, r.travel_date as reservation_travel_date, r.name as contact_name, r.phone as contact_phone')
				->order('o.create_time', 'desc')
				->page($page, $limit)
				->append(['id','create_time'])
				->select()
				->toArray();

			// 订单支付时间映射
			$orderIds = array_column($list, 'id');
			$paymentMap = [];
			if (!empty($orderIds)) {
				$payments = PaymentModel::mk()
					->whereIn('order_id', $orderIds)
					->where('payment_type', PaymentModel::TYPE_ORDER)
					->where('status', PaymentModel::STATUS_PAID)
					->order('payment_time', 'desc')
					->select()
					->toArray();
				foreach ($payments as $p) {
					$paymentMap[$p['orderId']] = $p['paymentTime'] ?? null;
				}
			}

			// 数据整形
			$statusTextMap = ItineraryOrderModel::STATUS_MAP;
			$reservationStatusTextMap = [
				ReservationModel::STATUS_PENDING => '待接单',
				ReservationModel::STATUS_CONFIRMED => '已预约',
				ReservationModel::STATUS_CANCELLED => '已取消',
				ReservationModel::STATUS_COMPLETED => '已完成',
			];

			foreach ($list as &$item) {
				$adult = (int)($item['adultCount'] ?? 0);
				$child = (int)($item['childCount'] ?? 0);
				$item['purchaseCount'] = $adult + $child;
				$item['orderIncome'] = (float)($item['totalAmount'] ?? 0);
				$item['paymentTime'] = $paymentMap[$item['id']] ?? null;
				$item['statusText'] = $statusTextMap[$item['status']] ?? $item['status'];
				$item['reservationStatusText'] = isset($item['reservationStatus']) ? ($reservationStatusTextMap[$item['reservationStatus']] ?? $item['reservationStatus']) : null;
				// 兼容前端字段示例
				$item['buyerPhone'] = $item['phone'] ?? '';
			}

			$total = ItineraryOrderModel::mk()
				->alias('o')
				->leftJoin('itinerary i', 'i.id = o.itinerary_id and i.delete_time = 0')
				->leftJoin('reservations r', 'r.order_id = o.id and r.delete_time = 0')
				->where('o.delete_time', 0)
                ->where('o.order_type', ItineraryOrderModel::ORDER_TYPE_RESERV);
			// 复用与列表相同的过滤逻辑
			if ($orderNo !== '') { $total = $total->whereLike('o.order_no', "%{$orderNo}%"); }
			if ($productName !== '') { $total = $total->whereLike('i.title', "%{$productName}%"); }
			if ($productType !== '') { $total = $total->where('i.product_type', $productType); }
			if ($buyerPhone !== '') { $total = $total->whereLike('o.phone', "%{$buyerPhone}%"); }
			if ($travelStart !== '' && $travelEnd !== '') {
				$total = $total->whereBetween('o.travel_date', [$travelStart, $travelEnd]);
			} elseif ($travelStart !== '') {
				$total = $total->where('o.travel_date', '>=', $travelStart);
			} elseif ($travelEnd !== '') {
				$total = $total->where('o.travel_date', '<=', $travelEnd);
			}
			switch ($status) {
				case 'unreserved': $total = $total->where('o.status', ItineraryOrderModel::STATUS_PAID)->whereNull('r.id'); break;
				case 'pending': $total = $total->where('r.status', ReservationModel::STATUS_PENDING); break;
				case 'reserved': $total = $total->where('r.status', ReservationModel::STATUS_CONFIRMED); break;
				case 'completed': $total = $total->where('r.status', ReservationModel::STATUS_COMPLETED); break;
				case 'cancel_refund': $total = $total->where(function($q){ $q->where('o.status', ItineraryOrderModel::STATUS_REFUNDED)->whereOr('r.status', ReservationModel::STATUS_CANCELLED); }); break;
				case 'cancelled': $total = $total->where('o.status', ItineraryOrderModel::STATUS_CANCELLED); break;
			}
			$totalCount = $total->count();

			return success([
				'list' => $list,
				'total' => $totalCount,
				'page' => $page,
				'limit' => $limit,
				'counts' => $counts,
			]);
        } catch (\Exception $e) {
            return $this->handleException($e, '获取预约列表');
        }
    }

    /**
     * 预约详情
     * @param Request $request
     * @param int|string $id 订单ID
     * @return Response
     */
    public function show(Request $request, $id): Response
    {
        try {
            $id = (string)$id;

            // 1) 订单详情（含行程）
            $orderModel = new ItineraryOrderModel();
            $order = $orderModel->getDetail($id);
            if (!$order) {
                return error(404, '订单不存在');
            }

            // 2) 预约信息
            $reservationModel = new ReservationModel();
            $reservation = $reservationModel->getByOrderId($id) ?? null;

            // 3) 支付汇总
            $orderPaid = (float)PaymentModel::mk()
                ->where('order_id', $id)
                ->where('payment_type', PaymentModel::TYPE_ORDER)
                ->where('status', PaymentModel::STATUS_PAID)
                ->sum('amount');

            $surchargePaid = (float)PaymentModel::mk()
                ->where('order_id', $id)
                ->where('payment_type', PaymentModel::TYPE_SURCHARGE)
                ->where('status', PaymentModel::STATUS_PAID)
                ->sum('amount');

            $itinerary = $order['itinerary'] ?? [];

            // 4) 衍生字段
            $adultCount = (int)($reservation['adult_count'] ?? $order['adultCount'] ?? 0);
            $childCount = (int)($reservation['child_count'] ?? $order['childCount'] ?? 0);
            $purchaseCount = $adultCount + $childCount;

            $travelDate = $reservation['travelDate'];
            $days = (int)($itinerary['days'] ?? 0);
            $nights = (int)($itinerary['nights'] ?? max(0, $days - 1));
            $travelRange = '';
            if ($travelDate && $days > 0) {
                $end = date('Y-m-d', strtotime($travelDate . ' +' . ($days - 1) . ' day'));
                $travelRange = sprintf('%s - %s（%d天%d晚）', $travelDate, $end, $days, $nights);
            }

            $reservationStatusTextMap = [
                ReservationModel::STATUS_PENDING => '待接单',
                ReservationModel::STATUS_CONFIRMED => '已预约',
                ReservationModel::STATUS_CANCELLED => '已取消',
                ReservationModel::STATUS_COMPLETED => '已完成',
            ];

            $detail = [
                'basic' => [
                    'orderNo' => $order['orderNo'] ?? '',
                    'productName' => $itinerary['title'] ?? '',
                    'productId' => $order['itineraryId'] ?? null,
                    'productCategoryId' => $itinerary['categoryId'] ?? null,
                    'productType' => $itinerary['productType'] ?? '',
                    'purchaseCount' => $purchaseCount,
                    'orderStatus' => $order['status'] ?? '',
                    'orderStatusText' => $order['statusText'] ?? '',
                    'reservationTime' => $reservation['createTime'],
                ],
                'contact' => [
                    'name' => $reservation['name'] ?? ($order['name'] ?? ''),
                    'phone' => $reservation['phone'] ?? ($order['phone'] ?? ''),
                    'email' => $order['email'] ?? '',
                ],
                'travel' => [
                    'travelDate' => $travelDate,
                    'travelRange' => $travelRange,
                    'realTravelerCount' => $purchaseCount,
                ],
                'occupancies' => $reservation['occupancies'] ?? [],
                'amounts' => [
                    'orderReceived' => (float)$orderPaid,
                    'saleAmount' => (float)($order['totalAmount'] ?? 0),
                    'surchargeAmount' => (float)($reservation['surcharge_amount'] ?? 0),
                    'surchargePaid' => (bool)($reservation['surcharge_paid'] ?? false),
                    'surchargeReceived' => (float)$surchargePaid,
                    'totalReceived' => (float)($orderPaid + $surchargePaid),
                ],
                'reservation' => $reservation,
                'order' => $order
            ];

            return success($detail);
        } catch (\Exception $e) {
            return $this->handleException($e, '获取预约详情');
        }
    }

    /**
     * 接单
     */
    public function receiveBooking(Request $request) {
        try {
            $reservationId = (int)$request->post('reservation_id', 0);
            $status = $request->post('status', '');
            $note = $request->post('note', '');
            if (!$reservationId) {
                return error(400, '预约ID不能为空');
            }

            if (!in_array($status, [ReservationModel::STATUS_CONFIRMED, ReservationModel::STATUS_REFUSED])) {
                return error(400, '状态错误');
            }

            $service = new \app\service\BookingService();
            $result = $service->receiveBooking($reservationId, $status, $note);

            return success($result, '接单成功');
        } catch (\Exception $e) {
            return $this->handleException($e, '接单');
        }
    }
}
