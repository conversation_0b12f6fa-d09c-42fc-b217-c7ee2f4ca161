<?php
namespace app\api\controller;

use support\Cache;
use support\Request;
use app\model\CityInfoModel;
use support\Response;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\facade\Db;

class CityInfo
{
    /**
     * 获取城市列表（按省份分组）
     *
     * @param Request $request
     * @return Response
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function json(Request $request)
    {
        // 定义缓存键
        $cacheKey = 'city_info_json';
        
        // 检查缓存是否存在
        if (Cache::has($cacheKey)) {
            return success(Cache::get($cacheKey));
        }
        
        // 缓存不存在，查询数据库
        // 获取所有城市数据
        $cityInfos = CityInfoModel::mk()
            ->field(['province_name', 'province_code', 'city_name', 'city_code', 'city_initial'])
            ->select();
        
        // 按省份分组处理数据
        $result = [];
        $provinceMap = [];
        
        foreach ($cityInfos as $cityInfo) {
            $provinceCode = $cityInfo->province_code;
            $provinceName = $cityInfo->province_name;
            $cityInitial = strtoupper($cityInfo->city_initial);
            
            // 如果省份不存在，则添加新的省份
            if (!isset($provinceMap[$provinceCode])) {
                $provinceData = [
                    'code' => $provinceCode,
                    'name' => $provinceName,
                    'children' => []
                ];
                $result[] = $provinceData;
                $provinceMap[$provinceCode] = count($result) - 1;
            }
            
            // 添加城市到对应省份的children中
            $cityData = [
                'code' => $cityInfo->city_code,
                'name' => $cityInfo->city_name,
                'index' => $cityInitial
            ];
            $result[$provinceMap[$provinceCode]]['children'][] = $cityData;
        }
        
        // 将结果存入缓存（缓存7天）
        Cache::set($cacheKey, $result, 86400 * 7);
        
        return success($result);
    }

    /**
     * 国外城市信息（按国家分组）
     * 
     * @param Request $request
     * @return Response
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function overseas(Request $request)
    {
        // 定义缓存键
        $cacheKey = 'city_info_overseas';
        
        // 检查缓存是否存在
        if (Cache::has($cacheKey)) {
            return success(Cache::get($cacheKey));
        }
        
        // 缓存不存在，查询数据库
        // 获取所有国家及其城市数据
        $countries = Db::table('countries')
            ->alias('c')
            ->field([
                'c.id as country_id', 
                'c.name as country_name', 
                'c.name_en as country_name_en', 
                'c.initial as country_initial'
            ])
            ->select();
        
        $result = [];
        $countryMap = [];
        
        // 构建国家层级
        foreach ($countries as $country) {
            $countryData = [
                'code' => $country['country_id'],
                'name' => $country['country_name']  . "({$country['country_name_en']})",
                'children' => []
            ];
            
            $result[] = $countryData;
            $countryMap[$country['country_id']] = count($result) - 1;
        }
        
        // 获取并关联城市数据
        $cities = Db::table('cities')
            ->field([
                'id', 
                'country_id',
                'name', 
                'name_en',
                'city_code',
                'poi_id', 
                'initial'
            ])
            ->select();
        
        // 将城市添加到对应国家下
        foreach ($cities as $city) {
            $countryId = $city['country_id'];
            
            // 如果国家存在于结果集中
            if (isset($countryMap[$countryId])) {
                $cityInitial = strtoupper($city['initial'] ?: substr($city['name'], 0, 1));
                
                $cityData = [
                    'name' => $city['name'] . "({$city['name_en']})",
                    'code' => $city['city_code'],
                    'index' => $cityInitial
                ];
                
                $result[$countryMap[$countryId]]['children'][] = $cityData;
            }
        }
        
        // 排序：先按国家首字母排序，再对每个国家下的城市按首字母排序
        foreach ($result as &$country) {
            usort($country['children'], function($a, $b) {
                return $a['index'] <=> $b['index'];
            });
        }
        
        // 将结果存入缓存（缓存1天）
        Cache::set($cacheKey, $result, 86400 * 7);
        
        return success($result);
    }
} 