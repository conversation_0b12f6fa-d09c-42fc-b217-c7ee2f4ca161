<?php

use Phinx\Migration\AbstractMigration;

class CreateItineraryThemesTable extends AbstractMigration
{
    /**
     * 创建线路主题表
     */
    public function change()
    {
        $table = $this->table('itinerary_themes', ['id' => true, 'comment' => '线路主题表']);
        $table->addColumn('name', 'string', ['limit' => 50, 'null' => false, 'comment' => '主题名称'])
              ->addColumn('icon', 'string', ['limit' => 100, 'null' => true, 'comment' => '图标类名或标识'])
              ->addColumn('image_url', 'string', ['limit' => 255, 'null' => true, 'comment' => '图片完整URL地址'])
              ->addColumn('create_time', 'integer', ['null' => false, 'default' => 0, 'comment' => '创建时间'])
              ->addColumn('update_time', 'integer', ['null' => false, 'default' => 0, 'comment' => '更新时间'])
              ->addIndex(['name'], ['unique' => true])
              ->create();
    }
} 