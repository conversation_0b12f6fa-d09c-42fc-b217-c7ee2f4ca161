<?php

namespace base\AuditLog\traits;

trait Scene
{
    /**
     * 场景
     *
     * @var array
     */
    protected array $scene = [];
    /**
     * 当前使用场景
     *
     * @var string
     */
    private string $currentScene = '';

    /**
     * 设置验证场景
     *
     * @access public
     *
     * @param string $name 场景名
     *
     * @return $this
     */
    public function scene(string $name): self
    {
        // 设置当前场景
        $this->currentScene = $name;

        return $this;
    }

    /**
     * 获取数据验证的场景
     *
     * @access protected
     *
     * @param string $scene 验证场景
     *
     * @return void
     */
    private function getScene(string $scene): void
    {
        if (method_exists($this, 'scene' . $scene)) {
            $this->only = $this->append = $this->remove = [];
            $this->{'scene' . $scene}();
        } elseif (isset($this->scene[$scene])) {
            // 如果设置了验证适用场景
            $this->only = $this->scene[$scene];
        }
    }
}
