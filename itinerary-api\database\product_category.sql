

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for product_category
-- ----------------------------
DROP TABLE IF EXISTS `product_category`;
CREATE TABLE `product_category`  (
  `category_id` bigint(20) NOT NULL COMMENT '分类ID',
  `parent_id` bigint(20) NULL DEFAULT 0 COMMENT '父级分类ID，0表示顶级分类',
  `category_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '分类名称',
  `category_level` int(11) NOT NULL DEFAULT 1 COMMENT '分类层级，1为一级分类',
  `access_status` tinyint(4) NULL DEFAULT 1 COMMENT '状态：1-可用，2-受限',
  `category_discard` tinyint(1) NULL DEFAULT 0 COMMENT '是否废弃',
  `allow_access_scope` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '允许访问的产品范围',
  `no_access_scope` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '不允许访问的产品范围',
  `detail_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '详情URL',
  `price_limit` decimal(10, 2) NULL DEFAULT NULL COMMENT '价格限制',
  `sort_order` int(11) NULL DEFAULT 0 COMMENT '排序顺序',
  `create_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  `delete_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '删除时间',
  PRIMARY KEY (`category_id`) USING BTREE,
  INDEX `idx_parent_id`(`parent_id`) USING BTREE,
  INDEX `idx_category_name`(`category_name`) USING BTREE,
  INDEX `idx_access_status`(`access_status`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商品分类表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of product_category
-- ----------------------------
INSERT INTO `product_category` VALUES (4015004, 18003000, '滑雪场', 3, 1, 0, '单滑雪票', '住宿', '', NULL, 0, 1747797378, 1747797378, 0);
INSERT INTO `product_category` VALUES (8000000, 0, '住宿', 1, 2, 0, '', '', '', NULL, 0, 1747797378, 1747797378, 0);
INSERT INTO `product_category` VALUES (8001000, 8000000, '酒店宾馆', 2, 1, 0, '', '', '', NULL, 0, 1747797378, 1747797378, 0);
INSERT INTO `product_category` VALUES (8001001, 8001000, '经济型酒店', 3, 1, 0, '住宿+选填（酒店内餐饮/酒店内权益或硬件服务）', '住宿+旅游服务组合打包品', '', NULL, 0, 1747797378, 1747797378, 0);
INSERT INTO `product_category` VALUES (8001002, 8001000, '舒适型酒店', 3, 1, 0, '住宿+选填（酒店内餐饮/酒店内权益或硬件服务）', '住宿+旅游服务组合打包品', '', NULL, 0, 1747797378, 1747797378, 0);
INSERT INTO `product_category` VALUES (8001003, 8001000, '高档型酒店', 3, 1, 0, '住宿+选填（酒店内餐饮/酒店内权益或硬件服务）', '住宿+旅游服务组合打包品', '', 5000.00, 0, 1747797378, 1747797378, 0);
INSERT INTO `product_category` VALUES (8001004, 8001000, '豪华型酒店', 3, 1, 0, '住宿+选填（酒店内餐饮/酒店内权益或硬件服务）', '住宿+旅游服务组合打包品', '', 5000.00, 0, 1747797378, 1747797378, 0);
INSERT INTO `product_category` VALUES (8002000, 8000000, '客栈民宿', 2, 1, 0, '', '', '', NULL, 0, 1747797378, 1747797378, 0);
INSERT INTO `product_category` VALUES (8002001, 8002000, '客栈民宿', 3, 1, 0, '住宿+选填（酒店内餐饮/酒店内权益或硬件服务）', '', '', 5000.00, 0, 1747797378, 1747797378, 0);
INSERT INTO `product_category` VALUES (8003000, 8000000, '其他住宿', 2, 1, 0, '', '', '', NULL, 0, 1747797378, 1747797378, 0);
INSERT INTO `product_category` VALUES (8003001, 8003000, '其他住宿服务', 3, 1, 0, '', '', '', NULL, 0, 1747797378, 1747797378, 0);
INSERT INTO `product_category` VALUES (8004000, 8000000, '境外酒店', 2, 1, 0, '', '', '', NULL, 0, 1747797378, 1747797378, 0);
INSERT INTO `product_category` VALUES (8004001, 8004000, '境外酒店住宿', 3, 1, 0, '住宿+选填（酒店内餐饮/酒店内权益或硬件服务）', '住宿+旅游服务组合打包品', '', NULL, 0, 1747797378, 1747797378, 0);
INSERT INTO `product_category` VALUES (8004002, 8004000, '港澳住宿', 3, 1, 0, '住宿+选填（酒店内餐饮/酒店内权益或硬件服务）', '住宿+旅游服务（司导/行程安排等）组合打包品', '', NULL, 0, 1747797378, 1747797378, 0);
INSERT INTO `product_category` VALUES (8005000, 8000000, '境内酒景套餐', 2, 1, 0, '', '', '', NULL, 0, 1747797378, 1747797378, 0);
INSERT INTO `product_category` VALUES (8005001, 8005000, '境内酒店套餐', 3, 1, 0, '酒店住宿+景点门票/游玩项目+选填（酒店内餐饮/酒店内权益或硬件服务）', '跨城交通/司导服务/单住宿/单景点门票/单游玩项目', '', NULL, 0, 1747797378, 1747797378, 0);
INSERT INTO `product_category` VALUES (8006000, 8000000, '境外酒景套餐', 2, 1, 0, '', '', '', NULL, 0, 1747797378, 1747797378, 0);
INSERT INTO `product_category` VALUES (8006001, 8006000, '境外酒景套餐（不含港澳）', 3, 1, 0, '酒店住宿+景点门票/游玩项目+选填（酒店内餐饮/酒店内权益或硬件服务）', '跨城交通/司导服务/单住宿/单景点门票/单游玩项目', '', NULL, 0, 1747797378, 1747797378, 0);
INSERT INTO `product_category` VALUES (8006002, 8006000, '港澳酒景套餐', 3, 1, 0, '酒店住宿+景点门票/游玩项目+选填（酒店内餐饮/酒店内权益或硬件服务）', '跨城交通/司导服务/单住宿/单景点门票/单游玩项目', '', NULL, 0, 1747797378, 1747797378, 0);
INSERT INTO `product_category` VALUES (18000000, 0, '游玩', 1, 2, 0, '', '', '', NULL, 0, 1747797378, 1747808634, 0);
INSERT INTO `product_category` VALUES (18003000, 18000000, '游玩项目', 2, 1, 0, '', '', '', NULL, 0, 1747797378, 1747797378, 0);
INSERT INTO `product_category` VALUES (18003001, 18003000, '水上体验', 3, 1, 0, '水上或水下进行的单个活动项目/门票（游泳、潜水、冲浪、漂流、划船、皮划艇、帆船出海、游艇、摩托艇、水上飞人、海上飞鱼滑翔/滑翔伞/热气球/三角翼滑板/滑旱冰、沙滩车、水上乐园、溪降、玻璃水滑道等', '', '', NULL, 0, 1747797378, 1747797378, 0);
INSERT INTO `product_category` VALUES (18003002, 18003000, '城市/高空观光', 3, 1, 0, '攀岩,高空滑索、高空跳伞,翼装飞行,蹦极,跳伞,滑翔伞,热气球,直升机观光体验,高空秋千,高空玻璃栈道等', '', '', NULL, 0, 1747797378, 1747797378, 0);
INSERT INTO `product_category` VALUES (18003004, 18003000, '温泉', 3, 1, 0, '单温泉门票', '住宿', '', NULL, 0, 1747797378, 1747797378, 0);
INSERT INTO `product_category` VALUES (18003007, 18003000, '国内短途游轮', 3, 1, 0, '', '', '', NULL, 0, 1747797378, 1747797378, 0);
INSERT INTO `product_category` VALUES (18003008, 18003000, '国内长途游轮', 3, 1, 0, '仅可发布国内长途游轮商品', '仅可发布国内长途游轮商品', '', NULL, 0, 1747797378, 1747797378, 0);
INSERT INTO `product_category` VALUES (18004000, 18000000, '景点票券', 2, 2, 0, '', '', '', NULL, 0, 1747797378, 1747797378, 0);
INSERT INTO `product_category` VALUES (18004001, 18004000, '单景点门票', 3, 1, 0, '单景点门票或单项目票（自然景观、人文古迹、古镇/古村、植物园、动物园、生态园、冰雪乐园、游乐园、水族馆、影视基地、博物馆、会展中心、纪念馆、科技馆、蜡像馆、美术馆、天文馆、展览馆、公园广场、地标建筑、观光街区）', '多项目的组合品（如门票+园内餐饮/讲解/住宿、门票A+门票B）；“游玩项目”二级类目下的商品（水上体验、温泉、滑雪场、高空体验等门票或项目票）', '', NULL, 0, 1747797378, 1747797378, 0);
INSERT INTO `product_category` VALUES (18004002, 18004000, '园内餐饮', 3, 1, 0, '景区内单餐饮类商品', '', '', NULL, 0, 1747797378, 1747797378, 0);
INSERT INTO `product_category` VALUES (18004003, 18004000, '园内交通', 3, 1, 0, '景区内通勤/摆渡的单交通类商品（例如园内的索道、小火车、小游船、小电车票、泰山山脚-景区大门的摆渡车等）', '', '', NULL, 0, 1747797378, 1747797378, 0);
INSERT INTO `product_category` VALUES (18004004, 18004000, '园内游玩项目', 3, 1, 0, '景区乐园类商家，园内二销项目售卖使用。', '除园内二销的收费项目外，其他禁入', '', NULL, 0, 1747797378, 1747797378, 0);
INSERT INTO `product_category` VALUES (18004005, 18004000, '园内讲解', 3, 1, 0, '景区内单讲解服务', '', '', NULL, 0, 1747797378, 1747797378, 0);
INSERT INTO `product_category` VALUES (18004006, 18004000, '景区纪念品', 3, 1, 0, '单独的景区/景点内的特色工艺品、多种工艺品组合', '除景区纪念品外商品禁入', '', NULL, 0, 1747797378, 1747797378, 0);
INSERT INTO `product_category` VALUES (18004007, 18004000, '多景点联票', 3, 1, 0, '多个景点门票的组合类商品', '非景点门票的商品（例如：景点A+景点B+园内餐饮）', '', NULL, 0, 1747797378, 1747797378, 0);
INSERT INTO `product_category` VALUES (18004008, 18004000, '园内套票', 3, 1, 0, '同一景点内的门票+不同服务/游玩项目的组合、不同服务+游玩项目的组合（门票+园内演出/餐饮/纪念品等）', '', '', NULL, 0, 1747797378, 1747797378, 0);
INSERT INTO `product_category` VALUES (18004009, 18004000, '园内套票（不含门票）', 3, 1, 0, '同一景点内的门票+不同服务/游玩项目的组合、不同服务+游玩项目的组合（门票+园内演出/餐饮/纪念品等）', '跟随通用规则', '', NULL, 0, 1747797378, 1747797378, 0);
INSERT INTO `product_category` VALUES (18004010, 18004000, '周期卡（年/季/月卡等）', 3, 1, 0, '景区/景点的周期卡（年卡、季卡等）商品', '', '', NULL, 0, 1747797378, 1747797378, 0);
INSERT INTO `product_category` VALUES (18004011, 18004000, '临时室内展览', 3, 1, 0, '室内举办的各类主题展览/展秀【XX主题展、灯光展、艺术展、画展、漫画展等】', '通用类的禁限售场景', '', NULL, 0, 1747797378, 1747797378, 0);
INSERT INTO `product_category` VALUES (18004012, 18004000, '临时室外展览', 3, 1, 0, '室外露天举办的各类展秀活动【例如：花卉展、庙会、民俗展、烟花秀、主题巡游、灯展、面包节/咖啡节等市集类活动等】', '通用类的禁限售场景', '', NULL, 0, 1747797378, 1747797378, 0);
INSERT INTO `product_category` VALUES (18011000, 18000000, '境外票务', 2, 1, 0, '', '', '', NULL, 0, 1747797378, 1747797378, 0);
INSERT INTO `product_category` VALUES (18011001, 18011000, '境外景区', 3, 1, 0, '境外景区的门票商品，包：\n1. 主题乐园、公园、名胜古迹遗迹、影视基地、动植物园； \n2.博物馆、科技馆、艺术馆、展览馆（及以馆内常设展）、室内体验馆、密室逃脱； 3.景区内的项目(索道、滑道、接驳车等)；', '', '', NULL, 0, 1747797378, 1747797378, 0);
INSERT INTO `product_category` VALUES (18012000, 18000000, '港澳票务', 2, 1, 0, '', '', '', NULL, 0, 1747797378, 1747797378, 0);
INSERT INTO `product_category` VALUES (18012001, 18012000, '港澳票务', 3, 1, 0, '单景/多景/景+游乐', '司导服务', '', NULL, 0, 1747797378, 1747797378, 0);
INSERT INTO `product_category` VALUES (18012002, 18012000, '港澳乐园', 3, 1, 0, '单游乐/多游乐', '司导服务', '', NULL, 0, 1747797378, 1747797378, 0);
INSERT INTO `product_category` VALUES (32000000, 0, '度假旅游服务', 1, 1, 0, '', '', '', NULL, 0, 1747797378, 1747797378, 0);
INSERT INTO `product_category` VALUES (32002000, 32000000, '出境行程游', 2, 1, 0, '', '', '', NULL, 0, 1747797378, 1747797378, 0);
INSERT INTO `product_category` VALUES (32002001, 32002000, '出境自由行', 3, 1, 0, '出发地到目的地交通+选填（境内外餐饮/住宿/景点/其他服务）', '司机/导游及行程安排服务', '', NULL, 0, 1747797378, 1747797378, 0);
INSERT INTO `product_category` VALUES (32002002, 32002000, '出境跟团游', 3, 1, 0, '出发地到目的地交通+司机/导游+行程安排服务+选填（境内外餐饮/住宿/景点/其他服务）', '', '', NULL, 0, 1747797378, 1747797378, 0);
INSERT INTO `product_category` VALUES (32002003, 32002000, '出境私家团', 3, 1, 0, '境外（含港澳）、跨境交通、包含服务（司导/行程安排）、无购物、一单一团', '', '', NULL, 0, 1747797378, 1747797378, 0);
INSERT INTO `product_category` VALUES (32003000, 32000000, '出境目的地玩乐', 2, 1, 0, '', '', '', NULL, 0, 1747797378, 1747797378, 0);
INSERT INTO `product_category` VALUES (32003001, 32003000, '境外一日游', 3, 1, 0, '行程天数≤1天，司导服务/行程安排+景点', '住宿、出发地到目的地交通', '', NULL, 0, 1747797378, 1747797378, 0);
INSERT INTO `product_category` VALUES (32003002, 32003000, '境外多日游', 3, 1, 0, '行程天数≥1天，司导服务/行程安排+景点', '出发地到目的地交通', '', NULL, 0, 1747797378, 1747797378, 0);
INSERT INTO `product_category` VALUES (32004000, 32000000, '邮轮', 2, 0, 0, '', '', '', NULL, 0, 1747797378, 1747797378, 0);
INSERT INTO `product_category` VALUES (32004001, 32004000, '境外邮轮', 3, 1, 0, '可发布任一出发地至中国大陆以外目的地或途径大陆以外地点的的邮轮/游轮商品（境外途径地/目的地仅可包含目前处境游已开放国家和地区）', '出发地、目的地、途经地均为中国大陆境内地区，不支持发布包含未开放目的国家/地区的商品', '', NULL, 0, 1747797378, 1747797378, 0);
INSERT INTO `product_category` VALUES (32006000, 32000000, '境内行程游', 2, 1, 0, '', '', '', NULL, 0, 1747797378, 1747797378, 0);
INSERT INTO `product_category` VALUES (32006001, 32006000, '境内自由行', 3, 1, 0, '出发地到目的地交通+选填（餐饮/住宿/景点/其他服务）', '司机/导游及行程安排服务', '', NULL, 0, 1747797378, 1747797378, 0);
INSERT INTO `product_category` VALUES (32006002, 32006000, '境内跟团游', 3, 1, 0, '出发地到目的地交通+司机/导游+行程安排服务+选填（餐饮/住宿/景点/其他服务）', '', '', NULL, 0, 1747797378, 1747797378, 0);
INSERT INTO `product_category` VALUES (32007000, 32000000, '境内目的地玩乐', 2, 1, 0, '', '', '', NULL, 0, 1747797378, 1747797378, 0);
INSERT INTO `product_category` VALUES (32007001, 32007000, '境内一日游', 3, 1, 0, '行程天数≤1天，司导服务/行程安排+景点', '住宿、出发地到目的地交通', '', NULL, 0, 1747797378, 1747797378, 0);
INSERT INTO `product_category` VALUES (32007002, 32007000, '境内二日游', 3, 1, 0, '行程天数>1天&≤2天，司导服务/行程安排+景点', '出发地到目的地交通', '', NULL, 0, 1747797378, 1747797378, 0);
INSERT INTO `product_category` VALUES (32007003, 32007000, '境内多日游', 3, 1, 0, '行程天数>2天，司导服务/行程安排+景点', '出发地到目的地交通', '', NULL, 0, 1747797378, 1747797378, 0);

SET FOREIGN_KEY_CHECKS = 1;
