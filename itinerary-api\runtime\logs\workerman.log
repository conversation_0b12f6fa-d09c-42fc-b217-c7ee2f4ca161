2025-05-07 11:26:00 pid:1 ErrorException: Undefined array key 1 in E:\code\iam\booking-api\vendor\topthink\think-orm\src\db\Builder.php:259
Stack trace:
#0 E:\code\iam\booking-api\vendor\topthink\think-orm\src\db\Builder.php(259): {closure}()
#1 E:\code\iam\booking-api\vendor\topthink\think-orm\src\db\BaseBuilder.php(279): think\db\Builder->parseWhereItem()
#2 E:\code\iam\booking-api\vendor\topthink\think-orm\src\db\BaseBuilder.php(223): think\db\BaseBuilder->parseWhereLogic()
#3 E:\code\iam\booking-api\vendor\topthink\think-orm\src\db\Builder.php(230): think\db\BaseBuilder->buildWhere()
#4 E:\code\iam\booking-api\vendor\topthink\think-orm\src\db\builder\Mysql.php(103): think\db\Builder->parseWhere()
#5 E:\code\iam\booking-api\vendor\topthink\think-orm\src\db\PDOConnection.php(974): think\db\builder\Mysql->select()
#6 E:\code\iam\booking-api\vendor\topthink\think-orm\src\db\PDOConnection.php(739): think\db\PDOConnection->think\db\{closure}()
#7 E:\code\iam\booking-api\vendor\topthink\think-orm\src\db\PDOConnection.php(973): think\db\PDOConnection->pdoQuery()
#8 E:\code\iam\booking-api\vendor\topthink\think-orm\src\db\BaseQuery.php(1466): think\db\PDOConnection->select()
#9 E:\code\iam\booking-api\app\crontab\OrderCrontab.php(19): think\db\BaseQuery->select()
#10 E:\code\iam\booking-api\app\process\Task.php(20): app\crontab\OrderCrontab->checkOrderStatus()
#11 E:\code\iam\booking-api\vendor\workerman\workerman\src\Events\Select.php(481): app\process\Task->app\process\{closure}()
#12 E:\code\iam\booking-api\vendor\workerman\workerman\src\Events\Select.php(340): Workerman\Events\Select->safeCall()
#13 E:\code\iam\booking-api\vendor\workerman\workerman\src\Events\Select.php(427): Workerman\Events\Select->tick()
#14 E:\code\iam\booking-api\vendor\workerman\workerman\src\Worker.php(1602): Workerman\Events\Select->run()
#15 E:\code\iam\booking-api\vendor\workerman\workerman\src\Worker.php(1527): Workerman\Worker::forkWorkersForWindows()
#16 E:\code\iam\booking-api\vendor\workerman\workerman\src\Worker.php(593): Workerman\Worker::forkWorkers()
#17 E:\code\iam\booking-api\runtime\windows\start_task.php(33): Workerman\Worker::runAll()
#18 {main}
2025-05-07 11:27:01 pid:1 ErrorException: Undefined array key 1 in E:\code\iam\booking-api\vendor\topthink\think-orm\src\db\Builder.php:259
Stack trace:
#0 E:\code\iam\booking-api\vendor\topthink\think-orm\src\db\Builder.php(259): {closure}()
#1 E:\code\iam\booking-api\vendor\topthink\think-orm\src\db\BaseBuilder.php(279): think\db\Builder->parseWhereItem()
#2 E:\code\iam\booking-api\vendor\topthink\think-orm\src\db\BaseBuilder.php(223): think\db\BaseBuilder->parseWhereLogic()
#3 E:\code\iam\booking-api\vendor\topthink\think-orm\src\db\Builder.php(230): think\db\BaseBuilder->buildWhere()
#4 E:\code\iam\booking-api\vendor\topthink\think-orm\src\db\builder\Mysql.php(103): think\db\Builder->parseWhere()
#5 E:\code\iam\booking-api\vendor\topthink\think-orm\src\db\PDOConnection.php(974): think\db\builder\Mysql->select()
#6 E:\code\iam\booking-api\vendor\topthink\think-orm\src\db\PDOConnection.php(739): think\db\PDOConnection->think\db\{closure}()
#7 E:\code\iam\booking-api\vendor\topthink\think-orm\src\db\PDOConnection.php(973): think\db\PDOConnection->pdoQuery()
#8 E:\code\iam\booking-api\vendor\topthink\think-orm\src\db\BaseQuery.php(1466): think\db\PDOConnection->select()
#9 E:\code\iam\booking-api\app\crontab\OrderCrontab.php(19): think\db\BaseQuery->select()
#10 E:\code\iam\booking-api\app\process\Task.php(20): app\crontab\OrderCrontab->checkOrderStatus()
#11 E:\code\iam\booking-api\vendor\workerman\workerman\src\Events\Select.php(481): app\process\Task->app\process\{closure}()
#12 E:\code\iam\booking-api\vendor\workerman\workerman\src\Events\Select.php(340): Workerman\Events\Select->safeCall()
#13 E:\code\iam\booking-api\vendor\workerman\workerman\src\Events\Select.php(427): Workerman\Events\Select->tick()
#14 E:\code\iam\booking-api\vendor\workerman\workerman\src\Worker.php(1602): Workerman\Events\Select->run()
#15 E:\code\iam\booking-api\vendor\workerman\workerman\src\Worker.php(1527): Workerman\Worker::forkWorkersForWindows()
#16 E:\code\iam\booking-api\vendor\workerman\workerman\src\Worker.php(593): Workerman\Worker::forkWorkers()
#17 E:\code\iam\booking-api\runtime\windows\start_task.php(33): Workerman\Worker::runAll()
#18 {main}
2025-05-07 11:28:01 pid:1 ErrorException: Undefined array key 1 in E:\code\iam\booking-api\vendor\topthink\think-orm\src\db\Builder.php:259
Stack trace:
#0 E:\code\iam\booking-api\vendor\topthink\think-orm\src\db\Builder.php(259): {closure}()
#1 E:\code\iam\booking-api\vendor\topthink\think-orm\src\db\BaseBuilder.php(279): think\db\Builder->parseWhereItem()
#2 E:\code\iam\booking-api\vendor\topthink\think-orm\src\db\BaseBuilder.php(223): think\db\BaseBuilder->parseWhereLogic()
#3 E:\code\iam\booking-api\vendor\topthink\think-orm\src\db\Builder.php(230): think\db\BaseBuilder->buildWhere()
#4 E:\code\iam\booking-api\vendor\topthink\think-orm\src\db\builder\Mysql.php(103): think\db\Builder->parseWhere()
#5 E:\code\iam\booking-api\vendor\topthink\think-orm\src\db\PDOConnection.php(974): think\db\builder\Mysql->select()
#6 E:\code\iam\booking-api\vendor\topthink\think-orm\src\db\PDOConnection.php(739): think\db\PDOConnection->think\db\{closure}()
#7 E:\code\iam\booking-api\vendor\topthink\think-orm\src\db\PDOConnection.php(973): think\db\PDOConnection->pdoQuery()
#8 E:\code\iam\booking-api\vendor\topthink\think-orm\src\db\BaseQuery.php(1466): think\db\PDOConnection->select()
#9 E:\code\iam\booking-api\app\crontab\OrderCrontab.php(19): think\db\BaseQuery->select()
#10 E:\code\iam\booking-api\app\process\Task.php(20): app\crontab\OrderCrontab->checkOrderStatus()
#11 E:\code\iam\booking-api\vendor\workerman\workerman\src\Events\Select.php(481): app\process\Task->app\process\{closure}()
#12 E:\code\iam\booking-api\vendor\workerman\workerman\src\Events\Select.php(340): Workerman\Events\Select->safeCall()
#13 E:\code\iam\booking-api\vendor\workerman\workerman\src\Events\Select.php(427): Workerman\Events\Select->tick()
#14 E:\code\iam\booking-api\vendor\workerman\workerman\src\Worker.php(1602): Workerman\Events\Select->run()
#15 E:\code\iam\booking-api\vendor\workerman\workerman\src\Worker.php(1527): Workerman\Worker::forkWorkersForWindows()
#16 E:\code\iam\booking-api\vendor\workerman\workerman\src\Worker.php(593): Workerman\Worker::forkWorkers()
#17 E:\code\iam\booking-api\runtime\windows\start_task.php(33): Workerman\Worker::runAll()
#18 {main}
2025-05-07 11:29:00 pid:1 ErrorException: Undefined array key 1 in E:\code\iam\booking-api\vendor\topthink\think-orm\src\db\Builder.php:259
Stack trace:
#0 E:\code\iam\booking-api\vendor\topthink\think-orm\src\db\Builder.php(259): {closure}()
#1 E:\code\iam\booking-api\vendor\topthink\think-orm\src\db\BaseBuilder.php(279): think\db\Builder->parseWhereItem()
#2 E:\code\iam\booking-api\vendor\topthink\think-orm\src\db\BaseBuilder.php(223): think\db\BaseBuilder->parseWhereLogic()
#3 E:\code\iam\booking-api\vendor\topthink\think-orm\src\db\Builder.php(230): think\db\BaseBuilder->buildWhere()
#4 E:\code\iam\booking-api\vendor\topthink\think-orm\src\db\builder\Mysql.php(103): think\db\Builder->parseWhere()
#5 E:\code\iam\booking-api\vendor\topthink\think-orm\src\db\PDOConnection.php(974): think\db\builder\Mysql->select()
#6 E:\code\iam\booking-api\vendor\topthink\think-orm\src\db\PDOConnection.php(739): think\db\PDOConnection->think\db\{closure}()
#7 E:\code\iam\booking-api\vendor\topthink\think-orm\src\db\PDOConnection.php(973): think\db\PDOConnection->pdoQuery()
#8 E:\code\iam\booking-api\vendor\topthink\think-orm\src\db\BaseQuery.php(1466): think\db\PDOConnection->select()
#9 E:\code\iam\booking-api\app\crontab\OrderCrontab.php(19): think\db\BaseQuery->select()
#10 E:\code\iam\booking-api\app\process\Task.php(20): app\crontab\OrderCrontab->checkOrderStatus()
#11 E:\code\iam\booking-api\vendor\workerman\workerman\src\Events\Select.php(481): app\process\Task->app\process\{closure}()
#12 E:\code\iam\booking-api\vendor\workerman\workerman\src\Events\Select.php(340): Workerman\Events\Select->safeCall()
#13 E:\code\iam\booking-api\vendor\workerman\workerman\src\Events\Select.php(427): Workerman\Events\Select->tick()
#14 E:\code\iam\booking-api\vendor\workerman\workerman\src\Worker.php(1602): Workerman\Events\Select->run()
#15 E:\code\iam\booking-api\vendor\workerman\workerman\src\Worker.php(1527): Workerman\Worker::forkWorkersForWindows()
#16 E:\code\iam\booking-api\vendor\workerman\workerman\src\Worker.php(593): Workerman\Worker::forkWorkers()
#17 E:\code\iam\booking-api\runtime\windows\start_task.php(33): Workerman\Worker::runAll()
#18 {main}
2025-05-07 11:30:00 pid:1 ErrorException: Undefined array key 1 in E:\code\iam\booking-api\vendor\topthink\think-orm\src\db\Builder.php:259
Stack trace:
#0 E:\code\iam\booking-api\vendor\topthink\think-orm\src\db\Builder.php(259): {closure}()
#1 E:\code\iam\booking-api\vendor\topthink\think-orm\src\db\BaseBuilder.php(279): think\db\Builder->parseWhereItem()
#2 E:\code\iam\booking-api\vendor\topthink\think-orm\src\db\BaseBuilder.php(223): think\db\BaseBuilder->parseWhereLogic()
#3 E:\code\iam\booking-api\vendor\topthink\think-orm\src\db\Builder.php(230): think\db\BaseBuilder->buildWhere()
#4 E:\code\iam\booking-api\vendor\topthink\think-orm\src\db\builder\Mysql.php(103): think\db\Builder->parseWhere()
#5 E:\code\iam\booking-api\vendor\topthink\think-orm\src\db\PDOConnection.php(974): think\db\builder\Mysql->select()
#6 E:\code\iam\booking-api\vendor\topthink\think-orm\src\db\PDOConnection.php(739): think\db\PDOConnection->think\db\{closure}()
#7 E:\code\iam\booking-api\vendor\topthink\think-orm\src\db\PDOConnection.php(973): think\db\PDOConnection->pdoQuery()
#8 E:\code\iam\booking-api\vendor\topthink\think-orm\src\db\BaseQuery.php(1466): think\db\PDOConnection->select()
#9 E:\code\iam\booking-api\app\crontab\OrderCrontab.php(19): think\db\BaseQuery->select()
#10 E:\code\iam\booking-api\app\process\Task.php(20): app\crontab\OrderCrontab->checkOrderStatus()
#11 E:\code\iam\booking-api\vendor\workerman\workerman\src\Events\Select.php(481): app\process\Task->app\process\{closure}()
#12 E:\code\iam\booking-api\vendor\workerman\workerman\src\Events\Select.php(340): Workerman\Events\Select->safeCall()
#13 E:\code\iam\booking-api\vendor\workerman\workerman\src\Events\Select.php(427): Workerman\Events\Select->tick()
#14 E:\code\iam\booking-api\vendor\workerman\workerman\src\Worker.php(1602): Workerman\Events\Select->run()
#15 E:\code\iam\booking-api\vendor\workerman\workerman\src\Worker.php(1527): Workerman\Worker::forkWorkersForWindows()
#16 E:\code\iam\booking-api\vendor\workerman\workerman\src\Worker.php(593): Workerman\Worker::forkWorkers()
#17 E:\code\iam\booking-api\runtime\windows\start_task.php(33): Workerman\Worker::runAll()
#18 {main}
2025-05-07 11:31:00 pid:1 ErrorException: Undefined array key 1 in E:\code\iam\booking-api\vendor\topthink\think-orm\src\db\Builder.php:259
Stack trace:
#0 E:\code\iam\booking-api\vendor\topthink\think-orm\src\db\Builder.php(259): {closure}()
#1 E:\code\iam\booking-api\vendor\topthink\think-orm\src\db\BaseBuilder.php(279): think\db\Builder->parseWhereItem()
#2 E:\code\iam\booking-api\vendor\topthink\think-orm\src\db\BaseBuilder.php(223): think\db\BaseBuilder->parseWhereLogic()
#3 E:\code\iam\booking-api\vendor\topthink\think-orm\src\db\Builder.php(230): think\db\BaseBuilder->buildWhere()
#4 E:\code\iam\booking-api\vendor\topthink\think-orm\src\db\builder\Mysql.php(103): think\db\Builder->parseWhere()
#5 E:\code\iam\booking-api\vendor\topthink\think-orm\src\db\PDOConnection.php(974): think\db\builder\Mysql->select()
#6 E:\code\iam\booking-api\vendor\topthink\think-orm\src\db\PDOConnection.php(739): think\db\PDOConnection->think\db\{closure}()
#7 E:\code\iam\booking-api\vendor\topthink\think-orm\src\db\PDOConnection.php(973): think\db\PDOConnection->pdoQuery()
#8 E:\code\iam\booking-api\vendor\topthink\think-orm\src\db\BaseQuery.php(1466): think\db\PDOConnection->select()
#9 E:\code\iam\booking-api\app\crontab\OrderCrontab.php(19): think\db\BaseQuery->select()
#10 E:\code\iam\booking-api\app\process\Task.php(20): app\crontab\OrderCrontab->checkOrderStatus()
#11 E:\code\iam\booking-api\vendor\workerman\workerman\src\Events\Select.php(481): app\process\Task->app\process\{closure}()
#12 E:\code\iam\booking-api\vendor\workerman\workerman\src\Events\Select.php(340): Workerman\Events\Select->safeCall()
#13 E:\code\iam\booking-api\vendor\workerman\workerman\src\Events\Select.php(427): Workerman\Events\Select->tick()
#14 E:\code\iam\booking-api\vendor\workerman\workerman\src\Worker.php(1602): Workerman\Events\Select->run()
#15 E:\code\iam\booking-api\vendor\workerman\workerman\src\Worker.php(1527): Workerman\Worker::forkWorkersForWindows()
#16 E:\code\iam\booking-api\vendor\workerman\workerman\src\Worker.php(593): Workerman\Worker::forkWorkers()
#17 E:\code\iam\booking-api\runtime\windows\start_task.php(33): Workerman\Worker::runAll()
#18 {main}
2025-05-07 11:32:00 pid:1 ErrorException: Undefined array key 1 in E:\code\iam\booking-api\vendor\topthink\think-orm\src\db\Builder.php:259
Stack trace:
#0 E:\code\iam\booking-api\vendor\topthink\think-orm\src\db\Builder.php(259): {closure}()
#1 E:\code\iam\booking-api\vendor\topthink\think-orm\src\db\BaseBuilder.php(279): think\db\Builder->parseWhereItem()
#2 E:\code\iam\booking-api\vendor\topthink\think-orm\src\db\BaseBuilder.php(223): think\db\BaseBuilder->parseWhereLogic()
#3 E:\code\iam\booking-api\vendor\topthink\think-orm\src\db\Builder.php(230): think\db\BaseBuilder->buildWhere()
#4 E:\code\iam\booking-api\vendor\topthink\think-orm\src\db\builder\Mysql.php(103): think\db\Builder->parseWhere()
#5 E:\code\iam\booking-api\vendor\topthink\think-orm\src\db\PDOConnection.php(974): think\db\builder\Mysql->select()
#6 E:\code\iam\booking-api\vendor\topthink\think-orm\src\db\PDOConnection.php(739): think\db\PDOConnection->think\db\{closure}()
#7 E:\code\iam\booking-api\vendor\topthink\think-orm\src\db\PDOConnection.php(973): think\db\PDOConnection->pdoQuery()
#8 E:\code\iam\booking-api\vendor\topthink\think-orm\src\db\BaseQuery.php(1466): think\db\PDOConnection->select()
#9 E:\code\iam\booking-api\app\crontab\OrderCrontab.php(19): think\db\BaseQuery->select()
#10 E:\code\iam\booking-api\app\process\Task.php(20): app\crontab\OrderCrontab->checkOrderStatus()
#11 E:\code\iam\booking-api\vendor\workerman\workerman\src\Events\Select.php(481): app\process\Task->app\process\{closure}()
#12 E:\code\iam\booking-api\vendor\workerman\workerman\src\Events\Select.php(340): Workerman\Events\Select->safeCall()
#13 E:\code\iam\booking-api\vendor\workerman\workerman\src\Events\Select.php(427): Workerman\Events\Select->tick()
#14 E:\code\iam\booking-api\vendor\workerman\workerman\src\Worker.php(1602): Workerman\Events\Select->run()
#15 E:\code\iam\booking-api\vendor\workerman\workerman\src\Worker.php(1527): Workerman\Worker::forkWorkersForWindows()
#16 E:\code\iam\booking-api\vendor\workerman\workerman\src\Worker.php(593): Workerman\Worker::forkWorkers()
#17 E:\code\iam\booking-api\runtime\windows\start_task.php(33): Workerman\Worker::runAll()
#18 {main}
2025-05-15 13:59:47 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 13:59:47 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 13:59:47 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 13:59:47 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 13:59:49 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 13:59:49 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 13:59:49 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 13:59:50 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 13:59:50 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 13:59:50 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 13:59:50 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 13:59:52 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 13:59:52 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 13:59:52 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 13:59:53 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 13:59:53 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 13:59:53 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 13:59:53 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 13:59:55 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 13:59:55 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 13:59:55 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 13:59:57 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 13:59:57 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 13:59:57 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 13:59:57 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 13:59:58 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 13:59:58 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 13:59:58 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 14:00:00 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 14:00:00 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 14:00:00 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 14:00:00 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 14:00:02 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 14:00:02 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 14:00:02 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 14:00:03 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 14:00:03 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 14:00:03 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 14:00:03 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 14:00:05 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 14:00:05 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 14:00:05 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 14:00:06 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 14:00:06 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 14:00:06 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 14:00:06 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 14:00:08 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 14:00:08 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 14:00:08 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 14:00:10 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 14:00:10 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 14:00:10 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 14:00:10 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 14:00:11 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 14:00:11 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 14:00:11 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 14:00:13 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 14:00:13 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 14:00:13 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 14:00:13 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 14:00:14 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 14:00:14 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 14:00:14 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 14:00:17 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 14:00:17 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 14:00:17 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 14:00:17 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 14:00:18 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 14:00:18 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 14:00:18 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 14:00:18 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 14:00:18 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 14:00:18 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 14:00:18 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 14:00:20 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 14:00:20 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 14:00:20 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 14:00:22 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 14:00:22 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 14:00:22 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 14:00:22 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 14:00:23 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 14:00:23 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 14:00:23 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 14:00:25 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 14:00:25 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 14:00:25 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 14:00:25 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 14:00:27 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 14:00:27 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 14:00:27 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 14:00:28 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 14:00:28 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 14:00:28 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 14:00:28 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 14:00:30 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 14:00:30 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 14:00:30 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 14:00:32 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 14:00:32 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 14:00:32 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 14:00:32 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 14:00:33 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 14:00:33 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 14:00:33 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 14:00:35 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 14:00:35 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 14:00:35 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 14:00:35 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 14:00:36 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 14:00:36 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 14:00:36 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 14:00:38 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 14:00:38 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 14:00:38 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 14:00:38 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 14:00:40 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 14:00:40 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 14:00:40 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 14:00:41 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 14:00:41 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 14:00:41 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 14:00:41 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 14:00:43 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 14:00:43 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 14:00:43 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 14:00:45 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 14:00:45 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 14:00:45 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 14:00:45 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 14:00:45 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 14:00:45 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 14:00:45 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 14:00:45 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 14:00:47 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 14:00:47 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 14:00:47 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 14:00:48 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 14:00:48 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 14:00:48 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 14:00:48 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 14:00:50 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 14:00:50 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 14:00:50 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 14:00:51 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 14:00:51 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 14:00:51 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 14:00:51 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 14:00:53 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 14:00:53 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 14:00:53 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 14:00:55 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 14:00:55 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 14:00:55 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 14:00:55 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 14:00:56 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 14:00:56 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-05-15 14:00:56 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot use think\facade\Route as Route because the name is already in use in E:\code\iam\itinerary-api\config\route.php on line 17"
2025-06-09 14:13:42 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot declare class app\website\controller\Theme, because the name is already in use in E:\code\iam\itinerary-api\app\api\controller\Theme.php on line 9"
2025-06-09 14:13:42 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot declare class app\website\controller\Theme, because the name is already in use in E:\code\iam\itinerary-api\app\api\controller\Theme.php on line 9"
2025-06-09 14:13:42 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot declare class app\website\controller\Theme, because the name is already in use in E:\code\iam\itinerary-api\app\api\controller\Theme.php on line 9"
2025-06-09 14:13:42 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot declare class app\website\controller\Theme, because the name is already in use in E:\code\iam\itinerary-api\app\api\controller\Theme.php on line 9"
2025-06-09 14:13:43 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot declare class app\website\controller\Theme, because the name is already in use in E:\code\iam\itinerary-api\app\api\controller\Theme.php on line 9"
2025-06-09 14:13:43 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot declare class app\website\controller\Theme, because the name is already in use in E:\code\iam\itinerary-api\app\api\controller\Theme.php on line 9"
2025-06-09 14:13:43 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot declare class app\website\controller\Theme, because the name is already in use in E:\code\iam\itinerary-api\app\api\controller\Theme.php on line 9"
2025-06-09 14:13:43 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot declare class app\website\controller\Theme, because the name is already in use in E:\code\iam\itinerary-api\app\api\controller\Theme.php on line 9"
2025-06-09 14:13:45 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot declare class app\website\controller\Theme, because the name is already in use in E:\code\iam\itinerary-api\app\api\controller\Theme.php on line 9"
2025-06-09 14:13:45 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot declare class app\website\controller\Theme, because the name is already in use in E:\code\iam\itinerary-api\app\api\controller\Theme.php on line 9"
2025-06-09 14:13:45 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot declare class app\website\controller\Theme, because the name is already in use in E:\code\iam\itinerary-api\app\api\controller\Theme.php on line 9"
2025-06-09 14:13:46 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot declare class app\website\controller\Theme, because the name is already in use in E:\code\iam\itinerary-api\app\api\controller\Theme.php on line 9"
2025-06-09 14:13:46 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot declare class app\website\controller\Theme, because the name is already in use in E:\code\iam\itinerary-api\app\api\controller\Theme.php on line 9"
2025-06-09 14:13:46 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot declare class app\website\controller\Theme, because the name is already in use in E:\code\iam\itinerary-api\app\api\controller\Theme.php on line 9"
2025-06-09 14:13:46 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot declare class app\website\controller\Theme, because the name is already in use in E:\code\iam\itinerary-api\app\api\controller\Theme.php on line 9"
2025-06-09 14:13:48 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot declare class app\website\controller\Theme, because the name is already in use in E:\code\iam\itinerary-api\app\api\controller\Theme.php on line 9"
2025-06-09 14:13:48 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot declare class app\website\controller\Theme, because the name is already in use in E:\code\iam\itinerary-api\app\api\controller\Theme.php on line 9"
2025-06-09 14:13:48 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot declare class app\website\controller\Theme, because the name is already in use in E:\code\iam\itinerary-api\app\api\controller\Theme.php on line 9"
2025-06-09 14:13:49 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot declare class app\website\controller\Theme, because the name is already in use in E:\code\iam\itinerary-api\app\api\controller\Theme.php on line 9"
2025-06-09 14:13:49 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot declare class app\website\controller\Theme, because the name is already in use in E:\code\iam\itinerary-api\app\api\controller\Theme.php on line 9"
2025-06-09 14:13:49 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot declare class app\website\controller\Theme, because the name is already in use in E:\code\iam\itinerary-api\app\api\controller\Theme.php on line 9"
2025-06-09 14:13:49 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot declare class app\website\controller\Theme, because the name is already in use in E:\code\iam\itinerary-api\app\api\controller\Theme.php on line 9"
2025-06-09 14:13:51 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot declare class app\website\controller\Theme, because the name is already in use in E:\code\iam\itinerary-api\app\api\controller\Theme.php on line 9"
2025-06-09 14:13:51 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot declare class app\website\controller\Theme, because the name is already in use in E:\code\iam\itinerary-api\app\api\controller\Theme.php on line 9"
2025-06-09 14:13:51 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot declare class app\website\controller\Theme, because the name is already in use in E:\code\iam\itinerary-api\app\api\controller\Theme.php on line 9"
2025-06-09 14:13:52 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot declare class app\website\controller\Theme, because the name is already in use in E:\code\iam\itinerary-api\app\api\controller\Theme.php on line 9"
2025-06-09 14:13:52 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot declare class app\website\controller\Theme, because the name is already in use in E:\code\iam\itinerary-api\app\api\controller\Theme.php on line 9"
2025-06-09 14:13:52 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot declare class app\website\controller\Theme, because the name is already in use in E:\code\iam\itinerary-api\app\api\controller\Theme.php on line 9"
2025-06-09 14:13:52 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot declare class app\website\controller\Theme, because the name is already in use in E:\code\iam\itinerary-api\app\api\controller\Theme.php on line 9"
2025-06-09 14:13:54 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot declare class app\website\controller\Theme, because the name is already in use in E:\code\iam\itinerary-api\app\api\controller\Theme.php on line 9"
2025-06-09 14:13:54 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot declare class app\website\controller\Theme, because the name is already in use in E:\code\iam\itinerary-api\app\api\controller\Theme.php on line 9"
2025-06-09 14:13:54 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot declare class app\website\controller\Theme, because the name is already in use in E:\code\iam\itinerary-api\app\api\controller\Theme.php on line 9"
2025-06-09 14:13:56 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot declare class app\website\controller\Theme, because the name is already in use in E:\code\iam\itinerary-api\app\api\controller\Theme.php on line 9"
2025-06-09 14:13:56 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot declare class app\website\controller\Theme, because the name is already in use in E:\code\iam\itinerary-api\app\api\controller\Theme.php on line 9"
2025-06-09 14:13:56 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot declare class app\website\controller\Theme, because the name is already in use in E:\code\iam\itinerary-api\app\api\controller\Theme.php on line 9"
2025-06-09 14:13:56 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot declare class app\website\controller\Theme, because the name is already in use in E:\code\iam\itinerary-api\app\api\controller\Theme.php on line 9"
2025-06-09 14:13:57 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot declare class app\website\controller\Theme, because the name is already in use in E:\code\iam\itinerary-api\app\api\controller\Theme.php on line 9"
2025-06-09 14:13:57 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot declare class app\website\controller\Theme, because the name is already in use in E:\code\iam\itinerary-api\app\api\controller\Theme.php on line 9"
2025-06-09 14:13:57 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot declare class app\website\controller\Theme, because the name is already in use in E:\code\iam\itinerary-api\app\api\controller\Theme.php on line 9"
2025-06-09 14:13:59 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot declare class app\website\controller\Theme, because the name is already in use in E:\code\iam\itinerary-api\app\api\controller\Theme.php on line 9"
2025-06-09 14:13:59 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot declare class app\website\controller\Theme, because the name is already in use in E:\code\iam\itinerary-api\app\api\controller\Theme.php on line 9"
2025-06-09 14:13:59 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot declare class app\website\controller\Theme, because the name is already in use in E:\code\iam\itinerary-api\app\api\controller\Theme.php on line 9"
2025-06-09 14:13:59 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot declare class app\website\controller\Theme, because the name is already in use in E:\code\iam\itinerary-api\app\api\controller\Theme.php on line 9"
2025-06-09 14:14:01 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot declare class app\website\controller\Theme, because the name is already in use in E:\code\iam\itinerary-api\app\api\controller\Theme.php on line 9"
2025-06-09 14:14:01 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot declare class app\website\controller\Theme, because the name is already in use in E:\code\iam\itinerary-api\app\api\controller\Theme.php on line 9"
2025-06-09 14:14:01 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot declare class app\website\controller\Theme, because the name is already in use in E:\code\iam\itinerary-api\app\api\controller\Theme.php on line 9"
2025-06-09 14:14:02 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot declare class app\website\controller\Theme, because the name is already in use in E:\code\iam\itinerary-api\app\api\controller\Theme.php on line 9"
2025-06-09 14:14:02 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot declare class app\website\controller\Theme, because the name is already in use in E:\code\iam\itinerary-api\app\api\controller\Theme.php on line 9"
2025-06-09 14:14:02 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot declare class app\website\controller\Theme, because the name is already in use in E:\code\iam\itinerary-api\app\api\controller\Theme.php on line 9"
2025-06-09 14:14:02 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot declare class app\website\controller\Theme, because the name is already in use in E:\code\iam\itinerary-api\app\api\controller\Theme.php on line 9"
2025-06-09 14:14:04 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot declare class app\website\controller\Theme, because the name is already in use in E:\code\iam\itinerary-api\app\api\controller\Theme.php on line 9"
2025-06-09 14:14:04 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot declare class app\website\controller\Theme, because the name is already in use in E:\code\iam\itinerary-api\app\api\controller\Theme.php on line 9"
2025-06-09 14:14:04 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot declare class app\website\controller\Theme, because the name is already in use in E:\code\iam\itinerary-api\app\api\controller\Theme.php on line 9"
2025-06-09 14:14:05 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot declare class app\website\controller\Theme, because the name is already in use in E:\code\iam\itinerary-api\app\api\controller\Theme.php on line 9"
2025-06-09 14:14:05 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot declare class app\website\controller\Theme, because the name is already in use in E:\code\iam\itinerary-api\app\api\controller\Theme.php on line 9"
2025-06-09 14:14:05 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot declare class app\website\controller\Theme, because the name is already in use in E:\code\iam\itinerary-api\app\api\controller\Theme.php on line 9"
2025-06-09 14:14:05 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot declare class app\website\controller\Theme, because the name is already in use in E:\code\iam\itinerary-api\app\api\controller\Theme.php on line 9"
2025-06-09 14:14:07 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot declare class app\website\controller\Theme, because the name is already in use in E:\code\iam\itinerary-api\app\api\controller\Theme.php on line 9"
2025-06-09 14:14:07 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot declare class app\website\controller\Theme, because the name is already in use in E:\code\iam\itinerary-api\app\api\controller\Theme.php on line 9"
2025-06-09 14:14:07 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot declare class app\website\controller\Theme, because the name is already in use in E:\code\iam\itinerary-api\app\api\controller\Theme.php on line 9"
2025-06-09 14:14:09 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot declare class app\website\controller\Theme, because the name is already in use in E:\code\iam\itinerary-api\app\api\controller\Theme.php on line 9"
2025-06-09 14:14:09 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot declare class app\website\controller\Theme, because the name is already in use in E:\code\iam\itinerary-api\app\api\controller\Theme.php on line 9"
2025-06-09 14:14:09 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot declare class app\website\controller\Theme, because the name is already in use in E:\code\iam\itinerary-api\app\api\controller\Theme.php on line 9"
2025-06-09 14:14:09 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot declare class app\website\controller\Theme, because the name is already in use in E:\code\iam\itinerary-api\app\api\controller\Theme.php on line 9"
2025-06-09 14:14:10 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot declare class app\website\controller\Theme, because the name is already in use in E:\code\iam\itinerary-api\app\api\controller\Theme.php on line 9"
2025-06-09 14:14:10 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot declare class app\website\controller\Theme, because the name is already in use in E:\code\iam\itinerary-api\app\api\controller\Theme.php on line 9"
2025-06-09 14:14:10 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot declare class app\website\controller\Theme, because the name is already in use in E:\code\iam\itinerary-api\app\api\controller\Theme.php on line 9"
2025-06-09 14:14:12 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot declare class app\website\controller\Theme, because the name is already in use in E:\code\iam\itinerary-api\app\api\controller\Theme.php on line 9"
2025-06-09 14:14:12 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot declare class app\website\controller\Theme, because the name is already in use in E:\code\iam\itinerary-api\app\api\controller\Theme.php on line 9"
2025-06-09 14:14:12 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot declare class app\website\controller\Theme, because the name is already in use in E:\code\iam\itinerary-api\app\api\controller\Theme.php on line 9"
2025-06-09 14:14:12 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot declare class app\website\controller\Theme, because the name is already in use in E:\code\iam\itinerary-api\app\api\controller\Theme.php on line 9"
2025-06-09 14:14:14 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot declare class app\website\controller\Theme, because the name is already in use in E:\code\iam\itinerary-api\app\api\controller\Theme.php on line 9"
2025-06-09 14:14:14 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot declare class app\website\controller\Theme, because the name is already in use in E:\code\iam\itinerary-api\app\api\controller\Theme.php on line 9"
2025-06-09 14:14:14 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot declare class app\website\controller\Theme, because the name is already in use in E:\code\iam\itinerary-api\app\api\controller\Theme.php on line 9"
2025-08-06 14:33:51 pid:1 ParseError: syntax error, unexpected variable "$result" in D:\work\hotel\all\itinerary-api\app\api\controller\User.php:81
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\composer\ClassLoader.php(427): Composer\Autoload\{closure}('D:\\work\\hotel\\a...')
#1 [internal function]: Composer\Autoload\ClassLoader->loadClass('app\\api\\control...')
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(452): class_exists('app\\api\\control...')
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(472): Webman\Route::convertToCallable('/register', Array)
#4 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(132): Webman\Route::addRoute('POST', '/register', Array)
#5 D:\work\hotel\all\itinerary-api\config\route.php(37): Webman\Route::post('/register', Array)
#6 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#7 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/api/user', Object(Closure))
#8 D:\work\hotel\all\itinerary-api\config\route.php(35): Webman\Route::group('/api/user', Object(Closure))
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#10 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#11 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#12 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#14 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#15 [internal function]: Workerman\Worker->Workerman\{closure}()
#16 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#17 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#18 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#19 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#20 D:\work\hotel\all\itinerary-api\runtime\windows\start_webman.php(33): Workerman\Worker::runAll()
#21 {main}
2025-08-06 14:33:51 pid:1 ParseError: syntax error, unexpected variable "$result" in D:\work\hotel\all\itinerary-api\app\api\controller\User.php:81
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\composer\ClassLoader.php(427): Composer\Autoload\{closure}('D:\\work\\hotel\\a...')
#1 [internal function]: Composer\Autoload\ClassLoader->loadClass('app\\api\\control...')
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(452): class_exists('app\\api\\control...')
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(472): Webman\Route::convertToCallable('/register', Array)
#4 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(132): Webman\Route::addRoute('POST', '/register', Array)
#5 D:\work\hotel\all\itinerary-api\config\route.php(37): Webman\Route::post('/register', Array)
#6 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#7 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/api/user', Object(Closure))
#8 D:\work\hotel\all\itinerary-api\config\route.php(35): Webman\Route::group('/api/user', Object(Closure))
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#10 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#11 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#12 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#14 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#15 [internal function]: Workerman\Worker->Workerman\{closure}()
#16 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#17 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#18 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#19 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#20 D:\work\hotel\all\itinerary-api\runtime\windows\start_monitor.php(33): Workerman\Worker::runAll()
#21 {main}
2025-08-06 14:33:51 pid:1 ParseError: syntax error, unexpected variable "$result" in D:\work\hotel\all\itinerary-api\app\api\controller\User.php:81
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\composer\ClassLoader.php(427): Composer\Autoload\{closure}('D:\\work\\hotel\\a...')
#1 [internal function]: Composer\Autoload\ClassLoader->loadClass('app\\api\\control...')
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(452): class_exists('app\\api\\control...')
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(472): Webman\Route::convertToCallable('/register', Array)
#4 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(132): Webman\Route::addRoute('POST', '/register', Array)
#5 D:\work\hotel\all\itinerary-api\config\route.php(37): Webman\Route::post('/register', Array)
#6 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#7 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/api/user', Object(Closure))
#8 D:\work\hotel\all\itinerary-api\config\route.php(35): Webman\Route::group('/api/user', Object(Closure))
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#10 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#11 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#12 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#14 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#15 [internal function]: Workerman\Worker->Workerman\{closure}()
#16 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#17 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#18 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#19 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#20 D:\work\hotel\all\itinerary-api\runtime\windows\start_plugin.webman.redis-queue.consumer.php(33): Workerman\Worker::runAll()
#21 {main}
2025-08-06 14:33:54 pid:1 ParseError: syntax error, unexpected variable "$result" in D:\work\hotel\all\itinerary-api\app\api\controller\User.php:81
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\composer\ClassLoader.php(427): Composer\Autoload\{closure}('D:\\work\\hotel\\a...')
#1 [internal function]: Composer\Autoload\ClassLoader->loadClass('app\\api\\control...')
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(452): class_exists('app\\api\\control...')
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(472): Webman\Route::convertToCallable('/register', Array)
#4 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(132): Webman\Route::addRoute('POST', '/register', Array)
#5 D:\work\hotel\all\itinerary-api\config\route.php(37): Webman\Route::post('/register', Array)
#6 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#7 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/api/user', Object(Closure))
#8 D:\work\hotel\all\itinerary-api\config\route.php(35): Webman\Route::group('/api/user', Object(Closure))
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#10 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#11 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#12 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#14 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#15 [internal function]: Workerman\Worker->Workerman\{closure}()
#16 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#17 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#18 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#19 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#20 D:\work\hotel\all\itinerary-api\runtime\windows\start_plugin.webman.redis-queue.consumer.php(33): Workerman\Worker::runAll()
#21 {main}
2025-08-06 14:33:54 pid:1 ParseError: syntax error, unexpected variable "$result" in D:\work\hotel\all\itinerary-api\app\api\controller\User.php:81
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\composer\ClassLoader.php(427): Composer\Autoload\{closure}('D:\\work\\hotel\\a...')
#1 [internal function]: Composer\Autoload\ClassLoader->loadClass('app\\api\\control...')
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(452): class_exists('app\\api\\control...')
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(472): Webman\Route::convertToCallable('/register', Array)
#4 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(132): Webman\Route::addRoute('POST', '/register', Array)
#5 D:\work\hotel\all\itinerary-api\config\route.php(37): Webman\Route::post('/register', Array)
#6 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#7 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/api/user', Object(Closure))
#8 D:\work\hotel\all\itinerary-api\config\route.php(35): Webman\Route::group('/api/user', Object(Closure))
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#10 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#11 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#12 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#14 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#15 [internal function]: Workerman\Worker->Workerman\{closure}()
#16 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#17 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#18 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#19 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#20 D:\work\hotel\all\itinerary-api\runtime\windows\start_webman.php(33): Workerman\Worker::runAll()
#21 {main}
2025-08-06 14:33:54 pid:1 ParseError: syntax error, unexpected variable "$result" in D:\work\hotel\all\itinerary-api\app\api\controller\User.php:81
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\composer\ClassLoader.php(427): Composer\Autoload\{closure}('D:\\work\\hotel\\a...')
#1 [internal function]: Composer\Autoload\ClassLoader->loadClass('app\\api\\control...')
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(452): class_exists('app\\api\\control...')
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(472): Webman\Route::convertToCallable('/register', Array)
#4 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(132): Webman\Route::addRoute('POST', '/register', Array)
#5 D:\work\hotel\all\itinerary-api\config\route.php(37): Webman\Route::post('/register', Array)
#6 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#7 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/api/user', Object(Closure))
#8 D:\work\hotel\all\itinerary-api\config\route.php(35): Webman\Route::group('/api/user', Object(Closure))
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#10 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#11 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#12 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#14 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#15 [internal function]: Workerman\Worker->Workerman\{closure}()
#16 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#17 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#18 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#19 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#20 D:\work\hotel\all\itinerary-api\runtime\windows\start_monitor.php(33): Workerman\Worker::runAll()
#21 {main}
2025-08-06 14:33:57 pid:1 ParseError: syntax error, unexpected variable "$result" in D:\work\hotel\all\itinerary-api\app\api\controller\User.php:81
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\composer\ClassLoader.php(427): Composer\Autoload\{closure}('D:\\work\\hotel\\a...')
#1 [internal function]: Composer\Autoload\ClassLoader->loadClass('app\\api\\control...')
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(452): class_exists('app\\api\\control...')
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(472): Webman\Route::convertToCallable('/register', Array)
#4 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(132): Webman\Route::addRoute('POST', '/register', Array)
#5 D:\work\hotel\all\itinerary-api\config\route.php(37): Webman\Route::post('/register', Array)
#6 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#7 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/api/user', Object(Closure))
#8 D:\work\hotel\all\itinerary-api\config\route.php(35): Webman\Route::group('/api/user', Object(Closure))
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#10 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#11 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#12 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#14 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#15 [internal function]: Workerman\Worker->Workerman\{closure}()
#16 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#17 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#18 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#19 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#20 D:\work\hotel\all\itinerary-api\runtime\windows\start_plugin.webman.redis-queue.consumer.php(33): Workerman\Worker::runAll()
#21 {main}
2025-08-06 14:33:57 pid:1 ParseError: syntax error, unexpected variable "$result" in D:\work\hotel\all\itinerary-api\app\api\controller\User.php:81
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\composer\ClassLoader.php(427): Composer\Autoload\{closure}('D:\\work\\hotel\\a...')
#1 [internal function]: Composer\Autoload\ClassLoader->loadClass('app\\api\\control...')
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(452): class_exists('app\\api\\control...')
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(472): Webman\Route::convertToCallable('/register', Array)
#4 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(132): Webman\Route::addRoute('POST', '/register', Array)
#5 D:\work\hotel\all\itinerary-api\config\route.php(37): Webman\Route::post('/register', Array)
#6 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#7 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/api/user', Object(Closure))
#8 D:\work\hotel\all\itinerary-api\config\route.php(35): Webman\Route::group('/api/user', Object(Closure))
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#10 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#11 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#12 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#14 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#15 [internal function]: Workerman\Worker->Workerman\{closure}()
#16 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#17 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#18 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#19 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#20 D:\work\hotel\all\itinerary-api\runtime\windows\start_monitor.php(33): Workerman\Worker::runAll()
#21 {main}
2025-08-06 14:33:57 pid:1 ParseError: syntax error, unexpected variable "$result" in D:\work\hotel\all\itinerary-api\app\api\controller\User.php:81
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\composer\ClassLoader.php(427): Composer\Autoload\{closure}('D:\\work\\hotel\\a...')
#1 [internal function]: Composer\Autoload\ClassLoader->loadClass('app\\api\\control...')
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(452): class_exists('app\\api\\control...')
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(472): Webman\Route::convertToCallable('/register', Array)
#4 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(132): Webman\Route::addRoute('POST', '/register', Array)
#5 D:\work\hotel\all\itinerary-api\config\route.php(37): Webman\Route::post('/register', Array)
#6 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#7 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/api/user', Object(Closure))
#8 D:\work\hotel\all\itinerary-api\config\route.php(35): Webman\Route::group('/api/user', Object(Closure))
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#10 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#11 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#12 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#14 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#15 [internal function]: Workerman\Worker->Workerman\{closure}()
#16 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#17 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#18 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#19 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#20 D:\work\hotel\all\itinerary-api\runtime\windows\start_webman.php(33): Workerman\Worker::runAll()
#21 {main}
2025-08-06 14:41:04 pid:1 ParseError: syntax error, unexpected token "}" in D:\work\hotel\all\itinerary-api\app\api\controller\User.php:84
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\composer\ClassLoader.php(427): Composer\Autoload\{closure}('D:\\work\\hotel\\a...')
#1 [internal function]: Composer\Autoload\ClassLoader->loadClass('app\\api\\control...')
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(452): class_exists('app\\api\\control...')
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(472): Webman\Route::convertToCallable('/register', Array)
#4 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(132): Webman\Route::addRoute('POST', '/register', Array)
#5 D:\work\hotel\all\itinerary-api\config\route.php(37): Webman\Route::post('/register', Array)
#6 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#7 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/api/user', Object(Closure))
#8 D:\work\hotel\all\itinerary-api\config\route.php(35): Webman\Route::group('/api/user', Object(Closure))
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#10 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#11 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#12 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#14 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#15 [internal function]: Workerman\Worker->Workerman\{closure}()
#16 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#17 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#18 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#19 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#20 D:\work\hotel\all\itinerary-api\runtime\windows\start_plugin.webman.redis-queue.consumer.php(33): Workerman\Worker::runAll()
#21 {main}
2025-08-06 14:41:04 pid:1 ParseError: syntax error, unexpected token "}" in D:\work\hotel\all\itinerary-api\app\api\controller\User.php:84
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\composer\ClassLoader.php(427): Composer\Autoload\{closure}('D:\\work\\hotel\\a...')
#1 [internal function]: Composer\Autoload\ClassLoader->loadClass('app\\api\\control...')
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(452): class_exists('app\\api\\control...')
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(472): Webman\Route::convertToCallable('/register', Array)
#4 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(132): Webman\Route::addRoute('POST', '/register', Array)
#5 D:\work\hotel\all\itinerary-api\config\route.php(37): Webman\Route::post('/register', Array)
#6 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#7 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/api/user', Object(Closure))
#8 D:\work\hotel\all\itinerary-api\config\route.php(35): Webman\Route::group('/api/user', Object(Closure))
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#10 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#11 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#12 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#14 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#15 [internal function]: Workerman\Worker->Workerman\{closure}()
#16 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#17 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#18 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#19 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#20 D:\work\hotel\all\itinerary-api\runtime\windows\start_monitor.php(33): Workerman\Worker::runAll()
#21 {main}
2025-08-06 14:41:05 pid:1 ParseError: syntax error, unexpected token "}" in D:\work\hotel\all\itinerary-api\app\api\controller\User.php:84
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\composer\ClassLoader.php(427): Composer\Autoload\{closure}('D:\\work\\hotel\\a...')
#1 [internal function]: Composer\Autoload\ClassLoader->loadClass('app\\api\\control...')
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(452): class_exists('app\\api\\control...')
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(472): Webman\Route::convertToCallable('/register', Array)
#4 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(132): Webman\Route::addRoute('POST', '/register', Array)
#5 D:\work\hotel\all\itinerary-api\config\route.php(37): Webman\Route::post('/register', Array)
#6 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#7 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/api/user', Object(Closure))
#8 D:\work\hotel\all\itinerary-api\config\route.php(35): Webman\Route::group('/api/user', Object(Closure))
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#10 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#11 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#12 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#14 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#15 [internal function]: Workerman\Worker->Workerman\{closure}()
#16 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#17 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#18 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#19 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#20 D:\work\hotel\all\itinerary-api\runtime\windows\start_webman.php(33): Workerman\Worker::runAll()
#21 {main}
2025-08-06 14:41:07 pid:1 ParseError: syntax error, unexpected token "}" in D:\work\hotel\all\itinerary-api\app\api\controller\User.php:84
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\composer\ClassLoader.php(427): Composer\Autoload\{closure}('D:\\work\\hotel\\a...')
#1 [internal function]: Composer\Autoload\ClassLoader->loadClass('app\\api\\control...')
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(452): class_exists('app\\api\\control...')
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(472): Webman\Route::convertToCallable('/register', Array)
#4 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(132): Webman\Route::addRoute('POST', '/register', Array)
#5 D:\work\hotel\all\itinerary-api\config\route.php(37): Webman\Route::post('/register', Array)
#6 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#7 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/api/user', Object(Closure))
#8 D:\work\hotel\all\itinerary-api\config\route.php(35): Webman\Route::group('/api/user', Object(Closure))
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#10 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#11 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#12 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#14 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#15 [internal function]: Workerman\Worker->Workerman\{closure}()
#16 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#17 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#18 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#19 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#20 D:\work\hotel\all\itinerary-api\runtime\windows\start_plugin.webman.redis-queue.consumer.php(33): Workerman\Worker::runAll()
#21 {main}
2025-08-06 14:41:07 pid:1 ParseError: syntax error, unexpected token "}" in D:\work\hotel\all\itinerary-api\app\api\controller\User.php:84
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\composer\ClassLoader.php(427): Composer\Autoload\{closure}('D:\\work\\hotel\\a...')
#1 [internal function]: Composer\Autoload\ClassLoader->loadClass('app\\api\\control...')
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(452): class_exists('app\\api\\control...')
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(472): Webman\Route::convertToCallable('/register', Array)
#4 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(132): Webman\Route::addRoute('POST', '/register', Array)
#5 D:\work\hotel\all\itinerary-api\config\route.php(37): Webman\Route::post('/register', Array)
#6 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#7 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/api/user', Object(Closure))
#8 D:\work\hotel\all\itinerary-api\config\route.php(35): Webman\Route::group('/api/user', Object(Closure))
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#10 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#11 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#12 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#14 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#15 [internal function]: Workerman\Worker->Workerman\{closure}()
#16 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#17 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#18 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#19 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#20 D:\work\hotel\all\itinerary-api\runtime\windows\start_monitor.php(33): Workerman\Worker::runAll()
#21 {main}
2025-08-06 14:41:07 pid:1 ParseError: syntax error, unexpected token "}" in D:\work\hotel\all\itinerary-api\app\api\controller\User.php:84
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\composer\ClassLoader.php(427): Composer\Autoload\{closure}('D:\\work\\hotel\\a...')
#1 [internal function]: Composer\Autoload\ClassLoader->loadClass('app\\api\\control...')
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(452): class_exists('app\\api\\control...')
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(472): Webman\Route::convertToCallable('/register', Array)
#4 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(132): Webman\Route::addRoute('POST', '/register', Array)
#5 D:\work\hotel\all\itinerary-api\config\route.php(37): Webman\Route::post('/register', Array)
#6 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#7 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/api/user', Object(Closure))
#8 D:\work\hotel\all\itinerary-api\config\route.php(35): Webman\Route::group('/api/user', Object(Closure))
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#10 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#11 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#12 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#14 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#15 [internal function]: Workerman\Worker->Workerman\{closure}()
#16 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#17 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#18 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#19 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#20 D:\work\hotel\all\itinerary-api\runtime\windows\start_webman.php(33): Workerman\Worker::runAll()
#21 {main}
2025-08-06 14:41:09 pid:1 ParseError: syntax error, unexpected token "}" in D:\work\hotel\all\itinerary-api\app\api\controller\User.php:84
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\composer\ClassLoader.php(427): Composer\Autoload\{closure}('D:\\work\\hotel\\a...')
#1 [internal function]: Composer\Autoload\ClassLoader->loadClass('app\\api\\control...')
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(452): class_exists('app\\api\\control...')
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(472): Webman\Route::convertToCallable('/register', Array)
#4 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(132): Webman\Route::addRoute('POST', '/register', Array)
#5 D:\work\hotel\all\itinerary-api\config\route.php(37): Webman\Route::post('/register', Array)
#6 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#7 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/api/user', Object(Closure))
#8 D:\work\hotel\all\itinerary-api\config\route.php(35): Webman\Route::group('/api/user', Object(Closure))
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#10 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#11 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#12 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#14 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#15 [internal function]: Workerman\Worker->Workerman\{closure}()
#16 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#17 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#18 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#19 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#20 D:\work\hotel\all\itinerary-api\runtime\windows\start_plugin.webman.redis-queue.consumer.php(33): Workerman\Worker::runAll()
#21 {main}
2025-08-06 14:41:09 pid:1 ParseError: syntax error, unexpected token "}" in D:\work\hotel\all\itinerary-api\app\api\controller\User.php:84
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\composer\ClassLoader.php(427): Composer\Autoload\{closure}('D:\\work\\hotel\\a...')
#1 [internal function]: Composer\Autoload\ClassLoader->loadClass('app\\api\\control...')
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(452): class_exists('app\\api\\control...')
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(472): Webman\Route::convertToCallable('/register', Array)
#4 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(132): Webman\Route::addRoute('POST', '/register', Array)
#5 D:\work\hotel\all\itinerary-api\config\route.php(37): Webman\Route::post('/register', Array)
#6 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#7 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/api/user', Object(Closure))
#8 D:\work\hotel\all\itinerary-api\config\route.php(35): Webman\Route::group('/api/user', Object(Closure))
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#10 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#11 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#12 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#14 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#15 [internal function]: Workerman\Worker->Workerman\{closure}()
#16 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#17 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#18 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#19 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#20 D:\work\hotel\all\itinerary-api\runtime\windows\start_monitor.php(33): Workerman\Worker::runAll()
#21 {main}
2025-08-06 14:41:09 pid:1 ParseError: syntax error, unexpected token "}" in D:\work\hotel\all\itinerary-api\app\api\controller\User.php:84
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\composer\ClassLoader.php(427): Composer\Autoload\{closure}('D:\\work\\hotel\\a...')
#1 [internal function]: Composer\Autoload\ClassLoader->loadClass('app\\api\\control...')
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(452): class_exists('app\\api\\control...')
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(472): Webman\Route::convertToCallable('/register', Array)
#4 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(132): Webman\Route::addRoute('POST', '/register', Array)
#5 D:\work\hotel\all\itinerary-api\config\route.php(37): Webman\Route::post('/register', Array)
#6 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#7 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/api/user', Object(Closure))
#8 D:\work\hotel\all\itinerary-api\config\route.php(35): Webman\Route::group('/api/user', Object(Closure))
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#10 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#11 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#12 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#14 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#15 [internal function]: Workerman\Worker->Workerman\{closure}()
#16 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#17 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#18 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#19 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#20 D:\work\hotel\all\itinerary-api\runtime\windows\start_webman.php(33): Workerman\Worker::runAll()
#21 {main}
2025-08-06 14:41:12 pid:1 ParseError: syntax error, unexpected token "}" in D:\work\hotel\all\itinerary-api\app\api\controller\User.php:84
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\composer\ClassLoader.php(427): Composer\Autoload\{closure}('D:\\work\\hotel\\a...')
#1 [internal function]: Composer\Autoload\ClassLoader->loadClass('app\\api\\control...')
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(452): class_exists('app\\api\\control...')
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(472): Webman\Route::convertToCallable('/register', Array)
#4 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(132): Webman\Route::addRoute('POST', '/register', Array)
#5 D:\work\hotel\all\itinerary-api\config\route.php(37): Webman\Route::post('/register', Array)
#6 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#7 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/api/user', Object(Closure))
#8 D:\work\hotel\all\itinerary-api\config\route.php(35): Webman\Route::group('/api/user', Object(Closure))
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#10 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#11 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#12 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#14 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#15 [internal function]: Workerman\Worker->Workerman\{closure}()
#16 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#17 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#18 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#19 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#20 D:\work\hotel\all\itinerary-api\runtime\windows\start_monitor.php(33): Workerman\Worker::runAll()
#21 {main}
2025-08-06 14:41:12 pid:1 ParseError: syntax error, unexpected token "}" in D:\work\hotel\all\itinerary-api\app\api\controller\User.php:84
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\composer\ClassLoader.php(427): Composer\Autoload\{closure}('D:\\work\\hotel\\a...')
#1 [internal function]: Composer\Autoload\ClassLoader->loadClass('app\\api\\control...')
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(452): class_exists('app\\api\\control...')
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(472): Webman\Route::convertToCallable('/register', Array)
#4 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(132): Webman\Route::addRoute('POST', '/register', Array)
#5 D:\work\hotel\all\itinerary-api\config\route.php(37): Webman\Route::post('/register', Array)
#6 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#7 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/api/user', Object(Closure))
#8 D:\work\hotel\all\itinerary-api\config\route.php(35): Webman\Route::group('/api/user', Object(Closure))
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#10 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#11 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#12 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#14 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#15 [internal function]: Workerman\Worker->Workerman\{closure}()
#16 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#17 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#18 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#19 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#20 D:\work\hotel\all\itinerary-api\runtime\windows\start_webman.php(33): Workerman\Worker::runAll()
#21 {main}
2025-08-06 14:41:12 pid:1 ParseError: syntax error, unexpected token "}" in D:\work\hotel\all\itinerary-api\app\api\controller\User.php:84
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\composer\ClassLoader.php(427): Composer\Autoload\{closure}('D:\\work\\hotel\\a...')
#1 [internal function]: Composer\Autoload\ClassLoader->loadClass('app\\api\\control...')
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(452): class_exists('app\\api\\control...')
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(472): Webman\Route::convertToCallable('/register', Array)
#4 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(132): Webman\Route::addRoute('POST', '/register', Array)
#5 D:\work\hotel\all\itinerary-api\config\route.php(37): Webman\Route::post('/register', Array)
#6 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#7 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/api/user', Object(Closure))
#8 D:\work\hotel\all\itinerary-api\config\route.php(35): Webman\Route::group('/api/user', Object(Closure))
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#10 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#11 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#12 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#14 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#15 [internal function]: Workerman\Worker->Workerman\{closure}()
#16 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#17 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#18 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#19 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#20 D:\work\hotel\all\itinerary-api\runtime\windows\start_plugin.webman.redis-queue.consumer.php(33): Workerman\Worker::runAll()
#21 {main}
2025-08-06 14:41:15 pid:1 ParseError: syntax error, unexpected token "}" in D:\work\hotel\all\itinerary-api\app\api\controller\User.php:84
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\composer\ClassLoader.php(427): Composer\Autoload\{closure}('D:\\work\\hotel\\a...')
#1 [internal function]: Composer\Autoload\ClassLoader->loadClass('app\\api\\control...')
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(452): class_exists('app\\api\\control...')
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(472): Webman\Route::convertToCallable('/register', Array)
#4 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(132): Webman\Route::addRoute('POST', '/register', Array)
#5 D:\work\hotel\all\itinerary-api\config\route.php(37): Webman\Route::post('/register', Array)
#6 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#7 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/api/user', Object(Closure))
#8 D:\work\hotel\all\itinerary-api\config\route.php(35): Webman\Route::group('/api/user', Object(Closure))
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#10 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#11 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#12 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#14 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#15 [internal function]: Workerman\Worker->Workerman\{closure}()
#16 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#17 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#18 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#19 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#20 D:\work\hotel\all\itinerary-api\runtime\windows\start_webman.php(33): Workerman\Worker::runAll()
#21 {main}
2025-08-06 14:41:15 pid:1 ParseError: syntax error, unexpected token "}" in D:\work\hotel\all\itinerary-api\app\api\controller\User.php:84
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\composer\ClassLoader.php(427): Composer\Autoload\{closure}('D:\\work\\hotel\\a...')
#1 [internal function]: Composer\Autoload\ClassLoader->loadClass('app\\api\\control...')
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(452): class_exists('app\\api\\control...')
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(472): Webman\Route::convertToCallable('/register', Array)
#4 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(132): Webman\Route::addRoute('POST', '/register', Array)
#5 D:\work\hotel\all\itinerary-api\config\route.php(37): Webman\Route::post('/register', Array)
#6 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#7 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/api/user', Object(Closure))
#8 D:\work\hotel\all\itinerary-api\config\route.php(35): Webman\Route::group('/api/user', Object(Closure))
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#10 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#11 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#12 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#14 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#15 [internal function]: Workerman\Worker->Workerman\{closure}()
#16 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#17 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#18 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#19 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#20 D:\work\hotel\all\itinerary-api\runtime\windows\start_plugin.webman.redis-queue.consumer.php(33): Workerman\Worker::runAll()
#21 {main}
2025-08-06 14:41:15 pid:1 ParseError: syntax error, unexpected token "}" in D:\work\hotel\all\itinerary-api\app\api\controller\User.php:84
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\composer\ClassLoader.php(427): Composer\Autoload\{closure}('D:\\work\\hotel\\a...')
#1 [internal function]: Composer\Autoload\ClassLoader->loadClass('app\\api\\control...')
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(452): class_exists('app\\api\\control...')
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(472): Webman\Route::convertToCallable('/register', Array)
#4 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(132): Webman\Route::addRoute('POST', '/register', Array)
#5 D:\work\hotel\all\itinerary-api\config\route.php(37): Webman\Route::post('/register', Array)
#6 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#7 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/api/user', Object(Closure))
#8 D:\work\hotel\all\itinerary-api\config\route.php(35): Webman\Route::group('/api/user', Object(Closure))
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#10 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#11 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#12 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#14 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#15 [internal function]: Workerman\Worker->Workerman\{closure}()
#16 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#17 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#18 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#19 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#20 D:\work\hotel\all\itinerary-api\runtime\windows\start_monitor.php(33): Workerman\Worker::runAll()
#21 {main}
2025-08-06 14:41:18 pid:1 ParseError: syntax error, unexpected token "}" in D:\work\hotel\all\itinerary-api\app\api\controller\User.php:84
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\composer\ClassLoader.php(427): Composer\Autoload\{closure}('D:\\work\\hotel\\a...')
#1 [internal function]: Composer\Autoload\ClassLoader->loadClass('app\\api\\control...')
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(452): class_exists('app\\api\\control...')
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(472): Webman\Route::convertToCallable('/register', Array)
#4 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(132): Webman\Route::addRoute('POST', '/register', Array)
#5 D:\work\hotel\all\itinerary-api\config\route.php(37): Webman\Route::post('/register', Array)
#6 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#7 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/api/user', Object(Closure))
#8 D:\work\hotel\all\itinerary-api\config\route.php(35): Webman\Route::group('/api/user', Object(Closure))
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#10 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#11 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#12 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#14 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#15 [internal function]: Workerman\Worker->Workerman\{closure}()
#16 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#17 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#18 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#19 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#20 D:\work\hotel\all\itinerary-api\runtime\windows\start_monitor.php(33): Workerman\Worker::runAll()
#21 {main}
2025-08-06 14:41:18 pid:1 ParseError: syntax error, unexpected token "}" in D:\work\hotel\all\itinerary-api\app\api\controller\User.php:84
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\composer\ClassLoader.php(427): Composer\Autoload\{closure}('D:\\work\\hotel\\a...')
#1 [internal function]: Composer\Autoload\ClassLoader->loadClass('app\\api\\control...')
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(452): class_exists('app\\api\\control...')
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(472): Webman\Route::convertToCallable('/register', Array)
#4 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(132): Webman\Route::addRoute('POST', '/register', Array)
#5 D:\work\hotel\all\itinerary-api\config\route.php(37): Webman\Route::post('/register', Array)
#6 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#7 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/api/user', Object(Closure))
#8 D:\work\hotel\all\itinerary-api\config\route.php(35): Webman\Route::group('/api/user', Object(Closure))
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#10 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#11 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#12 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#14 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#15 [internal function]: Workerman\Worker->Workerman\{closure}()
#16 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#17 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#18 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#19 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#20 D:\work\hotel\all\itinerary-api\runtime\windows\start_webman.php(33): Workerman\Worker::runAll()
#21 {main}
2025-08-06 14:41:18 pid:1 ParseError: syntax error, unexpected token "}" in D:\work\hotel\all\itinerary-api\app\api\controller\User.php:84
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\composer\ClassLoader.php(427): Composer\Autoload\{closure}('D:\\work\\hotel\\a...')
#1 [internal function]: Composer\Autoload\ClassLoader->loadClass('app\\api\\control...')
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(452): class_exists('app\\api\\control...')
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(472): Webman\Route::convertToCallable('/register', Array)
#4 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(132): Webman\Route::addRoute('POST', '/register', Array)
#5 D:\work\hotel\all\itinerary-api\config\route.php(37): Webman\Route::post('/register', Array)
#6 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#7 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/api/user', Object(Closure))
#8 D:\work\hotel\all\itinerary-api\config\route.php(35): Webman\Route::group('/api/user', Object(Closure))
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#10 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#11 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#12 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#14 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#15 [internal function]: Workerman\Worker->Workerman\{closure}()
#16 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#17 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#18 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#19 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#20 D:\work\hotel\all\itinerary-api\runtime\windows\start_plugin.webman.redis-queue.consumer.php(33): Workerman\Worker::runAll()
#21 {main}
2025-08-06 14:41:22 pid:1 ParseError: syntax error, unexpected token "}" in D:\work\hotel\all\itinerary-api\app\api\controller\User.php:84
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\composer\ClassLoader.php(427): Composer\Autoload\{closure}('D:\\work\\hotel\\a...')
#1 [internal function]: Composer\Autoload\ClassLoader->loadClass('app\\api\\control...')
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(452): class_exists('app\\api\\control...')
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(472): Webman\Route::convertToCallable('/register', Array)
#4 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(132): Webman\Route::addRoute('POST', '/register', Array)
#5 D:\work\hotel\all\itinerary-api\config\route.php(37): Webman\Route::post('/register', Array)
#6 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#7 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/api/user', Object(Closure))
#8 D:\work\hotel\all\itinerary-api\config\route.php(35): Webman\Route::group('/api/user', Object(Closure))
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#10 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#11 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#12 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#14 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#15 [internal function]: Workerman\Worker->Workerman\{closure}()
#16 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#17 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#18 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#19 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#20 D:\work\hotel\all\itinerary-api\runtime\windows\start_plugin.webman.redis-queue.consumer.php(33): Workerman\Worker::runAll()
#21 {main}
2025-08-06 14:41:22 pid:1 ParseError: syntax error, unexpected token "}" in D:\work\hotel\all\itinerary-api\app\api\controller\User.php:84
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\composer\ClassLoader.php(427): Composer\Autoload\{closure}('D:\\work\\hotel\\a...')
#1 [internal function]: Composer\Autoload\ClassLoader->loadClass('app\\api\\control...')
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(452): class_exists('app\\api\\control...')
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(472): Webman\Route::convertToCallable('/register', Array)
#4 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(132): Webman\Route::addRoute('POST', '/register', Array)
#5 D:\work\hotel\all\itinerary-api\config\route.php(37): Webman\Route::post('/register', Array)
#6 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#7 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/api/user', Object(Closure))
#8 D:\work\hotel\all\itinerary-api\config\route.php(35): Webman\Route::group('/api/user', Object(Closure))
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#10 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#11 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#12 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#14 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#15 [internal function]: Workerman\Worker->Workerman\{closure}()
#16 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#17 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#18 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#19 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#20 D:\work\hotel\all\itinerary-api\runtime\windows\start_webman.php(33): Workerman\Worker::runAll()
#21 {main}
2025-08-06 14:41:22 pid:1 ParseError: syntax error, unexpected token "}" in D:\work\hotel\all\itinerary-api\app\api\controller\User.php:84
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\composer\ClassLoader.php(427): Composer\Autoload\{closure}('D:\\work\\hotel\\a...')
#1 [internal function]: Composer\Autoload\ClassLoader->loadClass('app\\api\\control...')
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(452): class_exists('app\\api\\control...')
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(472): Webman\Route::convertToCallable('/register', Array)
#4 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(132): Webman\Route::addRoute('POST', '/register', Array)
#5 D:\work\hotel\all\itinerary-api\config\route.php(37): Webman\Route::post('/register', Array)
#6 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#7 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/api/user', Object(Closure))
#8 D:\work\hotel\all\itinerary-api\config\route.php(35): Webman\Route::group('/api/user', Object(Closure))
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#10 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#11 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#12 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#14 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#15 [internal function]: Workerman\Worker->Workerman\{closure}()
#16 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#17 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#18 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#19 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#20 D:\work\hotel\all\itinerary-api\runtime\windows\start_monitor.php(33): Workerman\Worker::runAll()
#21 {main}
2025-08-06 14:41:25 pid:1 ParseError: syntax error, unexpected token "}" in D:\work\hotel\all\itinerary-api\app\api\controller\User.php:84
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\composer\ClassLoader.php(427): Composer\Autoload\{closure}('D:\\work\\hotel\\a...')
#1 [internal function]: Composer\Autoload\ClassLoader->loadClass('app\\api\\control...')
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(452): class_exists('app\\api\\control...')
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(472): Webman\Route::convertToCallable('/register', Array)
#4 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(132): Webman\Route::addRoute('POST', '/register', Array)
#5 D:\work\hotel\all\itinerary-api\config\route.php(37): Webman\Route::post('/register', Array)
#6 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#7 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/api/user', Object(Closure))
#8 D:\work\hotel\all\itinerary-api\config\route.php(35): Webman\Route::group('/api/user', Object(Closure))
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#10 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#11 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#12 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#14 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#15 [internal function]: Workerman\Worker->Workerman\{closure}()
#16 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#17 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#18 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#19 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#20 D:\work\hotel\all\itinerary-api\runtime\windows\start_monitor.php(33): Workerman\Worker::runAll()
#21 {main}
2025-08-06 14:41:25 pid:1 ParseError: syntax error, unexpected token "}" in D:\work\hotel\all\itinerary-api\app\api\controller\User.php:84
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\composer\ClassLoader.php(427): Composer\Autoload\{closure}('D:\\work\\hotel\\a...')
#1 [internal function]: Composer\Autoload\ClassLoader->loadClass('app\\api\\control...')
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(452): class_exists('app\\api\\control...')
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(472): Webman\Route::convertToCallable('/register', Array)
#4 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(132): Webman\Route::addRoute('POST', '/register', Array)
#5 D:\work\hotel\all\itinerary-api\config\route.php(37): Webman\Route::post('/register', Array)
#6 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#7 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/api/user', Object(Closure))
#8 D:\work\hotel\all\itinerary-api\config\route.php(35): Webman\Route::group('/api/user', Object(Closure))
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#10 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#11 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#12 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#14 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#15 [internal function]: Workerman\Worker->Workerman\{closure}()
#16 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#17 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#18 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#19 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#20 D:\work\hotel\all\itinerary-api\runtime\windows\start_plugin.webman.redis-queue.consumer.php(33): Workerman\Worker::runAll()
#21 {main}
2025-08-06 14:41:25 pid:1 ParseError: syntax error, unexpected token "}" in D:\work\hotel\all\itinerary-api\app\api\controller\User.php:84
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\composer\ClassLoader.php(427): Composer\Autoload\{closure}('D:\\work\\hotel\\a...')
#1 [internal function]: Composer\Autoload\ClassLoader->loadClass('app\\api\\control...')
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(452): class_exists('app\\api\\control...')
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(472): Webman\Route::convertToCallable('/register', Array)
#4 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(132): Webman\Route::addRoute('POST', '/register', Array)
#5 D:\work\hotel\all\itinerary-api\config\route.php(37): Webman\Route::post('/register', Array)
#6 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#7 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/api/user', Object(Closure))
#8 D:\work\hotel\all\itinerary-api\config\route.php(35): Webman\Route::group('/api/user', Object(Closure))
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#10 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#11 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#12 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#14 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#15 [internal function]: Workerman\Worker->Workerman\{closure}()
#16 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#17 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#18 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#19 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#20 D:\work\hotel\all\itinerary-api\runtime\windows\start_webman.php(33): Workerman\Worker::runAll()
#21 {main}
2025-08-06 14:41:27 pid:1 ParseError: syntax error, unexpected token "}" in D:\work\hotel\all\itinerary-api\app\api\controller\User.php:84
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\composer\ClassLoader.php(427): Composer\Autoload\{closure}('D:\\work\\hotel\\a...')
#1 [internal function]: Composer\Autoload\ClassLoader->loadClass('app\\api\\control...')
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(452): class_exists('app\\api\\control...')
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(472): Webman\Route::convertToCallable('/register', Array)
#4 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(132): Webman\Route::addRoute('POST', '/register', Array)
#5 D:\work\hotel\all\itinerary-api\config\route.php(37): Webman\Route::post('/register', Array)
#6 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#7 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/api/user', Object(Closure))
#8 D:\work\hotel\all\itinerary-api\config\route.php(35): Webman\Route::group('/api/user', Object(Closure))
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#10 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#11 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#12 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#14 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#15 [internal function]: Workerman\Worker->Workerman\{closure}()
#16 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#17 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#18 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#19 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#20 D:\work\hotel\all\itinerary-api\runtime\windows\start_webman.php(33): Workerman\Worker::runAll()
#21 {main}
2025-08-06 14:41:27 pid:1 ParseError: syntax error, unexpected token "}" in D:\work\hotel\all\itinerary-api\app\api\controller\User.php:84
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\composer\ClassLoader.php(427): Composer\Autoload\{closure}('D:\\work\\hotel\\a...')
#1 [internal function]: Composer\Autoload\ClassLoader->loadClass('app\\api\\control...')
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(452): class_exists('app\\api\\control...')
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(472): Webman\Route::convertToCallable('/register', Array)
#4 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(132): Webman\Route::addRoute('POST', '/register', Array)
#5 D:\work\hotel\all\itinerary-api\config\route.php(37): Webman\Route::post('/register', Array)
#6 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#7 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/api/user', Object(Closure))
#8 D:\work\hotel\all\itinerary-api\config\route.php(35): Webman\Route::group('/api/user', Object(Closure))
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#10 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#11 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#12 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#14 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#15 [internal function]: Workerman\Worker->Workerman\{closure}()
#16 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#17 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#18 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#19 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#20 D:\work\hotel\all\itinerary-api\runtime\windows\start_plugin.webman.redis-queue.consumer.php(33): Workerman\Worker::runAll()
#21 {main}
2025-08-06 14:41:27 pid:1 ParseError: syntax error, unexpected token "}" in D:\work\hotel\all\itinerary-api\app\api\controller\User.php:84
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\composer\ClassLoader.php(427): Composer\Autoload\{closure}('D:\\work\\hotel\\a...')
#1 [internal function]: Composer\Autoload\ClassLoader->loadClass('app\\api\\control...')
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(452): class_exists('app\\api\\control...')
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(472): Webman\Route::convertToCallable('/register', Array)
#4 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(132): Webman\Route::addRoute('POST', '/register', Array)
#5 D:\work\hotel\all\itinerary-api\config\route.php(37): Webman\Route::post('/register', Array)
#6 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#7 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/api/user', Object(Closure))
#8 D:\work\hotel\all\itinerary-api\config\route.php(35): Webman\Route::group('/api/user', Object(Closure))
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#10 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#11 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#12 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#14 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#15 [internal function]: Workerman\Worker->Workerman\{closure}()
#16 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#17 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#18 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#19 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#20 D:\work\hotel\all\itinerary-api\runtime\windows\start_monitor.php(33): Workerman\Worker::runAll()
#21 {main}
2025-08-06 14:41:30 pid:1 ParseError: syntax error, unexpected token "}" in D:\work\hotel\all\itinerary-api\app\api\controller\User.php:84
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\composer\ClassLoader.php(427): Composer\Autoload\{closure}('D:\\work\\hotel\\a...')
#1 [internal function]: Composer\Autoload\ClassLoader->loadClass('app\\api\\control...')
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(452): class_exists('app\\api\\control...')
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(472): Webman\Route::convertToCallable('/register', Array)
#4 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(132): Webman\Route::addRoute('POST', '/register', Array)
#5 D:\work\hotel\all\itinerary-api\config\route.php(37): Webman\Route::post('/register', Array)
#6 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#7 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/api/user', Object(Closure))
#8 D:\work\hotel\all\itinerary-api\config\route.php(35): Webman\Route::group('/api/user', Object(Closure))
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#10 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#11 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#12 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#14 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#15 [internal function]: Workerman\Worker->Workerman\{closure}()
#16 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#17 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#18 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#19 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#20 D:\work\hotel\all\itinerary-api\runtime\windows\start_monitor.php(33): Workerman\Worker::runAll()
#21 {main}
2025-08-06 14:41:30 pid:1 ParseError: syntax error, unexpected token "}" in D:\work\hotel\all\itinerary-api\app\api\controller\User.php:84
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\composer\ClassLoader.php(427): Composer\Autoload\{closure}('D:\\work\\hotel\\a...')
#1 [internal function]: Composer\Autoload\ClassLoader->loadClass('app\\api\\control...')
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(452): class_exists('app\\api\\control...')
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(472): Webman\Route::convertToCallable('/register', Array)
#4 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(132): Webman\Route::addRoute('POST', '/register', Array)
#5 D:\work\hotel\all\itinerary-api\config\route.php(37): Webman\Route::post('/register', Array)
#6 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#7 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/api/user', Object(Closure))
#8 D:\work\hotel\all\itinerary-api\config\route.php(35): Webman\Route::group('/api/user', Object(Closure))
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#10 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#11 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#12 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#14 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#15 [internal function]: Workerman\Worker->Workerman\{closure}()
#16 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#17 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#18 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#19 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#20 D:\work\hotel\all\itinerary-api\runtime\windows\start_plugin.webman.redis-queue.consumer.php(33): Workerman\Worker::runAll()
#21 {main}
2025-08-06 14:41:30 pid:1 ParseError: syntax error, unexpected token "}" in D:\work\hotel\all\itinerary-api\app\api\controller\User.php:84
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\composer\ClassLoader.php(427): Composer\Autoload\{closure}('D:\\work\\hotel\\a...')
#1 [internal function]: Composer\Autoload\ClassLoader->loadClass('app\\api\\control...')
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(452): class_exists('app\\api\\control...')
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(472): Webman\Route::convertToCallable('/register', Array)
#4 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(132): Webman\Route::addRoute('POST', '/register', Array)
#5 D:\work\hotel\all\itinerary-api\config\route.php(37): Webman\Route::post('/register', Array)
#6 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#7 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/api/user', Object(Closure))
#8 D:\work\hotel\all\itinerary-api\config\route.php(35): Webman\Route::group('/api/user', Object(Closure))
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#10 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#11 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#12 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#14 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#15 [internal function]: Workerman\Worker->Workerman\{closure}()
#16 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#17 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#18 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#19 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#20 D:\work\hotel\all\itinerary-api\runtime\windows\start_webman.php(33): Workerman\Worker::runAll()
#21 {main}
2025-08-06 14:41:33 pid:1 ParseError: syntax error, unexpected token "}" in D:\work\hotel\all\itinerary-api\app\api\controller\User.php:84
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\composer\ClassLoader.php(427): Composer\Autoload\{closure}('D:\\work\\hotel\\a...')
#1 [internal function]: Composer\Autoload\ClassLoader->loadClass('app\\api\\control...')
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(452): class_exists('app\\api\\control...')
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(472): Webman\Route::convertToCallable('/register', Array)
#4 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(132): Webman\Route::addRoute('POST', '/register', Array)
#5 D:\work\hotel\all\itinerary-api\config\route.php(37): Webman\Route::post('/register', Array)
#6 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#7 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/api/user', Object(Closure))
#8 D:\work\hotel\all\itinerary-api\config\route.php(35): Webman\Route::group('/api/user', Object(Closure))
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#10 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#11 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#12 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#14 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#15 [internal function]: Workerman\Worker->Workerman\{closure}()
#16 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#17 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#18 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#19 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#20 D:\work\hotel\all\itinerary-api\runtime\windows\start_monitor.php(33): Workerman\Worker::runAll()
#21 {main}
2025-08-06 14:41:33 pid:1 ParseError: syntax error, unexpected token "}" in D:\work\hotel\all\itinerary-api\app\api\controller\User.php:84
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\composer\ClassLoader.php(427): Composer\Autoload\{closure}('D:\\work\\hotel\\a...')
#1 [internal function]: Composer\Autoload\ClassLoader->loadClass('app\\api\\control...')
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(452): class_exists('app\\api\\control...')
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(472): Webman\Route::convertToCallable('/register', Array)
#4 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(132): Webman\Route::addRoute('POST', '/register', Array)
#5 D:\work\hotel\all\itinerary-api\config\route.php(37): Webman\Route::post('/register', Array)
#6 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#7 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/api/user', Object(Closure))
#8 D:\work\hotel\all\itinerary-api\config\route.php(35): Webman\Route::group('/api/user', Object(Closure))
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#10 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#11 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#12 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#14 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#15 [internal function]: Workerman\Worker->Workerman\{closure}()
#16 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#17 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#18 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#19 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#20 D:\work\hotel\all\itinerary-api\runtime\windows\start_webman.php(33): Workerman\Worker::runAll()
#21 {main}
2025-08-06 14:41:33 pid:1 ParseError: syntax error, unexpected token "}" in D:\work\hotel\all\itinerary-api\app\api\controller\User.php:84
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\composer\ClassLoader.php(427): Composer\Autoload\{closure}('D:\\work\\hotel\\a...')
#1 [internal function]: Composer\Autoload\ClassLoader->loadClass('app\\api\\control...')
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(452): class_exists('app\\api\\control...')
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(472): Webman\Route::convertToCallable('/register', Array)
#4 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(132): Webman\Route::addRoute('POST', '/register', Array)
#5 D:\work\hotel\all\itinerary-api\config\route.php(37): Webman\Route::post('/register', Array)
#6 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#7 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/api/user', Object(Closure))
#8 D:\work\hotel\all\itinerary-api\config\route.php(35): Webman\Route::group('/api/user', Object(Closure))
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#10 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#11 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#12 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#14 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#15 [internal function]: Workerman\Worker->Workerman\{closure}()
#16 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#17 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#18 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#19 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#20 D:\work\hotel\all\itinerary-api\runtime\windows\start_plugin.webman.redis-queue.consumer.php(33): Workerman\Worker::runAll()
#21 {main}
2025-08-06 14:41:36 pid:1 ParseError: syntax error, unexpected token "}" in D:\work\hotel\all\itinerary-api\app\api\controller\User.php:84
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\composer\ClassLoader.php(427): Composer\Autoload\{closure}('D:\\work\\hotel\\a...')
#1 [internal function]: Composer\Autoload\ClassLoader->loadClass('app\\api\\control...')
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(452): class_exists('app\\api\\control...')
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(472): Webman\Route::convertToCallable('/register', Array)
#4 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(132): Webman\Route::addRoute('POST', '/register', Array)
#5 D:\work\hotel\all\itinerary-api\config\route.php(37): Webman\Route::post('/register', Array)
#6 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#7 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/api/user', Object(Closure))
#8 D:\work\hotel\all\itinerary-api\config\route.php(35): Webman\Route::group('/api/user', Object(Closure))
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#10 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#11 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#12 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#14 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#15 [internal function]: Workerman\Worker->Workerman\{closure}()
#16 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#17 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#18 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#19 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#20 D:\work\hotel\all\itinerary-api\runtime\windows\start_webman.php(33): Workerman\Worker::runAll()
#21 {main}
2025-08-06 14:41:36 pid:1 ParseError: syntax error, unexpected token "}" in D:\work\hotel\all\itinerary-api\app\api\controller\User.php:84
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\composer\ClassLoader.php(427): Composer\Autoload\{closure}('D:\\work\\hotel\\a...')
#1 [internal function]: Composer\Autoload\ClassLoader->loadClass('app\\api\\control...')
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(452): class_exists('app\\api\\control...')
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(472): Webman\Route::convertToCallable('/register', Array)
#4 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(132): Webman\Route::addRoute('POST', '/register', Array)
#5 D:\work\hotel\all\itinerary-api\config\route.php(37): Webman\Route::post('/register', Array)
#6 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#7 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/api/user', Object(Closure))
#8 D:\work\hotel\all\itinerary-api\config\route.php(35): Webman\Route::group('/api/user', Object(Closure))
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#10 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#11 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#12 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#14 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#15 [internal function]: Workerman\Worker->Workerman\{closure}()
#16 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#17 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#18 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#19 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#20 D:\work\hotel\all\itinerary-api\runtime\windows\start_plugin.webman.redis-queue.consumer.php(33): Workerman\Worker::runAll()
#21 {main}
2025-08-06 14:41:36 pid:1 ParseError: syntax error, unexpected token "}" in D:\work\hotel\all\itinerary-api\app\api\controller\User.php:84
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\composer\ClassLoader.php(427): Composer\Autoload\{closure}('D:\\work\\hotel\\a...')
#1 [internal function]: Composer\Autoload\ClassLoader->loadClass('app\\api\\control...')
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(452): class_exists('app\\api\\control...')
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(472): Webman\Route::convertToCallable('/register', Array)
#4 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(132): Webman\Route::addRoute('POST', '/register', Array)
#5 D:\work\hotel\all\itinerary-api\config\route.php(37): Webman\Route::post('/register', Array)
#6 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#7 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/api/user', Object(Closure))
#8 D:\work\hotel\all\itinerary-api\config\route.php(35): Webman\Route::group('/api/user', Object(Closure))
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#10 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#11 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#12 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#14 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#15 [internal function]: Workerman\Worker->Workerman\{closure}()
#16 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#17 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#18 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#19 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#20 D:\work\hotel\all\itinerary-api\runtime\windows\start_monitor.php(33): Workerman\Worker::runAll()
#21 {main}
2025-08-06 14:41:39 pid:1 ParseError: syntax error, unexpected token "}" in D:\work\hotel\all\itinerary-api\app\api\controller\User.php:84
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\composer\ClassLoader.php(427): Composer\Autoload\{closure}('D:\\work\\hotel\\a...')
#1 [internal function]: Composer\Autoload\ClassLoader->loadClass('app\\api\\control...')
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(452): class_exists('app\\api\\control...')
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(472): Webman\Route::convertToCallable('/register', Array)
#4 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(132): Webman\Route::addRoute('POST', '/register', Array)
#5 D:\work\hotel\all\itinerary-api\config\route.php(37): Webman\Route::post('/register', Array)
#6 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#7 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/api/user', Object(Closure))
#8 D:\work\hotel\all\itinerary-api\config\route.php(35): Webman\Route::group('/api/user', Object(Closure))
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#10 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#11 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#12 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#14 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#15 [internal function]: Workerman\Worker->Workerman\{closure}()
#16 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#17 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#18 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#19 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#20 D:\work\hotel\all\itinerary-api\runtime\windows\start_plugin.webman.redis-queue.consumer.php(33): Workerman\Worker::runAll()
#21 {main}
2025-08-06 14:41:39 pid:1 ParseError: syntax error, unexpected token "}" in D:\work\hotel\all\itinerary-api\app\api\controller\User.php:84
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\composer\ClassLoader.php(427): Composer\Autoload\{closure}('D:\\work\\hotel\\a...')
#1 [internal function]: Composer\Autoload\ClassLoader->loadClass('app\\api\\control...')
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(452): class_exists('app\\api\\control...')
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(472): Webman\Route::convertToCallable('/register', Array)
#4 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(132): Webman\Route::addRoute('POST', '/register', Array)
#5 D:\work\hotel\all\itinerary-api\config\route.php(37): Webman\Route::post('/register', Array)
#6 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#7 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/api/user', Object(Closure))
#8 D:\work\hotel\all\itinerary-api\config\route.php(35): Webman\Route::group('/api/user', Object(Closure))
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#10 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#11 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#12 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#14 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#15 [internal function]: Workerman\Worker->Workerman\{closure}()
#16 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#17 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#18 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#19 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#20 D:\work\hotel\all\itinerary-api\runtime\windows\start_monitor.php(33): Workerman\Worker::runAll()
#21 {main}
2025-08-06 14:41:39 pid:1 ParseError: syntax error, unexpected token "}" in D:\work\hotel\all\itinerary-api\app\api\controller\User.php:84
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\composer\ClassLoader.php(427): Composer\Autoload\{closure}('D:\\work\\hotel\\a...')
#1 [internal function]: Composer\Autoload\ClassLoader->loadClass('app\\api\\control...')
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(452): class_exists('app\\api\\control...')
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(472): Webman\Route::convertToCallable('/register', Array)
#4 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(132): Webman\Route::addRoute('POST', '/register', Array)
#5 D:\work\hotel\all\itinerary-api\config\route.php(37): Webman\Route::post('/register', Array)
#6 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#7 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/api/user', Object(Closure))
#8 D:\work\hotel\all\itinerary-api\config\route.php(35): Webman\Route::group('/api/user', Object(Closure))
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#10 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#11 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#12 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#14 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#15 [internal function]: Workerman\Worker->Workerman\{closure}()
#16 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#17 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#18 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#19 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#20 D:\work\hotel\all\itinerary-api\runtime\windows\start_webman.php(33): Workerman\Worker::runAll()
#21 {main}
2025-08-06 14:41:42 pid:1 ParseError: syntax error, unexpected token "}" in D:\work\hotel\all\itinerary-api\app\api\controller\User.php:84
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\composer\ClassLoader.php(427): Composer\Autoload\{closure}('D:\\work\\hotel\\a...')
#1 [internal function]: Composer\Autoload\ClassLoader->loadClass('app\\api\\control...')
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(452): class_exists('app\\api\\control...')
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(472): Webman\Route::convertToCallable('/register', Array)
#4 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(132): Webman\Route::addRoute('POST', '/register', Array)
#5 D:\work\hotel\all\itinerary-api\config\route.php(37): Webman\Route::post('/register', Array)
#6 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#7 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/api/user', Object(Closure))
#8 D:\work\hotel\all\itinerary-api\config\route.php(35): Webman\Route::group('/api/user', Object(Closure))
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#10 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#11 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#12 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#14 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#15 [internal function]: Workerman\Worker->Workerman\{closure}()
#16 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#17 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#18 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#19 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#20 D:\work\hotel\all\itinerary-api\runtime\windows\start_plugin.webman.redis-queue.consumer.php(33): Workerman\Worker::runAll()
#21 {main}
2025-08-06 14:41:42 pid:1 ParseError: syntax error, unexpected token "}" in D:\work\hotel\all\itinerary-api\app\api\controller\User.php:84
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\composer\ClassLoader.php(427): Composer\Autoload\{closure}('D:\\work\\hotel\\a...')
#1 [internal function]: Composer\Autoload\ClassLoader->loadClass('app\\api\\control...')
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(452): class_exists('app\\api\\control...')
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(472): Webman\Route::convertToCallable('/register', Array)
#4 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(132): Webman\Route::addRoute('POST', '/register', Array)
#5 D:\work\hotel\all\itinerary-api\config\route.php(37): Webman\Route::post('/register', Array)
#6 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#7 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/api/user', Object(Closure))
#8 D:\work\hotel\all\itinerary-api\config\route.php(35): Webman\Route::group('/api/user', Object(Closure))
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#10 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#11 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#12 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#14 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#15 [internal function]: Workerman\Worker->Workerman\{closure}()
#16 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#17 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#18 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#19 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#20 D:\work\hotel\all\itinerary-api\runtime\windows\start_webman.php(33): Workerman\Worker::runAll()
#21 {main}
2025-08-06 14:41:42 pid:1 ParseError: syntax error, unexpected token "}" in D:\work\hotel\all\itinerary-api\app\api\controller\User.php:84
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\composer\ClassLoader.php(427): Composer\Autoload\{closure}('D:\\work\\hotel\\a...')
#1 [internal function]: Composer\Autoload\ClassLoader->loadClass('app\\api\\control...')
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(452): class_exists('app\\api\\control...')
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(472): Webman\Route::convertToCallable('/register', Array)
#4 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(132): Webman\Route::addRoute('POST', '/register', Array)
#5 D:\work\hotel\all\itinerary-api\config\route.php(37): Webman\Route::post('/register', Array)
#6 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#7 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/api/user', Object(Closure))
#8 D:\work\hotel\all\itinerary-api\config\route.php(35): Webman\Route::group('/api/user', Object(Closure))
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#10 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#11 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#12 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#14 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#15 [internal function]: Workerman\Worker->Workerman\{closure}()
#16 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#17 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#18 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#19 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#20 D:\work\hotel\all\itinerary-api\runtime\windows\start_monitor.php(33): Workerman\Worker::runAll()
#21 {main}
2025-08-06 14:41:45 pid:1 ParseError: syntax error, unexpected token "}" in D:\work\hotel\all\itinerary-api\app\api\controller\User.php:84
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\composer\ClassLoader.php(427): Composer\Autoload\{closure}('D:\\work\\hotel\\a...')
#1 [internal function]: Composer\Autoload\ClassLoader->loadClass('app\\api\\control...')
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(452): class_exists('app\\api\\control...')
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(472): Webman\Route::convertToCallable('/register', Array)
#4 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(132): Webman\Route::addRoute('POST', '/register', Array)
#5 D:\work\hotel\all\itinerary-api\config\route.php(37): Webman\Route::post('/register', Array)
#6 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#7 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/api/user', Object(Closure))
#8 D:\work\hotel\all\itinerary-api\config\route.php(35): Webman\Route::group('/api/user', Object(Closure))
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#10 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#11 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#12 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#14 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#15 [internal function]: Workerman\Worker->Workerman\{closure}()
#16 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#17 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#18 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#19 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#20 D:\work\hotel\all\itinerary-api\runtime\windows\start_monitor.php(33): Workerman\Worker::runAll()
#21 {main}
2025-08-06 14:41:45 pid:1 ParseError: syntax error, unexpected token "}" in D:\work\hotel\all\itinerary-api\app\api\controller\User.php:84
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\composer\ClassLoader.php(427): Composer\Autoload\{closure}('D:\\work\\hotel\\a...')
#1 [internal function]: Composer\Autoload\ClassLoader->loadClass('app\\api\\control...')
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(452): class_exists('app\\api\\control...')
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(472): Webman\Route::convertToCallable('/register', Array)
#4 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(132): Webman\Route::addRoute('POST', '/register', Array)
#5 D:\work\hotel\all\itinerary-api\config\route.php(37): Webman\Route::post('/register', Array)
#6 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#7 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/api/user', Object(Closure))
#8 D:\work\hotel\all\itinerary-api\config\route.php(35): Webman\Route::group('/api/user', Object(Closure))
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#10 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#11 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#12 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#14 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#15 [internal function]: Workerman\Worker->Workerman\{closure}()
#16 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#17 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#18 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#19 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#20 D:\work\hotel\all\itinerary-api\runtime\windows\start_plugin.webman.redis-queue.consumer.php(33): Workerman\Worker::runAll()
#21 {main}
2025-08-06 14:41:45 pid:1 ParseError: syntax error, unexpected token "}" in D:\work\hotel\all\itinerary-api\app\api\controller\User.php:84
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\composer\ClassLoader.php(427): Composer\Autoload\{closure}('D:\\work\\hotel\\a...')
#1 [internal function]: Composer\Autoload\ClassLoader->loadClass('app\\api\\control...')
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(452): class_exists('app\\api\\control...')
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(472): Webman\Route::convertToCallable('/register', Array)
#4 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(132): Webman\Route::addRoute('POST', '/register', Array)
#5 D:\work\hotel\all\itinerary-api\config\route.php(37): Webman\Route::post('/register', Array)
#6 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#7 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/api/user', Object(Closure))
#8 D:\work\hotel\all\itinerary-api\config\route.php(35): Webman\Route::group('/api/user', Object(Closure))
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#10 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#11 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#12 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#14 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#15 [internal function]: Workerman\Worker->Workerman\{closure}()
#16 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#17 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#18 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#19 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#20 D:\work\hotel\all\itinerary-api\runtime\windows\start_webman.php(33): Workerman\Worker::runAll()
#21 {main}
2025-08-06 14:41:48 pid:1 ParseError: syntax error, unexpected token "}" in D:\work\hotel\all\itinerary-api\app\api\controller\User.php:84
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\composer\ClassLoader.php(427): Composer\Autoload\{closure}('D:\\work\\hotel\\a...')
#1 [internal function]: Composer\Autoload\ClassLoader->loadClass('app\\api\\control...')
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(452): class_exists('app\\api\\control...')
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(472): Webman\Route::convertToCallable('/register', Array)
#4 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(132): Webman\Route::addRoute('POST', '/register', Array)
#5 D:\work\hotel\all\itinerary-api\config\route.php(37): Webman\Route::post('/register', Array)
#6 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#7 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/api/user', Object(Closure))
#8 D:\work\hotel\all\itinerary-api\config\route.php(35): Webman\Route::group('/api/user', Object(Closure))
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#10 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#11 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#12 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#14 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#15 [internal function]: Workerman\Worker->Workerman\{closure}()
#16 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#17 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#18 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#19 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#20 D:\work\hotel\all\itinerary-api\runtime\windows\start_plugin.webman.redis-queue.consumer.php(33): Workerman\Worker::runAll()
#21 {main}
2025-08-06 14:41:48 pid:1 ParseError: syntax error, unexpected token "}" in D:\work\hotel\all\itinerary-api\app\api\controller\User.php:84
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\composer\ClassLoader.php(427): Composer\Autoload\{closure}('D:\\work\\hotel\\a...')
#1 [internal function]: Composer\Autoload\ClassLoader->loadClass('app\\api\\control...')
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(452): class_exists('app\\api\\control...')
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(472): Webman\Route::convertToCallable('/register', Array)
#4 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(132): Webman\Route::addRoute('POST', '/register', Array)
#5 D:\work\hotel\all\itinerary-api\config\route.php(37): Webman\Route::post('/register', Array)
#6 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#7 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/api/user', Object(Closure))
#8 D:\work\hotel\all\itinerary-api\config\route.php(35): Webman\Route::group('/api/user', Object(Closure))
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#10 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#11 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#12 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#14 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#15 [internal function]: Workerman\Worker->Workerman\{closure}()
#16 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#17 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#18 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#19 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#20 D:\work\hotel\all\itinerary-api\runtime\windows\start_webman.php(33): Workerman\Worker::runAll()
#21 {main}
2025-08-06 14:41:48 pid:1 ParseError: syntax error, unexpected token "}" in D:\work\hotel\all\itinerary-api\app\api\controller\User.php:84
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\composer\ClassLoader.php(427): Composer\Autoload\{closure}('D:\\work\\hotel\\a...')
#1 [internal function]: Composer\Autoload\ClassLoader->loadClass('app\\api\\control...')
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(452): class_exists('app\\api\\control...')
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(472): Webman\Route::convertToCallable('/register', Array)
#4 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(132): Webman\Route::addRoute('POST', '/register', Array)
#5 D:\work\hotel\all\itinerary-api\config\route.php(37): Webman\Route::post('/register', Array)
#6 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#7 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/api/user', Object(Closure))
#8 D:\work\hotel\all\itinerary-api\config\route.php(35): Webman\Route::group('/api/user', Object(Closure))
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#10 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#11 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#12 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#14 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#15 [internal function]: Workerman\Worker->Workerman\{closure}()
#16 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#17 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#18 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#19 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#20 D:\work\hotel\all\itinerary-api\runtime\windows\start_monitor.php(33): Workerman\Worker::runAll()
#21 {main}
2025-08-06 14:41:52 pid:1 ParseError: syntax error, unexpected token "}" in D:\work\hotel\all\itinerary-api\app\api\controller\User.php:85
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\composer\ClassLoader.php(427): Composer\Autoload\{closure}('D:\\work\\hotel\\a...')
#1 [internal function]: Composer\Autoload\ClassLoader->loadClass('app\\api\\control...')
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(452): class_exists('app\\api\\control...')
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(472): Webman\Route::convertToCallable('/register', Array)
#4 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(132): Webman\Route::addRoute('POST', '/register', Array)
#5 D:\work\hotel\all\itinerary-api\config\route.php(37): Webman\Route::post('/register', Array)
#6 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#7 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/api/user', Object(Closure))
#8 D:\work\hotel\all\itinerary-api\config\route.php(35): Webman\Route::group('/api/user', Object(Closure))
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#10 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#11 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#12 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#14 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#15 [internal function]: Workerman\Worker->Workerman\{closure}()
#16 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#17 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#18 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#19 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#20 D:\work\hotel\all\itinerary-api\runtime\windows\start_webman.php(33): Workerman\Worker::runAll()
#21 {main}
2025-08-06 14:41:52 pid:1 ParseError: syntax error, unexpected token "}" in D:\work\hotel\all\itinerary-api\app\api\controller\User.php:85
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\composer\ClassLoader.php(427): Composer\Autoload\{closure}('D:\\work\\hotel\\a...')
#1 [internal function]: Composer\Autoload\ClassLoader->loadClass('app\\api\\control...')
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(452): class_exists('app\\api\\control...')
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(472): Webman\Route::convertToCallable('/register', Array)
#4 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(132): Webman\Route::addRoute('POST', '/register', Array)
#5 D:\work\hotel\all\itinerary-api\config\route.php(37): Webman\Route::post('/register', Array)
#6 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#7 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/api/user', Object(Closure))
#8 D:\work\hotel\all\itinerary-api\config\route.php(35): Webman\Route::group('/api/user', Object(Closure))
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#10 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#11 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#12 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#14 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#15 [internal function]: Workerman\Worker->Workerman\{closure}()
#16 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#17 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#18 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#19 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#20 D:\work\hotel\all\itinerary-api\runtime\windows\start_plugin.webman.redis-queue.consumer.php(33): Workerman\Worker::runAll()
#21 {main}
2025-08-06 14:41:52 pid:1 ParseError: syntax error, unexpected token "}" in D:\work\hotel\all\itinerary-api\app\api\controller\User.php:85
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\composer\ClassLoader.php(427): Composer\Autoload\{closure}('D:\\work\\hotel\\a...')
#1 [internal function]: Composer\Autoload\ClassLoader->loadClass('app\\api\\control...')
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(452): class_exists('app\\api\\control...')
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(472): Webman\Route::convertToCallable('/register', Array)
#4 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(132): Webman\Route::addRoute('POST', '/register', Array)
#5 D:\work\hotel\all\itinerary-api\config\route.php(37): Webman\Route::post('/register', Array)
#6 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#7 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/api/user', Object(Closure))
#8 D:\work\hotel\all\itinerary-api\config\route.php(35): Webman\Route::group('/api/user', Object(Closure))
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#10 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#11 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#12 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#14 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#15 [internal function]: Workerman\Worker->Workerman\{closure}()
#16 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#17 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#18 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#19 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#20 D:\work\hotel\all\itinerary-api\runtime\windows\start_monitor.php(33): Workerman\Worker::runAll()
#21 {main}
2025-08-06 14:41:55 pid:1 ParseError: syntax error, unexpected token "}" in D:\work\hotel\all\itinerary-api\app\api\controller\User.php:85
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\composer\ClassLoader.php(427): Composer\Autoload\{closure}('D:\\work\\hotel\\a...')
#1 [internal function]: Composer\Autoload\ClassLoader->loadClass('app\\api\\control...')
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(452): class_exists('app\\api\\control...')
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(472): Webman\Route::convertToCallable('/register', Array)
#4 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(132): Webman\Route::addRoute('POST', '/register', Array)
#5 D:\work\hotel\all\itinerary-api\config\route.php(37): Webman\Route::post('/register', Array)
#6 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#7 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/api/user', Object(Closure))
#8 D:\work\hotel\all\itinerary-api\config\route.php(35): Webman\Route::group('/api/user', Object(Closure))
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#10 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#11 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#12 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#14 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#15 [internal function]: Workerman\Worker->Workerman\{closure}()
#16 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#17 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#18 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#19 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#20 D:\work\hotel\all\itinerary-api\runtime\windows\start_monitor.php(33): Workerman\Worker::runAll()
#21 {main}
2025-08-06 14:41:55 pid:1 ParseError: syntax error, unexpected token "}" in D:\work\hotel\all\itinerary-api\app\api\controller\User.php:85
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\composer\ClassLoader.php(427): Composer\Autoload\{closure}('D:\\work\\hotel\\a...')
#1 [internal function]: Composer\Autoload\ClassLoader->loadClass('app\\api\\control...')
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(452): class_exists('app\\api\\control...')
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(472): Webman\Route::convertToCallable('/register', Array)
#4 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(132): Webman\Route::addRoute('POST', '/register', Array)
#5 D:\work\hotel\all\itinerary-api\config\route.php(37): Webman\Route::post('/register', Array)
#6 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#7 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/api/user', Object(Closure))
#8 D:\work\hotel\all\itinerary-api\config\route.php(35): Webman\Route::group('/api/user', Object(Closure))
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#10 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#11 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#12 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#14 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#15 [internal function]: Workerman\Worker->Workerman\{closure}()
#16 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#17 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#18 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#19 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#20 D:\work\hotel\all\itinerary-api\runtime\windows\start_webman.php(33): Workerman\Worker::runAll()
#21 {main}
2025-08-06 14:41:55 pid:1 ParseError: syntax error, unexpected token "}" in D:\work\hotel\all\itinerary-api\app\api\controller\User.php:85
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\composer\ClassLoader.php(427): Composer\Autoload\{closure}('D:\\work\\hotel\\a...')
#1 [internal function]: Composer\Autoload\ClassLoader->loadClass('app\\api\\control...')
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(452): class_exists('app\\api\\control...')
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(472): Webman\Route::convertToCallable('/register', Array)
#4 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(132): Webman\Route::addRoute('POST', '/register', Array)
#5 D:\work\hotel\all\itinerary-api\config\route.php(37): Webman\Route::post('/register', Array)
#6 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#7 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/api/user', Object(Closure))
#8 D:\work\hotel\all\itinerary-api\config\route.php(35): Webman\Route::group('/api/user', Object(Closure))
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#10 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#11 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#12 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#14 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#15 [internal function]: Workerman\Worker->Workerman\{closure}()
#16 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#17 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#18 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#19 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#20 D:\work\hotel\all\itinerary-api\runtime\windows\start_plugin.webman.redis-queue.consumer.php(33): Workerman\Worker::runAll()
#21 {main}
2025-08-06 14:41:58 pid:1 ParseError: syntax error, unexpected token "}" in D:\work\hotel\all\itinerary-api\app\api\controller\User.php:85
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\composer\ClassLoader.php(427): Composer\Autoload\{closure}('D:\\work\\hotel\\a...')
#1 [internal function]: Composer\Autoload\ClassLoader->loadClass('app\\api\\control...')
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(452): class_exists('app\\api\\control...')
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(472): Webman\Route::convertToCallable('/register', Array)
#4 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(132): Webman\Route::addRoute('POST', '/register', Array)
#5 D:\work\hotel\all\itinerary-api\config\route.php(37): Webman\Route::post('/register', Array)
#6 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#7 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/api/user', Object(Closure))
#8 D:\work\hotel\all\itinerary-api\config\route.php(35): Webman\Route::group('/api/user', Object(Closure))
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#10 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#11 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#12 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#14 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#15 [internal function]: Workerman\Worker->Workerman\{closure}()
#16 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#17 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#18 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#19 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#20 D:\work\hotel\all\itinerary-api\runtime\windows\start_plugin.webman.redis-queue.consumer.php(33): Workerman\Worker::runAll()
#21 {main}
2025-08-06 14:41:58 pid:1 ParseError: syntax error, unexpected token "}" in D:\work\hotel\all\itinerary-api\app\api\controller\User.php:85
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\composer\ClassLoader.php(427): Composer\Autoload\{closure}('D:\\work\\hotel\\a...')
#1 [internal function]: Composer\Autoload\ClassLoader->loadClass('app\\api\\control...')
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(452): class_exists('app\\api\\control...')
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(472): Webman\Route::convertToCallable('/register', Array)
#4 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(132): Webman\Route::addRoute('POST', '/register', Array)
#5 D:\work\hotel\all\itinerary-api\config\route.php(37): Webman\Route::post('/register', Array)
#6 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#7 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/api/user', Object(Closure))
#8 D:\work\hotel\all\itinerary-api\config\route.php(35): Webman\Route::group('/api/user', Object(Closure))
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#10 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#11 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#12 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#14 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#15 [internal function]: Workerman\Worker->Workerman\{closure}()
#16 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#17 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#18 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#19 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#20 D:\work\hotel\all\itinerary-api\runtime\windows\start_monitor.php(33): Workerman\Worker::runAll()
#21 {main}
2025-08-06 14:41:58 pid:1 ParseError: syntax error, unexpected token "}" in D:\work\hotel\all\itinerary-api\app\api\controller\User.php:85
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\composer\ClassLoader.php(427): Composer\Autoload\{closure}('D:\\work\\hotel\\a...')
#1 [internal function]: Composer\Autoload\ClassLoader->loadClass('app\\api\\control...')
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(452): class_exists('app\\api\\control...')
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(472): Webman\Route::convertToCallable('/register', Array)
#4 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(132): Webman\Route::addRoute('POST', '/register', Array)
#5 D:\work\hotel\all\itinerary-api\config\route.php(37): Webman\Route::post('/register', Array)
#6 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#7 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/api/user', Object(Closure))
#8 D:\work\hotel\all\itinerary-api\config\route.php(35): Webman\Route::group('/api/user', Object(Closure))
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#10 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#11 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#12 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#14 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#15 [internal function]: Workerman\Worker->Workerman\{closure}()
#16 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#17 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#18 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#19 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#20 D:\work\hotel\all\itinerary-api\runtime\windows\start_webman.php(33): Workerman\Worker::runAll()
#21 {main}
2025-08-06 14:42:01 pid:1 ParseError: syntax error, unexpected token "}" in D:\work\hotel\all\itinerary-api\app\api\controller\User.php:85
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\composer\ClassLoader.php(427): Composer\Autoload\{closure}('D:\\work\\hotel\\a...')
#1 [internal function]: Composer\Autoload\ClassLoader->loadClass('app\\api\\control...')
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(452): class_exists('app\\api\\control...')
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(472): Webman\Route::convertToCallable('/register', Array)
#4 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(132): Webman\Route::addRoute('POST', '/register', Array)
#5 D:\work\hotel\all\itinerary-api\config\route.php(37): Webman\Route::post('/register', Array)
#6 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#7 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/api/user', Object(Closure))
#8 D:\work\hotel\all\itinerary-api\config\route.php(35): Webman\Route::group('/api/user', Object(Closure))
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#10 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#11 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#12 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#14 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#15 [internal function]: Workerman\Worker->Workerman\{closure}()
#16 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#17 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#18 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#19 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#20 D:\work\hotel\all\itinerary-api\runtime\windows\start_plugin.webman.redis-queue.consumer.php(33): Workerman\Worker::runAll()
#21 {main}
2025-08-06 14:42:01 pid:1 ParseError: syntax error, unexpected token "}" in D:\work\hotel\all\itinerary-api\app\api\controller\User.php:85
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\composer\ClassLoader.php(427): Composer\Autoload\{closure}('D:\\work\\hotel\\a...')
#1 [internal function]: Composer\Autoload\ClassLoader->loadClass('app\\api\\control...')
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(452): class_exists('app\\api\\control...')
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(472): Webman\Route::convertToCallable('/register', Array)
#4 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(132): Webman\Route::addRoute('POST', '/register', Array)
#5 D:\work\hotel\all\itinerary-api\config\route.php(37): Webman\Route::post('/register', Array)
#6 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#7 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/api/user', Object(Closure))
#8 D:\work\hotel\all\itinerary-api\config\route.php(35): Webman\Route::group('/api/user', Object(Closure))
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#10 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#11 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#12 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#14 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#15 [internal function]: Workerman\Worker->Workerman\{closure}()
#16 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#17 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#18 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#19 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#20 D:\work\hotel\all\itinerary-api\runtime\windows\start_webman.php(33): Workerman\Worker::runAll()
#21 {main}
2025-08-06 14:42:01 pid:1 ParseError: syntax error, unexpected token "}" in D:\work\hotel\all\itinerary-api\app\api\controller\User.php:85
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\composer\ClassLoader.php(427): Composer\Autoload\{closure}('D:\\work\\hotel\\a...')
#1 [internal function]: Composer\Autoload\ClassLoader->loadClass('app\\api\\control...')
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(452): class_exists('app\\api\\control...')
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(472): Webman\Route::convertToCallable('/register', Array)
#4 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(132): Webman\Route::addRoute('POST', '/register', Array)
#5 D:\work\hotel\all\itinerary-api\config\route.php(37): Webman\Route::post('/register', Array)
#6 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#7 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/api/user', Object(Closure))
#8 D:\work\hotel\all\itinerary-api\config\route.php(35): Webman\Route::group('/api/user', Object(Closure))
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#10 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#11 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#12 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#14 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#15 [internal function]: Workerman\Worker->Workerman\{closure}()
#16 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#17 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#18 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#19 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#20 D:\work\hotel\all\itinerary-api\runtime\windows\start_monitor.php(33): Workerman\Worker::runAll()
#21 {main}
2025-08-06 16:57:46 pid:1 FastRoute\BadRouteException: Cannot register two routes matching "/api/user/register" for method "POST" in D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\DataGenerator\RegexBasedAbstract.php:86
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\DataGenerator\RegexBasedAbstract.php(30): FastRoute\DataGenerator\RegexBasedAbstract->addStaticRoute('POST', Array, Array)
#1 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(44): FastRoute\DataGenerator\RegexBasedAbstract->addRoute('POST', Array, Array)
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(473): FastRoute\RouteCollector->addRoute('POST', '/api/user/regis...', Array)
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(132): Webman\Route::addRoute('POST', '/register', Array)
#4 D:\work\hotel\all\itinerary-api\config\route.php(56): Webman\Route::post('/register', Array)
#5 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#6 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/api/user', Object(Closure))
#7 D:\work\hotel\all\itinerary-api\config\route.php(54): Webman\Route::group('/api/user', Object(Closure))
#8 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#9 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#10 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#11 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#12 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#14 [internal function]: Workerman\Worker->Workerman\{closure}()
#15 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#16 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#17 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#18 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#19 D:\work\hotel\all\itinerary-api\runtime\windows\start_webman.php(33): Workerman\Worker::runAll()
#20 {main}
2025-08-06 16:57:46 pid:1 FastRoute\BadRouteException: Cannot register two routes matching "/api/user/register" for method "POST" in D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\DataGenerator\RegexBasedAbstract.php:86
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\DataGenerator\RegexBasedAbstract.php(30): FastRoute\DataGenerator\RegexBasedAbstract->addStaticRoute('POST', Array, Array)
#1 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(44): FastRoute\DataGenerator\RegexBasedAbstract->addRoute('POST', Array, Array)
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(473): FastRoute\RouteCollector->addRoute('POST', '/api/user/regis...', Array)
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(132): Webman\Route::addRoute('POST', '/register', Array)
#4 D:\work\hotel\all\itinerary-api\config\route.php(56): Webman\Route::post('/register', Array)
#5 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#6 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/api/user', Object(Closure))
#7 D:\work\hotel\all\itinerary-api\config\route.php(54): Webman\Route::group('/api/user', Object(Closure))
#8 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#9 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#10 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#11 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#12 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#14 [internal function]: Workerman\Worker->Workerman\{closure}()
#15 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#16 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#17 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#18 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#19 D:\work\hotel\all\itinerary-api\runtime\windows\start_plugin.webman.redis-queue.consumer.php(33): Workerman\Worker::runAll()
#20 {main}
2025-08-06 16:57:46 pid:1 FastRoute\BadRouteException: Cannot register two routes matching "/api/user/register" for method "POST" in D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\DataGenerator\RegexBasedAbstract.php:86
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\DataGenerator\RegexBasedAbstract.php(30): FastRoute\DataGenerator\RegexBasedAbstract->addStaticRoute('POST', Array, Array)
#1 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(44): FastRoute\DataGenerator\RegexBasedAbstract->addRoute('POST', Array, Array)
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(473): FastRoute\RouteCollector->addRoute('POST', '/api/user/regis...', Array)
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(132): Webman\Route::addRoute('POST', '/register', Array)
#4 D:\work\hotel\all\itinerary-api\config\route.php(56): Webman\Route::post('/register', Array)
#5 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#6 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/api/user', Object(Closure))
#7 D:\work\hotel\all\itinerary-api\config\route.php(54): Webman\Route::group('/api/user', Object(Closure))
#8 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#9 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#10 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#11 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#12 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#14 [internal function]: Workerman\Worker->Workerman\{closure}()
#15 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#16 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#17 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#18 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#19 D:\work\hotel\all\itinerary-api\runtime\windows\start_monitor.php(33): Workerman\Worker::runAll()
#20 {main}
2025-08-06 16:57:49 pid:1 FastRoute\BadRouteException: Cannot register two routes matching "/api/user/register" for method "POST" in D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\DataGenerator\RegexBasedAbstract.php:86
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\DataGenerator\RegexBasedAbstract.php(30): FastRoute\DataGenerator\RegexBasedAbstract->addStaticRoute('POST', Array, Array)
#1 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(44): FastRoute\DataGenerator\RegexBasedAbstract->addRoute('POST', Array, Array)
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(473): FastRoute\RouteCollector->addRoute('POST', '/api/user/regis...', Array)
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(132): Webman\Route::addRoute('POST', '/register', Array)
#4 D:\work\hotel\all\itinerary-api\config\route.php(56): Webman\Route::post('/register', Array)
#5 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#6 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/api/user', Object(Closure))
#7 D:\work\hotel\all\itinerary-api\config\route.php(54): Webman\Route::group('/api/user', Object(Closure))
#8 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#9 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#10 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#11 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#12 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#14 [internal function]: Workerman\Worker->Workerman\{closure}()
#15 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#16 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#17 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#18 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#19 D:\work\hotel\all\itinerary-api\runtime\windows\start_monitor.php(33): Workerman\Worker::runAll()
#20 {main}
2025-08-06 16:57:49 pid:1 FastRoute\BadRouteException: Cannot register two routes matching "/api/user/register" for method "POST" in D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\DataGenerator\RegexBasedAbstract.php:86
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\DataGenerator\RegexBasedAbstract.php(30): FastRoute\DataGenerator\RegexBasedAbstract->addStaticRoute('POST', Array, Array)
#1 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(44): FastRoute\DataGenerator\RegexBasedAbstract->addRoute('POST', Array, Array)
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(473): FastRoute\RouteCollector->addRoute('POST', '/api/user/regis...', Array)
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(132): Webman\Route::addRoute('POST', '/register', Array)
#4 D:\work\hotel\all\itinerary-api\config\route.php(56): Webman\Route::post('/register', Array)
#5 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#6 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/api/user', Object(Closure))
#7 D:\work\hotel\all\itinerary-api\config\route.php(54): Webman\Route::group('/api/user', Object(Closure))
#8 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#9 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#10 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#11 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#12 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#14 [internal function]: Workerman\Worker->Workerman\{closure}()
#15 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#16 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#17 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#18 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#19 D:\work\hotel\all\itinerary-api\runtime\windows\start_webman.php(33): Workerman\Worker::runAll()
#20 {main}
2025-08-06 16:57:49 pid:1 FastRoute\BadRouteException: Cannot register two routes matching "/api/user/register" for method "POST" in D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\DataGenerator\RegexBasedAbstract.php:86
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\DataGenerator\RegexBasedAbstract.php(30): FastRoute\DataGenerator\RegexBasedAbstract->addStaticRoute('POST', Array, Array)
#1 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(44): FastRoute\DataGenerator\RegexBasedAbstract->addRoute('POST', Array, Array)
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(473): FastRoute\RouteCollector->addRoute('POST', '/api/user/regis...', Array)
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(132): Webman\Route::addRoute('POST', '/register', Array)
#4 D:\work\hotel\all\itinerary-api\config\route.php(56): Webman\Route::post('/register', Array)
#5 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#6 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/api/user', Object(Closure))
#7 D:\work\hotel\all\itinerary-api\config\route.php(54): Webman\Route::group('/api/user', Object(Closure))
#8 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#9 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#10 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#11 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#12 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#14 [internal function]: Workerman\Worker->Workerman\{closure}()
#15 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#16 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#17 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#18 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#19 D:\work\hotel\all\itinerary-api\runtime\windows\start_plugin.webman.redis-queue.consumer.php(33): Workerman\Worker::runAll()
#20 {main}
2025-08-06 16:57:52 pid:1 FastRoute\BadRouteException: Cannot register two routes matching "/api/user/register" for method "POST" in D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\DataGenerator\RegexBasedAbstract.php:86
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\DataGenerator\RegexBasedAbstract.php(30): FastRoute\DataGenerator\RegexBasedAbstract->addStaticRoute('POST', Array, Array)
#1 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(44): FastRoute\DataGenerator\RegexBasedAbstract->addRoute('POST', Array, Array)
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(473): FastRoute\RouteCollector->addRoute('POST', '/api/user/regis...', Array)
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(132): Webman\Route::addRoute('POST', '/register', Array)
#4 D:\work\hotel\all\itinerary-api\config\route.php(56): Webman\Route::post('/register', Array)
#5 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#6 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/api/user', Object(Closure))
#7 D:\work\hotel\all\itinerary-api\config\route.php(54): Webman\Route::group('/api/user', Object(Closure))
#8 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#9 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#10 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#11 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#12 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#14 [internal function]: Workerman\Worker->Workerman\{closure}()
#15 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#16 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#17 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#18 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#19 D:\work\hotel\all\itinerary-api\runtime\windows\start_plugin.webman.redis-queue.consumer.php(33): Workerman\Worker::runAll()
#20 {main}
2025-08-06 16:57:52 pid:1 FastRoute\BadRouteException: Cannot register two routes matching "/api/user/register" for method "POST" in D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\DataGenerator\RegexBasedAbstract.php:86
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\DataGenerator\RegexBasedAbstract.php(30): FastRoute\DataGenerator\RegexBasedAbstract->addStaticRoute('POST', Array, Array)
#1 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(44): FastRoute\DataGenerator\RegexBasedAbstract->addRoute('POST', Array, Array)
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(473): FastRoute\RouteCollector->addRoute('POST', '/api/user/regis...', Array)
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(132): Webman\Route::addRoute('POST', '/register', Array)
#4 D:\work\hotel\all\itinerary-api\config\route.php(56): Webman\Route::post('/register', Array)
#5 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#6 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/api/user', Object(Closure))
#7 D:\work\hotel\all\itinerary-api\config\route.php(54): Webman\Route::group('/api/user', Object(Closure))
#8 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#9 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#10 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#11 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#12 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#14 [internal function]: Workerman\Worker->Workerman\{closure}()
#15 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#16 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#17 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#18 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#19 D:\work\hotel\all\itinerary-api\runtime\windows\start_monitor.php(33): Workerman\Worker::runAll()
#20 {main}
2025-08-06 16:57:52 pid:1 FastRoute\BadRouteException: Cannot register two routes matching "/api/user/register" for method "POST" in D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\DataGenerator\RegexBasedAbstract.php:86
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\DataGenerator\RegexBasedAbstract.php(30): FastRoute\DataGenerator\RegexBasedAbstract->addStaticRoute('POST', Array, Array)
#1 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(44): FastRoute\DataGenerator\RegexBasedAbstract->addRoute('POST', Array, Array)
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(473): FastRoute\RouteCollector->addRoute('POST', '/api/user/regis...', Array)
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(132): Webman\Route::addRoute('POST', '/register', Array)
#4 D:\work\hotel\all\itinerary-api\config\route.php(56): Webman\Route::post('/register', Array)
#5 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#6 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/api/user', Object(Closure))
#7 D:\work\hotel\all\itinerary-api\config\route.php(54): Webman\Route::group('/api/user', Object(Closure))
#8 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#9 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#10 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#11 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#12 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#14 [internal function]: Workerman\Worker->Workerman\{closure}()
#15 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#16 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#17 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#18 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#19 D:\work\hotel\all\itinerary-api\runtime\windows\start_webman.php(33): Workerman\Worker::runAll()
#20 {main}
2025-08-07 10:52:22 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot declare class app\api\controller\SystemConfig, because the name is already in use in D:\work\hotel\all\itinerary-api\app\api\controller\User.php on line 13"
2025-08-07 10:52:22 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot declare class app\api\controller\SystemConfig, because the name is already in use in D:\work\hotel\all\itinerary-api\app\api\controller\User.php on line 13"
2025-08-07 10:52:22 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot declare class app\api\controller\SystemConfig, because the name is already in use in D:\work\hotel\all\itinerary-api\app\api\controller\User.php on line 13"
2025-08-07 10:52:24 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot declare class app\api\controller\SystemConfig, because the name is already in use in D:\work\hotel\all\itinerary-api\app\api\controller\User.php on line 13"
2025-08-07 10:52:24 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot declare class app\api\controller\SystemConfig, because the name is already in use in D:\work\hotel\all\itinerary-api\app\api\controller\User.php on line 13"
2025-08-07 10:52:24 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot declare class app\api\controller\SystemConfig, because the name is already in use in D:\work\hotel\all\itinerary-api\app\api\controller\User.php on line 13"
2025-08-07 10:52:26 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot declare class app\api\controller\SystemConfig, because the name is already in use in D:\work\hotel\all\itinerary-api\app\api\controller\User.php on line 13"
2025-08-07 10:52:26 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot declare class app\api\controller\SystemConfig, because the name is already in use in D:\work\hotel\all\itinerary-api\app\api\controller\User.php on line 13"
2025-08-07 10:52:26 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot declare class app\api\controller\SystemConfig, because the name is already in use in D:\work\hotel\all\itinerary-api\app\api\controller\User.php on line 13"
2025-08-07 10:52:28 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot declare class app\api\controller\SystemConfig, because the name is already in use in D:\work\hotel\all\itinerary-api\app\api\controller\User.php on line 13"
2025-08-07 10:52:28 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot declare class app\api\controller\SystemConfig, because the name is already in use in D:\work\hotel\all\itinerary-api\app\api\controller\User.php on line 13"
2025-08-07 10:52:28 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot declare class app\api\controller\SystemConfig, because the name is already in use in D:\work\hotel\all\itinerary-api\app\api\controller\User.php on line 13"
2025-08-07 10:52:30 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot declare class app\api\controller\SystemConfig, because the name is already in use in D:\work\hotel\all\itinerary-api\app\api\controller\User.php on line 13"
2025-08-07 10:52:30 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot declare class app\api\controller\SystemConfig, because the name is already in use in D:\work\hotel\all\itinerary-api\app\api\controller\User.php on line 13"
2025-08-07 10:52:30 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot declare class app\api\controller\SystemConfig, because the name is already in use in D:\work\hotel\all\itinerary-api\app\api\controller\User.php on line 13"
2025-08-07 10:52:32 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot declare class app\api\controller\SystemConfig, because the name is already in use in D:\work\hotel\all\itinerary-api\app\api\controller\User.php on line 13"
2025-08-07 10:52:32 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot declare class app\api\controller\SystemConfig, because the name is already in use in D:\work\hotel\all\itinerary-api\app\api\controller\User.php on line 13"
2025-08-07 10:52:32 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot declare class app\api\controller\SystemConfig, because the name is already in use in D:\work\hotel\all\itinerary-api\app\api\controller\User.php on line 13"
2025-08-07 10:52:34 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot declare class app\api\controller\SystemConfig, because the name is already in use in D:\work\hotel\all\itinerary-api\app\api\controller\User.php on line 13"
2025-08-07 10:52:34 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot declare class app\api\controller\SystemConfig, because the name is already in use in D:\work\hotel\all\itinerary-api\app\api\controller\User.php on line 13"
2025-08-07 10:52:34 pid:1 Worker process terminated with ERROR: E_COMPILE_ERROR "Cannot declare class app\api\controller\SystemConfig, because the name is already in use in D:\work\hotel\all\itinerary-api\app\api\controller\User.php on line 13"
2025-08-07 11:34:46 pid:1 Error: Class "app\admin\controller\BaseController" not found in D:\work\hotel\all\itinerary-api\app\admin\controller\SystemConfigController.php:14
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\composer\ClassLoader.php(478): include()
#1 D:\work\hotel\all\itinerary-api\vendor\composer\ClassLoader.php(346): Composer\Autoload\includeFile('D:\\work\\hotel\\a...')
#2 [internal function]: Composer\Autoload\ClassLoader->loadClass('app\\admin\\contr...')
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(452): class_exists('app\\admin\\contr...')
#4 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(472): Webman\Route::convertToCallable('', Array)
#5 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(122): Webman\Route::addRoute('GET', '', Array)
#6 D:\work\hotel\all\itinerary-api\config\route.php(148): Webman\Route::get('', Array)
#7 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#8 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/system-config', Object(Closure))
#9 D:\work\hotel\all\itinerary-api\config\route.php(146): Webman\Route::group('/system-config', Object(Closure))
#10 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#11 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#12 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#13 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#14 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#15 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#16 [internal function]: Workerman\Worker->Workerman\{closure}()
#17 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#18 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#19 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#20 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#21 D:\work\hotel\all\itinerary-api\runtime\windows\start_monitor.php(33): Workerman\Worker::runAll()
#22 {main}
2025-08-07 11:34:46 pid:1 Error: Class "app\admin\controller\BaseController" not found in D:\work\hotel\all\itinerary-api\app\admin\controller\SystemConfigController.php:14
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\composer\ClassLoader.php(478): include()
#1 D:\work\hotel\all\itinerary-api\vendor\composer\ClassLoader.php(346): Composer\Autoload\includeFile('D:\\work\\hotel\\a...')
#2 [internal function]: Composer\Autoload\ClassLoader->loadClass('app\\admin\\contr...')
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(452): class_exists('app\\admin\\contr...')
#4 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(472): Webman\Route::convertToCallable('', Array)
#5 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(122): Webman\Route::addRoute('GET', '', Array)
#6 D:\work\hotel\all\itinerary-api\config\route.php(148): Webman\Route::get('', Array)
#7 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#8 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/system-config', Object(Closure))
#9 D:\work\hotel\all\itinerary-api\config\route.php(146): Webman\Route::group('/system-config', Object(Closure))
#10 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#11 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#12 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#13 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#14 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#15 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#16 [internal function]: Workerman\Worker->Workerman\{closure}()
#17 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#18 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#19 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#20 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#21 D:\work\hotel\all\itinerary-api\runtime\windows\start_webman.php(33): Workerman\Worker::runAll()
#22 {main}
2025-08-07 11:34:46 pid:1 Error: Class "app\admin\controller\BaseController" not found in D:\work\hotel\all\itinerary-api\app\admin\controller\SystemConfigController.php:14
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\composer\ClassLoader.php(478): include()
#1 D:\work\hotel\all\itinerary-api\vendor\composer\ClassLoader.php(346): Composer\Autoload\includeFile('D:\\work\\hotel\\a...')
#2 [internal function]: Composer\Autoload\ClassLoader->loadClass('app\\admin\\contr...')
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(452): class_exists('app\\admin\\contr...')
#4 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(472): Webman\Route::convertToCallable('', Array)
#5 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(122): Webman\Route::addRoute('GET', '', Array)
#6 D:\work\hotel\all\itinerary-api\config\route.php(148): Webman\Route::get('', Array)
#7 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#8 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/system-config', Object(Closure))
#9 D:\work\hotel\all\itinerary-api\config\route.php(146): Webman\Route::group('/system-config', Object(Closure))
#10 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#11 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#12 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#13 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#14 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#15 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#16 [internal function]: Workerman\Worker->Workerman\{closure}()
#17 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#18 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#19 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#20 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#21 D:\work\hotel\all\itinerary-api\runtime\windows\start_plugin.webman.redis-queue.consumer.php(33): Workerman\Worker::runAll()
#22 {main}
2025-08-07 11:34:49 pid:1 Error: Class "app\admin\controller\BaseController" not found in D:\work\hotel\all\itinerary-api\app\admin\controller\SystemConfigController.php:14
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\composer\ClassLoader.php(478): include()
#1 D:\work\hotel\all\itinerary-api\vendor\composer\ClassLoader.php(346): Composer\Autoload\includeFile('D:\\work\\hotel\\a...')
#2 [internal function]: Composer\Autoload\ClassLoader->loadClass('app\\admin\\contr...')
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(452): class_exists('app\\admin\\contr...')
#4 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(472): Webman\Route::convertToCallable('', Array)
#5 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(122): Webman\Route::addRoute('GET', '', Array)
#6 D:\work\hotel\all\itinerary-api\config\route.php(148): Webman\Route::get('', Array)
#7 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#8 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/system-config', Object(Closure))
#9 D:\work\hotel\all\itinerary-api\config\route.php(146): Webman\Route::group('/system-config', Object(Closure))
#10 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#11 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#12 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#13 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#14 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#15 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#16 [internal function]: Workerman\Worker->Workerman\{closure}()
#17 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#18 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#19 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#20 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#21 D:\work\hotel\all\itinerary-api\runtime\windows\start_webman.php(33): Workerman\Worker::runAll()
#22 {main}
2025-08-07 11:34:49 pid:1 Error: Class "app\admin\controller\BaseController" not found in D:\work\hotel\all\itinerary-api\app\admin\controller\SystemConfigController.php:14
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\composer\ClassLoader.php(478): include()
#1 D:\work\hotel\all\itinerary-api\vendor\composer\ClassLoader.php(346): Composer\Autoload\includeFile('D:\\work\\hotel\\a...')
#2 [internal function]: Composer\Autoload\ClassLoader->loadClass('app\\admin\\contr...')
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(452): class_exists('app\\admin\\contr...')
#4 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(472): Webman\Route::convertToCallable('', Array)
#5 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(122): Webman\Route::addRoute('GET', '', Array)
#6 D:\work\hotel\all\itinerary-api\config\route.php(148): Webman\Route::get('', Array)
#7 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#8 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/system-config', Object(Closure))
#9 D:\work\hotel\all\itinerary-api\config\route.php(146): Webman\Route::group('/system-config', Object(Closure))
#10 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#11 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#12 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#13 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#14 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#15 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#16 [internal function]: Workerman\Worker->Workerman\{closure}()
#17 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#18 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#19 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#20 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#21 D:\work\hotel\all\itinerary-api\runtime\windows\start_plugin.webman.redis-queue.consumer.php(33): Workerman\Worker::runAll()
#22 {main}
2025-08-07 11:34:49 pid:1 Error: Class "app\admin\controller\BaseController" not found in D:\work\hotel\all\itinerary-api\app\admin\controller\SystemConfigController.php:14
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\composer\ClassLoader.php(478): include()
#1 D:\work\hotel\all\itinerary-api\vendor\composer\ClassLoader.php(346): Composer\Autoload\includeFile('D:\\work\\hotel\\a...')
#2 [internal function]: Composer\Autoload\ClassLoader->loadClass('app\\admin\\contr...')
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(452): class_exists('app\\admin\\contr...')
#4 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(472): Webman\Route::convertToCallable('', Array)
#5 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(122): Webman\Route::addRoute('GET', '', Array)
#6 D:\work\hotel\all\itinerary-api\config\route.php(148): Webman\Route::get('', Array)
#7 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#8 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/system-config', Object(Closure))
#9 D:\work\hotel\all\itinerary-api\config\route.php(146): Webman\Route::group('/system-config', Object(Closure))
#10 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#11 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#12 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#13 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#14 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#15 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#16 [internal function]: Workerman\Worker->Workerman\{closure}()
#17 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#18 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#19 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#20 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#21 D:\work\hotel\all\itinerary-api\runtime\windows\start_monitor.php(33): Workerman\Worker::runAll()
#22 {main}
2025-08-07 11:34:52 pid:1 Error: Class "app\admin\controller\BaseController" not found in D:\work\hotel\all\itinerary-api\app\admin\controller\SystemConfigController.php:14
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\composer\ClassLoader.php(478): include()
#1 D:\work\hotel\all\itinerary-api\vendor\composer\ClassLoader.php(346): Composer\Autoload\includeFile('D:\\work\\hotel\\a...')
#2 [internal function]: Composer\Autoload\ClassLoader->loadClass('app\\admin\\contr...')
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(452): class_exists('app\\admin\\contr...')
#4 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(472): Webman\Route::convertToCallable('', Array)
#5 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(122): Webman\Route::addRoute('GET', '', Array)
#6 D:\work\hotel\all\itinerary-api\config\route.php(148): Webman\Route::get('', Array)
#7 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#8 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/system-config', Object(Closure))
#9 D:\work\hotel\all\itinerary-api\config\route.php(146): Webman\Route::group('/system-config', Object(Closure))
#10 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#11 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#12 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#13 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#14 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#15 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#16 [internal function]: Workerman\Worker->Workerman\{closure}()
#17 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#18 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#19 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#20 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#21 D:\work\hotel\all\itinerary-api\runtime\windows\start_webman.php(33): Workerman\Worker::runAll()
#22 {main}
2025-08-07 11:34:52 pid:1 Error: Class "app\admin\controller\BaseController" not found in D:\work\hotel\all\itinerary-api\app\admin\controller\SystemConfigController.php:14
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\composer\ClassLoader.php(478): include()
#1 D:\work\hotel\all\itinerary-api\vendor\composer\ClassLoader.php(346): Composer\Autoload\includeFile('D:\\work\\hotel\\a...')
#2 [internal function]: Composer\Autoload\ClassLoader->loadClass('app\\admin\\contr...')
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(452): class_exists('app\\admin\\contr...')
#4 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(472): Webman\Route::convertToCallable('', Array)
#5 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(122): Webman\Route::addRoute('GET', '', Array)
#6 D:\work\hotel\all\itinerary-api\config\route.php(148): Webman\Route::get('', Array)
#7 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#8 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/system-config', Object(Closure))
#9 D:\work\hotel\all\itinerary-api\config\route.php(146): Webman\Route::group('/system-config', Object(Closure))
#10 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#11 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#12 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#13 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#14 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#15 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#16 [internal function]: Workerman\Worker->Workerman\{closure}()
#17 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#18 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#19 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#20 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#21 D:\work\hotel\all\itinerary-api\runtime\windows\start_plugin.webman.redis-queue.consumer.php(33): Workerman\Worker::runAll()
#22 {main}
2025-08-07 11:34:52 pid:1 Error: Class "app\admin\controller\BaseController" not found in D:\work\hotel\all\itinerary-api\app\admin\controller\SystemConfigController.php:14
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\composer\ClassLoader.php(478): include()
#1 D:\work\hotel\all\itinerary-api\vendor\composer\ClassLoader.php(346): Composer\Autoload\includeFile('D:\\work\\hotel\\a...')
#2 [internal function]: Composer\Autoload\ClassLoader->loadClass('app\\admin\\contr...')
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(452): class_exists('app\\admin\\contr...')
#4 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(472): Webman\Route::convertToCallable('', Array)
#5 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(122): Webman\Route::addRoute('GET', '', Array)
#6 D:\work\hotel\all\itinerary-api\config\route.php(148): Webman\Route::get('', Array)
#7 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#8 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/system-config', Object(Closure))
#9 D:\work\hotel\all\itinerary-api\config\route.php(146): Webman\Route::group('/system-config', Object(Closure))
#10 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#11 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#12 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#13 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#14 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#15 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#16 [internal function]: Workerman\Worker->Workerman\{closure}()
#17 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#18 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#19 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#20 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#21 D:\work\hotel\all\itinerary-api\runtime\windows\start_monitor.php(33): Workerman\Worker::runAll()
#22 {main}
2025-08-08 17:40:13 pid:1 FastRoute\BadRouteException: Cannot register two routes matching "/admin/live\-room/([^/]+)/products/([^/]+)" for method "DELETE" in D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\DataGenerator\RegexBasedAbstract.php:111
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\DataGenerator\RegexBasedAbstract.php(32): FastRoute\DataGenerator\RegexBasedAbstract->addVariableRoute('DELETE', Array, Array)
#1 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(44): FastRoute\DataGenerator\RegexBasedAbstract->addRoute('DELETE', Array, Array)
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(473): FastRoute\RouteCollector->addRoute('DELETE', '/admin/live-roo...', Array)
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(162): Webman\Route::addRoute('DELETE', '/{id}/products/...', Array)
#4 D:\work\hotel\all\itinerary-api\config\route.php(164): Webman\Route::delete('/{id}/products/...', Array)
#5 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#6 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/admin/live-roo...', Object(Closure))
#7 D:\work\hotel\all\itinerary-api\config\route.php(148): Webman\Route::group('/admin/live-roo...', Object(Closure))
#8 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#9 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#10 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#11 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#12 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#14 [internal function]: Workerman\Worker->Workerman\{closure}()
#15 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#16 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#17 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#18 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#19 D:\work\hotel\all\itinerary-api\runtime\windows\start_monitor.php(33): Workerman\Worker::runAll()
#20 {main}
2025-08-08 17:40:13 pid:1 FastRoute\BadRouteException: Cannot register two routes matching "/admin/live\-room/([^/]+)/products/([^/]+)" for method "DELETE" in D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\DataGenerator\RegexBasedAbstract.php:111
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\DataGenerator\RegexBasedAbstract.php(32): FastRoute\DataGenerator\RegexBasedAbstract->addVariableRoute('DELETE', Array, Array)
#1 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(44): FastRoute\DataGenerator\RegexBasedAbstract->addRoute('DELETE', Array, Array)
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(473): FastRoute\RouteCollector->addRoute('DELETE', '/admin/live-roo...', Array)
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(162): Webman\Route::addRoute('DELETE', '/{id}/products/...', Array)
#4 D:\work\hotel\all\itinerary-api\config\route.php(164): Webman\Route::delete('/{id}/products/...', Array)
#5 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#6 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/admin/live-roo...', Object(Closure))
#7 D:\work\hotel\all\itinerary-api\config\route.php(148): Webman\Route::group('/admin/live-roo...', Object(Closure))
#8 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#9 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#10 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#11 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#12 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#14 [internal function]: Workerman\Worker->Workerman\{closure}()
#15 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#16 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#17 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#18 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#19 D:\work\hotel\all\itinerary-api\runtime\windows\start_webman.php(33): Workerman\Worker::runAll()
#20 {main}
2025-08-08 17:40:13 pid:1 FastRoute\BadRouteException: Cannot register two routes matching "/admin/live\-room/([^/]+)/products/([^/]+)" for method "DELETE" in D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\DataGenerator\RegexBasedAbstract.php:111
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\DataGenerator\RegexBasedAbstract.php(32): FastRoute\DataGenerator\RegexBasedAbstract->addVariableRoute('DELETE', Array, Array)
#1 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(44): FastRoute\DataGenerator\RegexBasedAbstract->addRoute('DELETE', Array, Array)
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(473): FastRoute\RouteCollector->addRoute('DELETE', '/admin/live-roo...', Array)
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(162): Webman\Route::addRoute('DELETE', '/{id}/products/...', Array)
#4 D:\work\hotel\all\itinerary-api\config\route.php(164): Webman\Route::delete('/{id}/products/...', Array)
#5 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#6 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/admin/live-roo...', Object(Closure))
#7 D:\work\hotel\all\itinerary-api\config\route.php(148): Webman\Route::group('/admin/live-roo...', Object(Closure))
#8 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#9 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#10 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#11 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#12 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#14 [internal function]: Workerman\Worker->Workerman\{closure}()
#15 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#16 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#17 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#18 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#19 D:\work\hotel\all\itinerary-api\runtime\windows\start_plugin.webman.redis-queue.consumer.php(33): Workerman\Worker::runAll()
#20 {main}
2025-08-11 13:47:26 pid:1 ArgumentCountError: Too few arguments to function Webman\Route::group(), 0 passed in D:\work\hotel\all\itinerary-api\config\route.php on line 149 and at least 1 expected in D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php:211
Stack trace:
#0 D:\work\hotel\all\itinerary-api\config\route.php(149): Webman\Route::group()
#1 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/admin', Object(Closure))
#3 D:\work\hotel\all\itinerary-api\config\route.php(148): Webman\Route::group('/admin', Object(Closure))
#4 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#5 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#6 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#7 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#8 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#10 [internal function]: Workerman\Worker->Workerman\{closure}()
#11 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#12 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#14 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#15 D:\work\hotel\all\itinerary-api\runtime\windows\start_webman.php(33): Workerman\Worker::runAll()
#16 {main}
2025-08-11 13:47:26 pid:1 ArgumentCountError: Too few arguments to function Webman\Route::group(), 0 passed in D:\work\hotel\all\itinerary-api\config\route.php on line 149 and at least 1 expected in D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php:211
Stack trace:
#0 D:\work\hotel\all\itinerary-api\config\route.php(149): Webman\Route::group()
#1 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/admin', Object(Closure))
#3 D:\work\hotel\all\itinerary-api\config\route.php(148): Webman\Route::group('/admin', Object(Closure))
#4 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#5 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#6 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#7 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#8 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#10 [internal function]: Workerman\Worker->Workerman\{closure}()
#11 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#12 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#14 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#15 D:\work\hotel\all\itinerary-api\runtime\windows\start_monitor.php(33): Workerman\Worker::runAll()
#16 {main}
2025-08-11 13:47:26 pid:1 ArgumentCountError: Too few arguments to function Webman\Route::group(), 0 passed in D:\work\hotel\all\itinerary-api\config\route.php on line 149 and at least 1 expected in D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php:211
Stack trace:
#0 D:\work\hotel\all\itinerary-api\config\route.php(149): Webman\Route::group()
#1 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/admin', Object(Closure))
#3 D:\work\hotel\all\itinerary-api\config\route.php(148): Webman\Route::group('/admin', Object(Closure))
#4 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#5 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#6 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#7 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#8 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#10 [internal function]: Workerman\Worker->Workerman\{closure}()
#11 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#12 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#14 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#15 D:\work\hotel\all\itinerary-api\runtime\windows\start_plugin.webman.redis-queue.consumer.php(33): Workerman\Worker::runAll()
#16 {main}
2025-08-11 13:47:29 pid:1 ArgumentCountError: Too few arguments to function Webman\Route::group(), 0 passed in D:\work\hotel\all\itinerary-api\config\route.php on line 149 and at least 1 expected in D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php:211
Stack trace:
#0 D:\work\hotel\all\itinerary-api\config\route.php(149): Webman\Route::group()
#1 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/admin', Object(Closure))
#3 D:\work\hotel\all\itinerary-api\config\route.php(148): Webman\Route::group('/admin', Object(Closure))
#4 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#5 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#6 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#7 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#8 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#10 [internal function]: Workerman\Worker->Workerman\{closure}()
#11 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#12 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#14 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#15 D:\work\hotel\all\itinerary-api\runtime\windows\start_plugin.webman.redis-queue.consumer.php(33): Workerman\Worker::runAll()
#16 {main}
2025-08-11 13:47:29 pid:1 ArgumentCountError: Too few arguments to function Webman\Route::group(), 0 passed in D:\work\hotel\all\itinerary-api\config\route.php on line 149 and at least 1 expected in D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php:211
Stack trace:
#0 D:\work\hotel\all\itinerary-api\config\route.php(149): Webman\Route::group()
#1 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/admin', Object(Closure))
#3 D:\work\hotel\all\itinerary-api\config\route.php(148): Webman\Route::group('/admin', Object(Closure))
#4 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#5 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#6 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#7 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#8 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#10 [internal function]: Workerman\Worker->Workerman\{closure}()
#11 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#12 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#14 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#15 D:\work\hotel\all\itinerary-api\runtime\windows\start_monitor.php(33): Workerman\Worker::runAll()
#16 {main}
2025-08-11 13:47:29 pid:1 ArgumentCountError: Too few arguments to function Webman\Route::group(), 0 passed in D:\work\hotel\all\itinerary-api\config\route.php on line 149 and at least 1 expected in D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php:211
Stack trace:
#0 D:\work\hotel\all\itinerary-api\config\route.php(149): Webman\Route::group()
#1 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/admin', Object(Closure))
#3 D:\work\hotel\all\itinerary-api\config\route.php(148): Webman\Route::group('/admin', Object(Closure))
#4 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#5 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#6 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#7 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#8 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#10 [internal function]: Workerman\Worker->Workerman\{closure}()
#11 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#12 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#14 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#15 D:\work\hotel\all\itinerary-api\runtime\windows\start_webman.php(33): Workerman\Worker::runAll()
#16 {main}
2025-08-11 13:47:32 pid:1 ArgumentCountError: Too few arguments to function Webman\Route::group(), 0 passed in D:\work\hotel\all\itinerary-api\config\route.php on line 149 and at least 1 expected in D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php:211
Stack trace:
#0 D:\work\hotel\all\itinerary-api\config\route.php(149): Webman\Route::group()
#1 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/admin', Object(Closure))
#3 D:\work\hotel\all\itinerary-api\config\route.php(148): Webman\Route::group('/admin', Object(Closure))
#4 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#5 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#6 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#7 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#8 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#10 [internal function]: Workerman\Worker->Workerman\{closure}()
#11 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#12 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#14 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#15 D:\work\hotel\all\itinerary-api\runtime\windows\start_webman.php(33): Workerman\Worker::runAll()
#16 {main}
2025-08-11 13:47:32 pid:1 ArgumentCountError: Too few arguments to function Webman\Route::group(), 0 passed in D:\work\hotel\all\itinerary-api\config\route.php on line 149 and at least 1 expected in D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php:211
Stack trace:
#0 D:\work\hotel\all\itinerary-api\config\route.php(149): Webman\Route::group()
#1 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/admin', Object(Closure))
#3 D:\work\hotel\all\itinerary-api\config\route.php(148): Webman\Route::group('/admin', Object(Closure))
#4 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#5 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#6 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#7 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#8 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#10 [internal function]: Workerman\Worker->Workerman\{closure}()
#11 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#12 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#14 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#15 D:\work\hotel\all\itinerary-api\runtime\windows\start_plugin.webman.redis-queue.consumer.php(33): Workerman\Worker::runAll()
#16 {main}
2025-08-11 13:47:32 pid:1 ArgumentCountError: Too few arguments to function Webman\Route::group(), 0 passed in D:\work\hotel\all\itinerary-api\config\route.php on line 149 and at least 1 expected in D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php:211
Stack trace:
#0 D:\work\hotel\all\itinerary-api\config\route.php(149): Webman\Route::group()
#1 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/admin', Object(Closure))
#3 D:\work\hotel\all\itinerary-api\config\route.php(148): Webman\Route::group('/admin', Object(Closure))
#4 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#5 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#6 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#7 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#8 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#10 [internal function]: Workerman\Worker->Workerman\{closure}()
#11 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#12 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#14 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#15 D:\work\hotel\all\itinerary-api\runtime\windows\start_monitor.php(33): Workerman\Worker::runAll()
#16 {main}
2025-08-11 13:47:35 pid:1 ArgumentCountError: Too few arguments to function Webman\Route::group(), 0 passed in D:\work\hotel\all\itinerary-api\config\route.php on line 149 and at least 1 expected in D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php:211
Stack trace:
#0 D:\work\hotel\all\itinerary-api\config\route.php(149): Webman\Route::group()
#1 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/admin', Object(Closure))
#3 D:\work\hotel\all\itinerary-api\config\route.php(148): Webman\Route::group('/admin', Object(Closure))
#4 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#5 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#6 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#7 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#8 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#10 [internal function]: Workerman\Worker->Workerman\{closure}()
#11 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#12 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#14 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#15 D:\work\hotel\all\itinerary-api\runtime\windows\start_plugin.webman.redis-queue.consumer.php(33): Workerman\Worker::runAll()
#16 {main}
2025-08-11 13:47:35 pid:1 ArgumentCountError: Too few arguments to function Webman\Route::group(), 0 passed in D:\work\hotel\all\itinerary-api\config\route.php on line 149 and at least 1 expected in D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php:211
Stack trace:
#0 D:\work\hotel\all\itinerary-api\config\route.php(149): Webman\Route::group()
#1 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/admin', Object(Closure))
#3 D:\work\hotel\all\itinerary-api\config\route.php(148): Webman\Route::group('/admin', Object(Closure))
#4 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#5 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#6 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#7 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#8 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#10 [internal function]: Workerman\Worker->Workerman\{closure}()
#11 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#12 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#14 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#15 D:\work\hotel\all\itinerary-api\runtime\windows\start_webman.php(33): Workerman\Worker::runAll()
#16 {main}
2025-08-11 13:47:35 pid:1 ArgumentCountError: Too few arguments to function Webman\Route::group(), 0 passed in D:\work\hotel\all\itinerary-api\config\route.php on line 149 and at least 1 expected in D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php:211
Stack trace:
#0 D:\work\hotel\all\itinerary-api\config\route.php(149): Webman\Route::group()
#1 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/admin', Object(Closure))
#3 D:\work\hotel\all\itinerary-api\config\route.php(148): Webman\Route::group('/admin', Object(Closure))
#4 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#5 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#6 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#7 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#8 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#10 [internal function]: Workerman\Worker->Workerman\{closure}()
#11 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#12 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#14 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#15 D:\work\hotel\all\itinerary-api\runtime\windows\start_monitor.php(33): Workerman\Worker::runAll()
#16 {main}
2025-08-11 16:35:34 pid:1 FastRoute\BadRouteException: Cannot register two routes matching "/system-config/groups" for method "GET" in D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\DataGenerator\RegexBasedAbstract.php:86
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\DataGenerator\RegexBasedAbstract.php(30): FastRoute\DataGenerator\RegexBasedAbstract->addStaticRoute('GET', Array, Array)
#1 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(44): FastRoute\DataGenerator\RegexBasedAbstract->addRoute('GET', Array, Array)
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(473): FastRoute\RouteCollector->addRoute('GET', '/system-config/...', Array)
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(122): Webman\Route::addRoute('GET', '', Array)
#4 D:\work\hotel\all\itinerary-api\config\route.php(200): Webman\Route::get('', Array)
#5 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#6 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/groups', Object(Closure))
#7 D:\work\hotel\all\itinerary-api\config\route.php(198): Webman\Route::group('/groups', Object(Closure))
#8 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/system-config', Object(Closure))
#10 D:\work\hotel\all\itinerary-api\config\route.php(184): Webman\Route::group('/system-config', Object(Closure))
#11 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#12 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#14 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#15 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#16 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#17 [internal function]: Workerman\Worker->Workerman\{closure}()
#18 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#19 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#20 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#21 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#22 D:\work\hotel\all\itinerary-api\runtime\windows\start_webman.php(33): Workerman\Worker::runAll()
#23 {main}
2025-08-11 16:35:34 pid:1 FastRoute\BadRouteException: Cannot register two routes matching "/system-config/groups" for method "GET" in D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\DataGenerator\RegexBasedAbstract.php:86
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\DataGenerator\RegexBasedAbstract.php(30): FastRoute\DataGenerator\RegexBasedAbstract->addStaticRoute('GET', Array, Array)
#1 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(44): FastRoute\DataGenerator\RegexBasedAbstract->addRoute('GET', Array, Array)
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(473): FastRoute\RouteCollector->addRoute('GET', '/system-config/...', Array)
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(122): Webman\Route::addRoute('GET', '', Array)
#4 D:\work\hotel\all\itinerary-api\config\route.php(200): Webman\Route::get('', Array)
#5 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#6 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/groups', Object(Closure))
#7 D:\work\hotel\all\itinerary-api\config\route.php(198): Webman\Route::group('/groups', Object(Closure))
#8 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/system-config', Object(Closure))
#10 D:\work\hotel\all\itinerary-api\config\route.php(184): Webman\Route::group('/system-config', Object(Closure))
#11 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#12 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#14 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#15 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#16 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#17 [internal function]: Workerman\Worker->Workerman\{closure}()
#18 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#19 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#20 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#21 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#22 D:\work\hotel\all\itinerary-api\runtime\windows\start_monitor.php(33): Workerman\Worker::runAll()
#23 {main}
2025-08-11 16:35:34 pid:1 FastRoute\BadRouteException: Cannot register two routes matching "/system-config/groups" for method "GET" in D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\DataGenerator\RegexBasedAbstract.php:86
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\DataGenerator\RegexBasedAbstract.php(30): FastRoute\DataGenerator\RegexBasedAbstract->addStaticRoute('GET', Array, Array)
#1 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(44): FastRoute\DataGenerator\RegexBasedAbstract->addRoute('GET', Array, Array)
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(473): FastRoute\RouteCollector->addRoute('GET', '/system-config/...', Array)
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(122): Webman\Route::addRoute('GET', '', Array)
#4 D:\work\hotel\all\itinerary-api\config\route.php(200): Webman\Route::get('', Array)
#5 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#6 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/groups', Object(Closure))
#7 D:\work\hotel\all\itinerary-api\config\route.php(198): Webman\Route::group('/groups', Object(Closure))
#8 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/system-config', Object(Closure))
#10 D:\work\hotel\all\itinerary-api\config\route.php(184): Webman\Route::group('/system-config', Object(Closure))
#11 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#12 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#14 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#15 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#16 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#17 [internal function]: Workerman\Worker->Workerman\{closure}()
#18 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#19 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#20 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#21 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#22 D:\work\hotel\all\itinerary-api\runtime\windows\start_plugin.webman.redis-queue.consumer.php(33): Workerman\Worker::runAll()
#23 {main}
2025-08-11 16:35:37 pid:1 FastRoute\BadRouteException: Cannot register two routes matching "/system-config/groups" for method "GET" in D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\DataGenerator\RegexBasedAbstract.php:86
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\DataGenerator\RegexBasedAbstract.php(30): FastRoute\DataGenerator\RegexBasedAbstract->addStaticRoute('GET', Array, Array)
#1 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(44): FastRoute\DataGenerator\RegexBasedAbstract->addRoute('GET', Array, Array)
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(473): FastRoute\RouteCollector->addRoute('GET', '/system-config/...', Array)
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(122): Webman\Route::addRoute('GET', '', Array)
#4 D:\work\hotel\all\itinerary-api\config\route.php(200): Webman\Route::get('', Array)
#5 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#6 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/groups', Object(Closure))
#7 D:\work\hotel\all\itinerary-api\config\route.php(198): Webman\Route::group('/groups', Object(Closure))
#8 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/system-config', Object(Closure))
#10 D:\work\hotel\all\itinerary-api\config\route.php(184): Webman\Route::group('/system-config', Object(Closure))
#11 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#12 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#14 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#15 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#16 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#17 [internal function]: Workerman\Worker->Workerman\{closure}()
#18 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#19 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#20 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#21 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#22 D:\work\hotel\all\itinerary-api\runtime\windows\start_plugin.webman.redis-queue.consumer.php(33): Workerman\Worker::runAll()
#23 {main}
2025-08-11 16:35:37 pid:1 FastRoute\BadRouteException: Cannot register two routes matching "/system-config/groups" for method "GET" in D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\DataGenerator\RegexBasedAbstract.php:86
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\DataGenerator\RegexBasedAbstract.php(30): FastRoute\DataGenerator\RegexBasedAbstract->addStaticRoute('GET', Array, Array)
#1 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(44): FastRoute\DataGenerator\RegexBasedAbstract->addRoute('GET', Array, Array)
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(473): FastRoute\RouteCollector->addRoute('GET', '/system-config/...', Array)
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(122): Webman\Route::addRoute('GET', '', Array)
#4 D:\work\hotel\all\itinerary-api\config\route.php(200): Webman\Route::get('', Array)
#5 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#6 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/groups', Object(Closure))
#7 D:\work\hotel\all\itinerary-api\config\route.php(198): Webman\Route::group('/groups', Object(Closure))
#8 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/system-config', Object(Closure))
#10 D:\work\hotel\all\itinerary-api\config\route.php(184): Webman\Route::group('/system-config', Object(Closure))
#11 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#12 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#14 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#15 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#16 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#17 [internal function]: Workerman\Worker->Workerman\{closure}()
#18 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#19 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#20 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#21 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#22 D:\work\hotel\all\itinerary-api\runtime\windows\start_monitor.php(33): Workerman\Worker::runAll()
#23 {main}
2025-08-11 16:35:37 pid:1 FastRoute\BadRouteException: Cannot register two routes matching "/system-config/groups" for method "GET" in D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\DataGenerator\RegexBasedAbstract.php:86
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\DataGenerator\RegexBasedAbstract.php(30): FastRoute\DataGenerator\RegexBasedAbstract->addStaticRoute('GET', Array, Array)
#1 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(44): FastRoute\DataGenerator\RegexBasedAbstract->addRoute('GET', Array, Array)
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(473): FastRoute\RouteCollector->addRoute('GET', '/system-config/...', Array)
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(122): Webman\Route::addRoute('GET', '', Array)
#4 D:\work\hotel\all\itinerary-api\config\route.php(200): Webman\Route::get('', Array)
#5 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#6 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/groups', Object(Closure))
#7 D:\work\hotel\all\itinerary-api\config\route.php(198): Webman\Route::group('/groups', Object(Closure))
#8 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/system-config', Object(Closure))
#10 D:\work\hotel\all\itinerary-api\config\route.php(184): Webman\Route::group('/system-config', Object(Closure))
#11 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#12 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#14 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#15 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#16 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#17 [internal function]: Workerman\Worker->Workerman\{closure}()
#18 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#19 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#20 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#21 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#22 D:\work\hotel\all\itinerary-api\runtime\windows\start_webman.php(33): Workerman\Worker::runAll()
#23 {main}
2025-08-11 16:35:40 pid:1 FastRoute\BadRouteException: Cannot register two routes matching "/system-config/groups" for method "GET" in D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\DataGenerator\RegexBasedAbstract.php:86
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\DataGenerator\RegexBasedAbstract.php(30): FastRoute\DataGenerator\RegexBasedAbstract->addStaticRoute('GET', Array, Array)
#1 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(44): FastRoute\DataGenerator\RegexBasedAbstract->addRoute('GET', Array, Array)
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(473): FastRoute\RouteCollector->addRoute('GET', '/system-config/...', Array)
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(122): Webman\Route::addRoute('GET', '', Array)
#4 D:\work\hotel\all\itinerary-api\config\route.php(200): Webman\Route::get('', Array)
#5 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#6 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/groups', Object(Closure))
#7 D:\work\hotel\all\itinerary-api\config\route.php(198): Webman\Route::group('/groups', Object(Closure))
#8 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/system-config', Object(Closure))
#10 D:\work\hotel\all\itinerary-api\config\route.php(184): Webman\Route::group('/system-config', Object(Closure))
#11 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#12 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#14 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#15 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#16 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#17 [internal function]: Workerman\Worker->Workerman\{closure}()
#18 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#19 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#20 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#21 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#22 D:\work\hotel\all\itinerary-api\runtime\windows\start_monitor.php(33): Workerman\Worker::runAll()
#23 {main}
2025-08-11 16:35:40 pid:1 FastRoute\BadRouteException: Cannot register two routes matching "/system-config/groups" for method "GET" in D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\DataGenerator\RegexBasedAbstract.php:86
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\DataGenerator\RegexBasedAbstract.php(30): FastRoute\DataGenerator\RegexBasedAbstract->addStaticRoute('GET', Array, Array)
#1 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(44): FastRoute\DataGenerator\RegexBasedAbstract->addRoute('GET', Array, Array)
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(473): FastRoute\RouteCollector->addRoute('GET', '/system-config/...', Array)
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(122): Webman\Route::addRoute('GET', '', Array)
#4 D:\work\hotel\all\itinerary-api\config\route.php(200): Webman\Route::get('', Array)
#5 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#6 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/groups', Object(Closure))
#7 D:\work\hotel\all\itinerary-api\config\route.php(198): Webman\Route::group('/groups', Object(Closure))
#8 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/system-config', Object(Closure))
#10 D:\work\hotel\all\itinerary-api\config\route.php(184): Webman\Route::group('/system-config', Object(Closure))
#11 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#12 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#14 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#15 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#16 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#17 [internal function]: Workerman\Worker->Workerman\{closure}()
#18 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#19 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#20 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#21 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#22 D:\work\hotel\all\itinerary-api\runtime\windows\start_plugin.webman.redis-queue.consumer.php(33): Workerman\Worker::runAll()
#23 {main}
2025-08-11 16:35:40 pid:1 FastRoute\BadRouteException: Cannot register two routes matching "/system-config/groups" for method "GET" in D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\DataGenerator\RegexBasedAbstract.php:86
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\DataGenerator\RegexBasedAbstract.php(30): FastRoute\DataGenerator\RegexBasedAbstract->addStaticRoute('GET', Array, Array)
#1 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(44): FastRoute\DataGenerator\RegexBasedAbstract->addRoute('GET', Array, Array)
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(473): FastRoute\RouteCollector->addRoute('GET', '/system-config/...', Array)
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(122): Webman\Route::addRoute('GET', '', Array)
#4 D:\work\hotel\all\itinerary-api\config\route.php(200): Webman\Route::get('', Array)
#5 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#6 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/groups', Object(Closure))
#7 D:\work\hotel\all\itinerary-api\config\route.php(198): Webman\Route::group('/groups', Object(Closure))
#8 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/system-config', Object(Closure))
#10 D:\work\hotel\all\itinerary-api\config\route.php(184): Webman\Route::group('/system-config', Object(Closure))
#11 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#12 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#14 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#15 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#16 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#17 [internal function]: Workerman\Worker->Workerman\{closure}()
#18 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#19 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#20 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#21 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#22 D:\work\hotel\all\itinerary-api\runtime\windows\start_webman.php(33): Workerman\Worker::runAll()
#23 {main}
2025-08-11 16:35:43 pid:1 FastRoute\BadRouteException: Cannot register two routes matching "/system-config/groups" for method "GET" in D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\DataGenerator\RegexBasedAbstract.php:86
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\DataGenerator\RegexBasedAbstract.php(30): FastRoute\DataGenerator\RegexBasedAbstract->addStaticRoute('GET', Array, Array)
#1 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(44): FastRoute\DataGenerator\RegexBasedAbstract->addRoute('GET', Array, Array)
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(473): FastRoute\RouteCollector->addRoute('GET', '/system-config/...', Array)
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(122): Webman\Route::addRoute('GET', '', Array)
#4 D:\work\hotel\all\itinerary-api\config\route.php(200): Webman\Route::get('', Array)
#5 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#6 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/groups', Object(Closure))
#7 D:\work\hotel\all\itinerary-api\config\route.php(198): Webman\Route::group('/groups', Object(Closure))
#8 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/system-config', Object(Closure))
#10 D:\work\hotel\all\itinerary-api\config\route.php(184): Webman\Route::group('/system-config', Object(Closure))
#11 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#12 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#14 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#15 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#16 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#17 [internal function]: Workerman\Worker->Workerman\{closure}()
#18 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#19 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#20 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#21 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#22 D:\work\hotel\all\itinerary-api\runtime\windows\start_webman.php(33): Workerman\Worker::runAll()
#23 {main}
2025-08-11 16:35:43 pid:1 FastRoute\BadRouteException: Cannot register two routes matching "/system-config/groups" for method "GET" in D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\DataGenerator\RegexBasedAbstract.php:86
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\DataGenerator\RegexBasedAbstract.php(30): FastRoute\DataGenerator\RegexBasedAbstract->addStaticRoute('GET', Array, Array)
#1 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(44): FastRoute\DataGenerator\RegexBasedAbstract->addRoute('GET', Array, Array)
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(473): FastRoute\RouteCollector->addRoute('GET', '/system-config/...', Array)
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(122): Webman\Route::addRoute('GET', '', Array)
#4 D:\work\hotel\all\itinerary-api\config\route.php(200): Webman\Route::get('', Array)
#5 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#6 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/groups', Object(Closure))
#7 D:\work\hotel\all\itinerary-api\config\route.php(198): Webman\Route::group('/groups', Object(Closure))
#8 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/system-config', Object(Closure))
#10 D:\work\hotel\all\itinerary-api\config\route.php(184): Webman\Route::group('/system-config', Object(Closure))
#11 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#12 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#14 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#15 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#16 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#17 [internal function]: Workerman\Worker->Workerman\{closure}()
#18 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#19 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#20 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#21 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#22 D:\work\hotel\all\itinerary-api\runtime\windows\start_monitor.php(33): Workerman\Worker::runAll()
#23 {main}
2025-08-11 16:35:43 pid:1 FastRoute\BadRouteException: Cannot register two routes matching "/system-config/groups" for method "GET" in D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\DataGenerator\RegexBasedAbstract.php:86
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\DataGenerator\RegexBasedAbstract.php(30): FastRoute\DataGenerator\RegexBasedAbstract->addStaticRoute('GET', Array, Array)
#1 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(44): FastRoute\DataGenerator\RegexBasedAbstract->addRoute('GET', Array, Array)
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(473): FastRoute\RouteCollector->addRoute('GET', '/system-config/...', Array)
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(122): Webman\Route::addRoute('GET', '', Array)
#4 D:\work\hotel\all\itinerary-api\config\route.php(200): Webman\Route::get('', Array)
#5 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#6 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/groups', Object(Closure))
#7 D:\work\hotel\all\itinerary-api\config\route.php(198): Webman\Route::group('/groups', Object(Closure))
#8 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/system-config', Object(Closure))
#10 D:\work\hotel\all\itinerary-api\config\route.php(184): Webman\Route::group('/system-config', Object(Closure))
#11 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#12 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#14 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#15 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#16 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#17 [internal function]: Workerman\Worker->Workerman\{closure}()
#18 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#19 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#20 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#21 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#22 D:\work\hotel\all\itinerary-api\runtime\windows\start_plugin.webman.redis-queue.consumer.php(33): Workerman\Worker::runAll()
#23 {main}
2025-08-11 16:35:46 pid:1 FastRoute\BadRouteException: Cannot register two routes matching "/system-config/groups" for method "GET" in D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\DataGenerator\RegexBasedAbstract.php:86
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\DataGenerator\RegexBasedAbstract.php(30): FastRoute\DataGenerator\RegexBasedAbstract->addStaticRoute('GET', Array, Array)
#1 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(44): FastRoute\DataGenerator\RegexBasedAbstract->addRoute('GET', Array, Array)
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(473): FastRoute\RouteCollector->addRoute('GET', '/system-config/...', Array)
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(122): Webman\Route::addRoute('GET', '', Array)
#4 D:\work\hotel\all\itinerary-api\config\route.php(200): Webman\Route::get('', Array)
#5 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#6 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/groups', Object(Closure))
#7 D:\work\hotel\all\itinerary-api\config\route.php(198): Webman\Route::group('/groups', Object(Closure))
#8 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/system-config', Object(Closure))
#10 D:\work\hotel\all\itinerary-api\config\route.php(184): Webman\Route::group('/system-config', Object(Closure))
#11 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#12 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#14 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#15 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#16 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#17 [internal function]: Workerman\Worker->Workerman\{closure}()
#18 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#19 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#20 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#21 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#22 D:\work\hotel\all\itinerary-api\runtime\windows\start_monitor.php(33): Workerman\Worker::runAll()
#23 {main}
2025-08-11 16:35:46 pid:1 FastRoute\BadRouteException: Cannot register two routes matching "/system-config/groups" for method "GET" in D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\DataGenerator\RegexBasedAbstract.php:86
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\DataGenerator\RegexBasedAbstract.php(30): FastRoute\DataGenerator\RegexBasedAbstract->addStaticRoute('GET', Array, Array)
#1 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(44): FastRoute\DataGenerator\RegexBasedAbstract->addRoute('GET', Array, Array)
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(473): FastRoute\RouteCollector->addRoute('GET', '/system-config/...', Array)
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(122): Webman\Route::addRoute('GET', '', Array)
#4 D:\work\hotel\all\itinerary-api\config\route.php(200): Webman\Route::get('', Array)
#5 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#6 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/groups', Object(Closure))
#7 D:\work\hotel\all\itinerary-api\config\route.php(198): Webman\Route::group('/groups', Object(Closure))
#8 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/system-config', Object(Closure))
#10 D:\work\hotel\all\itinerary-api\config\route.php(184): Webman\Route::group('/system-config', Object(Closure))
#11 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#12 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#14 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#15 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#16 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#17 [internal function]: Workerman\Worker->Workerman\{closure}()
#18 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#19 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#20 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#21 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#22 D:\work\hotel\all\itinerary-api\runtime\windows\start_plugin.webman.redis-queue.consumer.php(33): Workerman\Worker::runAll()
#23 {main}
2025-08-11 16:35:46 pid:1 FastRoute\BadRouteException: Cannot register two routes matching "/system-config/groups" for method "GET" in D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\DataGenerator\RegexBasedAbstract.php:86
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\DataGenerator\RegexBasedAbstract.php(30): FastRoute\DataGenerator\RegexBasedAbstract->addStaticRoute('GET', Array, Array)
#1 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(44): FastRoute\DataGenerator\RegexBasedAbstract->addRoute('GET', Array, Array)
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(473): FastRoute\RouteCollector->addRoute('GET', '/system-config/...', Array)
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(122): Webman\Route::addRoute('GET', '', Array)
#4 D:\work\hotel\all\itinerary-api\config\route.php(200): Webman\Route::get('', Array)
#5 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#6 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/groups', Object(Closure))
#7 D:\work\hotel\all\itinerary-api\config\route.php(198): Webman\Route::group('/groups', Object(Closure))
#8 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/system-config', Object(Closure))
#10 D:\work\hotel\all\itinerary-api\config\route.php(184): Webman\Route::group('/system-config', Object(Closure))
#11 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#12 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#14 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#15 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#16 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#17 [internal function]: Workerman\Worker->Workerman\{closure}()
#18 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#19 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#20 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#21 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#22 D:\work\hotel\all\itinerary-api\runtime\windows\start_webman.php(33): Workerman\Worker::runAll()
#23 {main}
2025-08-11 16:35:49 pid:1 FastRoute\BadRouteException: Cannot register two routes matching "/system-config/groups" for method "GET" in D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\DataGenerator\RegexBasedAbstract.php:86
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\DataGenerator\RegexBasedAbstract.php(30): FastRoute\DataGenerator\RegexBasedAbstract->addStaticRoute('GET', Array, Array)
#1 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(44): FastRoute\DataGenerator\RegexBasedAbstract->addRoute('GET', Array, Array)
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(473): FastRoute\RouteCollector->addRoute('GET', '/system-config/...', Array)
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(122): Webman\Route::addRoute('GET', '', Array)
#4 D:\work\hotel\all\itinerary-api\config\route.php(200): Webman\Route::get('', Array)
#5 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#6 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/groups', Object(Closure))
#7 D:\work\hotel\all\itinerary-api\config\route.php(198): Webman\Route::group('/groups', Object(Closure))
#8 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/system-config', Object(Closure))
#10 D:\work\hotel\all\itinerary-api\config\route.php(184): Webman\Route::group('/system-config', Object(Closure))
#11 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#12 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#14 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#15 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#16 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#17 [internal function]: Workerman\Worker->Workerman\{closure}()
#18 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#19 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#20 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#21 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#22 D:\work\hotel\all\itinerary-api\runtime\windows\start_webman.php(33): Workerman\Worker::runAll()
#23 {main}
2025-08-11 16:35:49 pid:1 FastRoute\BadRouteException: Cannot register two routes matching "/system-config/groups" for method "GET" in D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\DataGenerator\RegexBasedAbstract.php:86
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\DataGenerator\RegexBasedAbstract.php(30): FastRoute\DataGenerator\RegexBasedAbstract->addStaticRoute('GET', Array, Array)
#1 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(44): FastRoute\DataGenerator\RegexBasedAbstract->addRoute('GET', Array, Array)
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(473): FastRoute\RouteCollector->addRoute('GET', '/system-config/...', Array)
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(122): Webman\Route::addRoute('GET', '', Array)
#4 D:\work\hotel\all\itinerary-api\config\route.php(200): Webman\Route::get('', Array)
#5 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#6 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/groups', Object(Closure))
#7 D:\work\hotel\all\itinerary-api\config\route.php(198): Webman\Route::group('/groups', Object(Closure))
#8 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/system-config', Object(Closure))
#10 D:\work\hotel\all\itinerary-api\config\route.php(184): Webman\Route::group('/system-config', Object(Closure))
#11 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#12 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#14 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#15 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#16 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#17 [internal function]: Workerman\Worker->Workerman\{closure}()
#18 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#19 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#20 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#21 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#22 D:\work\hotel\all\itinerary-api\runtime\windows\start_monitor.php(33): Workerman\Worker::runAll()
#23 {main}
2025-08-11 16:35:49 pid:1 FastRoute\BadRouteException: Cannot register two routes matching "/system-config/groups" for method "GET" in D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\DataGenerator\RegexBasedAbstract.php:86
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\DataGenerator\RegexBasedAbstract.php(30): FastRoute\DataGenerator\RegexBasedAbstract->addStaticRoute('GET', Array, Array)
#1 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(44): FastRoute\DataGenerator\RegexBasedAbstract->addRoute('GET', Array, Array)
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(473): FastRoute\RouteCollector->addRoute('GET', '/system-config/...', Array)
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(122): Webman\Route::addRoute('GET', '', Array)
#4 D:\work\hotel\all\itinerary-api\config\route.php(200): Webman\Route::get('', Array)
#5 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#6 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/groups', Object(Closure))
#7 D:\work\hotel\all\itinerary-api\config\route.php(198): Webman\Route::group('/groups', Object(Closure))
#8 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/system-config', Object(Closure))
#10 D:\work\hotel\all\itinerary-api\config\route.php(184): Webman\Route::group('/system-config', Object(Closure))
#11 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#12 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#14 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#15 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#16 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#17 [internal function]: Workerman\Worker->Workerman\{closure}()
#18 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#19 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#20 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#21 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#22 D:\work\hotel\all\itinerary-api\runtime\windows\start_plugin.webman.redis-queue.consumer.php(33): Workerman\Worker::runAll()
#23 {main}
2025-08-11 16:35:52 pid:1 FastRoute\BadRouteException: Cannot register two routes matching "/system-config/groups" for method "GET" in D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\DataGenerator\RegexBasedAbstract.php:86
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\DataGenerator\RegexBasedAbstract.php(30): FastRoute\DataGenerator\RegexBasedAbstract->addStaticRoute('GET', Array, Array)
#1 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(44): FastRoute\DataGenerator\RegexBasedAbstract->addRoute('GET', Array, Array)
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(473): FastRoute\RouteCollector->addRoute('GET', '/system-config/...', Array)
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(122): Webman\Route::addRoute('GET', '', Array)
#4 D:\work\hotel\all\itinerary-api\config\route.php(200): Webman\Route::get('', Array)
#5 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#6 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/groups', Object(Closure))
#7 D:\work\hotel\all\itinerary-api\config\route.php(198): Webman\Route::group('/groups', Object(Closure))
#8 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/system-config', Object(Closure))
#10 D:\work\hotel\all\itinerary-api\config\route.php(184): Webman\Route::group('/system-config', Object(Closure))
#11 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#12 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#14 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#15 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#16 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#17 [internal function]: Workerman\Worker->Workerman\{closure}()
#18 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#19 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#20 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#21 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#22 D:\work\hotel\all\itinerary-api\runtime\windows\start_plugin.webman.redis-queue.consumer.php(33): Workerman\Worker::runAll()
#23 {main}
2025-08-11 16:35:52 pid:1 FastRoute\BadRouteException: Cannot register two routes matching "/system-config/groups" for method "GET" in D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\DataGenerator\RegexBasedAbstract.php:86
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\DataGenerator\RegexBasedAbstract.php(30): FastRoute\DataGenerator\RegexBasedAbstract->addStaticRoute('GET', Array, Array)
#1 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(44): FastRoute\DataGenerator\RegexBasedAbstract->addRoute('GET', Array, Array)
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(473): FastRoute\RouteCollector->addRoute('GET', '/system-config/...', Array)
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(122): Webman\Route::addRoute('GET', '', Array)
#4 D:\work\hotel\all\itinerary-api\config\route.php(200): Webman\Route::get('', Array)
#5 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#6 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/groups', Object(Closure))
#7 D:\work\hotel\all\itinerary-api\config\route.php(198): Webman\Route::group('/groups', Object(Closure))
#8 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/system-config', Object(Closure))
#10 D:\work\hotel\all\itinerary-api\config\route.php(184): Webman\Route::group('/system-config', Object(Closure))
#11 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#12 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#14 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#15 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#16 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#17 [internal function]: Workerman\Worker->Workerman\{closure}()
#18 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#19 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#20 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#21 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#22 D:\work\hotel\all\itinerary-api\runtime\windows\start_monitor.php(33): Workerman\Worker::runAll()
#23 {main}
2025-08-11 16:35:52 pid:1 FastRoute\BadRouteException: Cannot register two routes matching "/system-config/groups" for method "GET" in D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\DataGenerator\RegexBasedAbstract.php:86
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\DataGenerator\RegexBasedAbstract.php(30): FastRoute\DataGenerator\RegexBasedAbstract->addStaticRoute('GET', Array, Array)
#1 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(44): FastRoute\DataGenerator\RegexBasedAbstract->addRoute('GET', Array, Array)
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(473): FastRoute\RouteCollector->addRoute('GET', '/system-config/...', Array)
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(122): Webman\Route::addRoute('GET', '', Array)
#4 D:\work\hotel\all\itinerary-api\config\route.php(200): Webman\Route::get('', Array)
#5 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#6 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/groups', Object(Closure))
#7 D:\work\hotel\all\itinerary-api\config\route.php(198): Webman\Route::group('/groups', Object(Closure))
#8 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/system-config', Object(Closure))
#10 D:\work\hotel\all\itinerary-api\config\route.php(184): Webman\Route::group('/system-config', Object(Closure))
#11 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#12 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#14 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#15 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#16 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#17 [internal function]: Workerman\Worker->Workerman\{closure}()
#18 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#19 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#20 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#21 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#22 D:\work\hotel\all\itinerary-api\runtime\windows\start_webman.php(33): Workerman\Worker::runAll()
#23 {main}
2025-08-11 16:35:55 pid:1 FastRoute\BadRouteException: Cannot register two routes matching "/system-config/groups" for method "GET" in D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\DataGenerator\RegexBasedAbstract.php:86
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\DataGenerator\RegexBasedAbstract.php(30): FastRoute\DataGenerator\RegexBasedAbstract->addStaticRoute('GET', Array, Array)
#1 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(44): FastRoute\DataGenerator\RegexBasedAbstract->addRoute('GET', Array, Array)
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(473): FastRoute\RouteCollector->addRoute('GET', '/system-config/...', Array)
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(122): Webman\Route::addRoute('GET', '', Array)
#4 D:\work\hotel\all\itinerary-api\config\route.php(200): Webman\Route::get('', Array)
#5 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#6 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/groups', Object(Closure))
#7 D:\work\hotel\all\itinerary-api\config\route.php(198): Webman\Route::group('/groups', Object(Closure))
#8 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/system-config', Object(Closure))
#10 D:\work\hotel\all\itinerary-api\config\route.php(184): Webman\Route::group('/system-config', Object(Closure))
#11 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#12 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#14 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#15 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#16 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#17 [internal function]: Workerman\Worker->Workerman\{closure}()
#18 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#19 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#20 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#21 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#22 D:\work\hotel\all\itinerary-api\runtime\windows\start_webman.php(33): Workerman\Worker::runAll()
#23 {main}
2025-08-11 16:35:55 pid:1 FastRoute\BadRouteException: Cannot register two routes matching "/system-config/groups" for method "GET" in D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\DataGenerator\RegexBasedAbstract.php:86
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\DataGenerator\RegexBasedAbstract.php(30): FastRoute\DataGenerator\RegexBasedAbstract->addStaticRoute('GET', Array, Array)
#1 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(44): FastRoute\DataGenerator\RegexBasedAbstract->addRoute('GET', Array, Array)
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(473): FastRoute\RouteCollector->addRoute('GET', '/system-config/...', Array)
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(122): Webman\Route::addRoute('GET', '', Array)
#4 D:\work\hotel\all\itinerary-api\config\route.php(200): Webman\Route::get('', Array)
#5 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#6 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/groups', Object(Closure))
#7 D:\work\hotel\all\itinerary-api\config\route.php(198): Webman\Route::group('/groups', Object(Closure))
#8 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/system-config', Object(Closure))
#10 D:\work\hotel\all\itinerary-api\config\route.php(184): Webman\Route::group('/system-config', Object(Closure))
#11 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#12 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#14 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#15 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#16 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#17 [internal function]: Workerman\Worker->Workerman\{closure}()
#18 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#19 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#20 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#21 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#22 D:\work\hotel\all\itinerary-api\runtime\windows\start_plugin.webman.redis-queue.consumer.php(33): Workerman\Worker::runAll()
#23 {main}
2025-08-11 16:35:55 pid:1 FastRoute\BadRouteException: Cannot register two routes matching "/system-config/groups" for method "GET" in D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\DataGenerator\RegexBasedAbstract.php:86
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\DataGenerator\RegexBasedAbstract.php(30): FastRoute\DataGenerator\RegexBasedAbstract->addStaticRoute('GET', Array, Array)
#1 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(44): FastRoute\DataGenerator\RegexBasedAbstract->addRoute('GET', Array, Array)
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(473): FastRoute\RouteCollector->addRoute('GET', '/system-config/...', Array)
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(122): Webman\Route::addRoute('GET', '', Array)
#4 D:\work\hotel\all\itinerary-api\config\route.php(200): Webman\Route::get('', Array)
#5 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#6 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/groups', Object(Closure))
#7 D:\work\hotel\all\itinerary-api\config\route.php(198): Webman\Route::group('/groups', Object(Closure))
#8 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/system-config', Object(Closure))
#10 D:\work\hotel\all\itinerary-api\config\route.php(184): Webman\Route::group('/system-config', Object(Closure))
#11 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#12 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#14 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#15 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#16 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#17 [internal function]: Workerman\Worker->Workerman\{closure}()
#18 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#19 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#20 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#21 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#22 D:\work\hotel\all\itinerary-api\runtime\windows\start_monitor.php(33): Workerman\Worker::runAll()
#23 {main}
2025-08-11 16:35:58 pid:1 FastRoute\BadRouteException: Cannot register two routes matching "/system-config/groups" for method "GET" in D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\DataGenerator\RegexBasedAbstract.php:86
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\DataGenerator\RegexBasedAbstract.php(30): FastRoute\DataGenerator\RegexBasedAbstract->addStaticRoute('GET', Array, Array)
#1 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(44): FastRoute\DataGenerator\RegexBasedAbstract->addRoute('GET', Array, Array)
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(473): FastRoute\RouteCollector->addRoute('GET', '/system-config/...', Array)
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(122): Webman\Route::addRoute('GET', '', Array)
#4 D:\work\hotel\all\itinerary-api\config\route.php(200): Webman\Route::get('', Array)
#5 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#6 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/groups', Object(Closure))
#7 D:\work\hotel\all\itinerary-api\config\route.php(198): Webman\Route::group('/groups', Object(Closure))
#8 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/system-config', Object(Closure))
#10 D:\work\hotel\all\itinerary-api\config\route.php(184): Webman\Route::group('/system-config', Object(Closure))
#11 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#12 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#14 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#15 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#16 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#17 [internal function]: Workerman\Worker->Workerman\{closure}()
#18 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#19 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#20 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#21 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#22 D:\work\hotel\all\itinerary-api\runtime\windows\start_webman.php(33): Workerman\Worker::runAll()
#23 {main}
2025-08-11 16:35:58 pid:1 FastRoute\BadRouteException: Cannot register two routes matching "/system-config/groups" for method "GET" in D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\DataGenerator\RegexBasedAbstract.php:86
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\DataGenerator\RegexBasedAbstract.php(30): FastRoute\DataGenerator\RegexBasedAbstract->addStaticRoute('GET', Array, Array)
#1 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(44): FastRoute\DataGenerator\RegexBasedAbstract->addRoute('GET', Array, Array)
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(473): FastRoute\RouteCollector->addRoute('GET', '/system-config/...', Array)
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(122): Webman\Route::addRoute('GET', '', Array)
#4 D:\work\hotel\all\itinerary-api\config\route.php(200): Webman\Route::get('', Array)
#5 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#6 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/groups', Object(Closure))
#7 D:\work\hotel\all\itinerary-api\config\route.php(198): Webman\Route::group('/groups', Object(Closure))
#8 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/system-config', Object(Closure))
#10 D:\work\hotel\all\itinerary-api\config\route.php(184): Webman\Route::group('/system-config', Object(Closure))
#11 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#12 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#14 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#15 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#16 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#17 [internal function]: Workerman\Worker->Workerman\{closure}()
#18 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#19 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#20 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#21 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#22 D:\work\hotel\all\itinerary-api\runtime\windows\start_monitor.php(33): Workerman\Worker::runAll()
#23 {main}
2025-08-11 16:35:58 pid:1 FastRoute\BadRouteException: Cannot register two routes matching "/system-config/groups" for method "GET" in D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\DataGenerator\RegexBasedAbstract.php:86
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\DataGenerator\RegexBasedAbstract.php(30): FastRoute\DataGenerator\RegexBasedAbstract->addStaticRoute('GET', Array, Array)
#1 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(44): FastRoute\DataGenerator\RegexBasedAbstract->addRoute('GET', Array, Array)
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(473): FastRoute\RouteCollector->addRoute('GET', '/system-config/...', Array)
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(122): Webman\Route::addRoute('GET', '', Array)
#4 D:\work\hotel\all\itinerary-api\config\route.php(200): Webman\Route::get('', Array)
#5 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#6 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/groups', Object(Closure))
#7 D:\work\hotel\all\itinerary-api\config\route.php(198): Webman\Route::group('/groups', Object(Closure))
#8 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(61): Webman\Route::{closure}(Object(FastRoute\RouteCollector))
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(221): FastRoute\RouteCollector->addGroup('/system-config', Object(Closure))
#10 D:\work\hotel\all\itinerary-api\config\route.php(184): Webman\Route::group('/system-config', Object(Closure))
#11 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#12 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#14 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#15 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#16 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#17 [internal function]: Workerman\Worker->Workerman\{closure}()
#18 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#19 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#20 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#21 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#22 D:\work\hotel\all\itinerary-api\runtime\windows\start_plugin.webman.redis-queue.consumer.php(33): Workerman\Worker::runAll()
#23 {main}
2025-08-12 09:29:18 pid:1 FastRoute\BadRouteException: Cannot register two routes matching "/api/itinerary/detail/([^/]+)" for method "GET" in D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\DataGenerator\RegexBasedAbstract.php:111
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\DataGenerator\RegexBasedAbstract.php(32): FastRoute\DataGenerator\RegexBasedAbstract->addVariableRoute('GET', Array, Array)
#1 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(44): FastRoute\DataGenerator\RegexBasedAbstract->addRoute('GET', Array, Array)
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(473): FastRoute\RouteCollector->addRoute('GET', '/api/itinerary/...', Array)
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(122): Webman\Route::addRoute('GET', '/api/itinerary/...', Array)
#4 D:\work\hotel\all\itinerary-api\config\route.php(87): Webman\Route::get('/api/itinerary/...', Array)
#5 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#6 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#7 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#8 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#10 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#11 [internal function]: Workerman\Worker->Workerman\{closure}()
#12 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#14 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#15 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#16 D:\work\hotel\all\itinerary-api\runtime\windows\start_webman.php(33): Workerman\Worker::runAll()
#17 {main}
2025-08-12 09:29:18 pid:1 FastRoute\BadRouteException: Cannot register two routes matching "/api/itinerary/detail/([^/]+)" for method "GET" in D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\DataGenerator\RegexBasedAbstract.php:111
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\DataGenerator\RegexBasedAbstract.php(32): FastRoute\DataGenerator\RegexBasedAbstract->addVariableRoute('GET', Array, Array)
#1 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(44): FastRoute\DataGenerator\RegexBasedAbstract->addRoute('GET', Array, Array)
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(473): FastRoute\RouteCollector->addRoute('GET', '/api/itinerary/...', Array)
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(122): Webman\Route::addRoute('GET', '/api/itinerary/...', Array)
#4 D:\work\hotel\all\itinerary-api\config\route.php(87): Webman\Route::get('/api/itinerary/...', Array)
#5 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#6 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#7 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#8 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#10 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#11 [internal function]: Workerman\Worker->Workerman\{closure}()
#12 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#14 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#15 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#16 D:\work\hotel\all\itinerary-api\runtime\windows\start_plugin.webman.redis-queue.consumer.php(33): Workerman\Worker::runAll()
#17 {main}
2025-08-12 09:29:18 pid:1 FastRoute\BadRouteException: Cannot register two routes matching "/api/itinerary/detail/([^/]+)" for method "GET" in D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\DataGenerator\RegexBasedAbstract.php:111
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\DataGenerator\RegexBasedAbstract.php(32): FastRoute\DataGenerator\RegexBasedAbstract->addVariableRoute('GET', Array, Array)
#1 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(44): FastRoute\DataGenerator\RegexBasedAbstract->addRoute('GET', Array, Array)
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(473): FastRoute\RouteCollector->addRoute('GET', '/api/itinerary/...', Array)
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(122): Webman\Route::addRoute('GET', '/api/itinerary/...', Array)
#4 D:\work\hotel\all\itinerary-api\config\route.php(87): Webman\Route::get('/api/itinerary/...', Array)
#5 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#6 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#7 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#8 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#10 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#11 [internal function]: Workerman\Worker->Workerman\{closure}()
#12 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#14 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#15 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#16 D:\work\hotel\all\itinerary-api\runtime\windows\start_monitor.php(33): Workerman\Worker::runAll()
#17 {main}
2025-08-12 09:29:21 pid:1 FastRoute\BadRouteException: Cannot register two routes matching "/api/itinerary/detail/([^/]+)" for method "GET" in D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\DataGenerator\RegexBasedAbstract.php:111
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\DataGenerator\RegexBasedAbstract.php(32): FastRoute\DataGenerator\RegexBasedAbstract->addVariableRoute('GET', Array, Array)
#1 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(44): FastRoute\DataGenerator\RegexBasedAbstract->addRoute('GET', Array, Array)
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(473): FastRoute\RouteCollector->addRoute('GET', '/api/itinerary/...', Array)
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(122): Webman\Route::addRoute('GET', '/api/itinerary/...', Array)
#4 D:\work\hotel\all\itinerary-api\config\route.php(87): Webman\Route::get('/api/itinerary/...', Array)
#5 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#6 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#7 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#8 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#10 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#11 [internal function]: Workerman\Worker->Workerman\{closure}()
#12 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#14 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#15 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#16 D:\work\hotel\all\itinerary-api\runtime\windows\start_monitor.php(33): Workerman\Worker::runAll()
#17 {main}
2025-08-12 09:29:21 pid:1 FastRoute\BadRouteException: Cannot register two routes matching "/api/itinerary/detail/([^/]+)" for method "GET" in D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\DataGenerator\RegexBasedAbstract.php:111
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\DataGenerator\RegexBasedAbstract.php(32): FastRoute\DataGenerator\RegexBasedAbstract->addVariableRoute('GET', Array, Array)
#1 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(44): FastRoute\DataGenerator\RegexBasedAbstract->addRoute('GET', Array, Array)
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(473): FastRoute\RouteCollector->addRoute('GET', '/api/itinerary/...', Array)
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(122): Webman\Route::addRoute('GET', '/api/itinerary/...', Array)
#4 D:\work\hotel\all\itinerary-api\config\route.php(87): Webman\Route::get('/api/itinerary/...', Array)
#5 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#6 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#7 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#8 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#10 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#11 [internal function]: Workerman\Worker->Workerman\{closure}()
#12 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#14 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#15 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#16 D:\work\hotel\all\itinerary-api\runtime\windows\start_webman.php(33): Workerman\Worker::runAll()
#17 {main}
2025-08-12 09:29:21 pid:1 FastRoute\BadRouteException: Cannot register two routes matching "/api/itinerary/detail/([^/]+)" for method "GET" in D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\DataGenerator\RegexBasedAbstract.php:111
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\DataGenerator\RegexBasedAbstract.php(32): FastRoute\DataGenerator\RegexBasedAbstract->addVariableRoute('GET', Array, Array)
#1 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(44): FastRoute\DataGenerator\RegexBasedAbstract->addRoute('GET', Array, Array)
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(473): FastRoute\RouteCollector->addRoute('GET', '/api/itinerary/...', Array)
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(122): Webman\Route::addRoute('GET', '/api/itinerary/...', Array)
#4 D:\work\hotel\all\itinerary-api\config\route.php(87): Webman\Route::get('/api/itinerary/...', Array)
#5 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#6 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#7 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#8 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#10 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#11 [internal function]: Workerman\Worker->Workerman\{closure}()
#12 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#14 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#15 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#16 D:\work\hotel\all\itinerary-api\runtime\windows\start_plugin.webman.redis-queue.consumer.php(33): Workerman\Worker::runAll()
#17 {main}
2025-08-12 09:29:24 pid:1 FastRoute\BadRouteException: Cannot register two routes matching "/api/itinerary/detail/([^/]+)" for method "GET" in D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\DataGenerator\RegexBasedAbstract.php:111
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\DataGenerator\RegexBasedAbstract.php(32): FastRoute\DataGenerator\RegexBasedAbstract->addVariableRoute('GET', Array, Array)
#1 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(44): FastRoute\DataGenerator\RegexBasedAbstract->addRoute('GET', Array, Array)
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(473): FastRoute\RouteCollector->addRoute('GET', '/api/itinerary/...', Array)
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(122): Webman\Route::addRoute('GET', '/api/itinerary/...', Array)
#4 D:\work\hotel\all\itinerary-api\config\route.php(87): Webman\Route::get('/api/itinerary/...', Array)
#5 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#6 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#7 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#8 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#10 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#11 [internal function]: Workerman\Worker->Workerman\{closure}()
#12 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#14 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#15 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#16 D:\work\hotel\all\itinerary-api\runtime\windows\start_plugin.webman.redis-queue.consumer.php(33): Workerman\Worker::runAll()
#17 {main}
2025-08-12 09:29:24 pid:1 FastRoute\BadRouteException: Cannot register two routes matching "/api/itinerary/detail/([^/]+)" for method "GET" in D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\DataGenerator\RegexBasedAbstract.php:111
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\DataGenerator\RegexBasedAbstract.php(32): FastRoute\DataGenerator\RegexBasedAbstract->addVariableRoute('GET', Array, Array)
#1 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(44): FastRoute\DataGenerator\RegexBasedAbstract->addRoute('GET', Array, Array)
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(473): FastRoute\RouteCollector->addRoute('GET', '/api/itinerary/...', Array)
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(122): Webman\Route::addRoute('GET', '/api/itinerary/...', Array)
#4 D:\work\hotel\all\itinerary-api\config\route.php(87): Webman\Route::get('/api/itinerary/...', Array)
#5 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#6 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#7 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#8 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#10 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#11 [internal function]: Workerman\Worker->Workerman\{closure}()
#12 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#14 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#15 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#16 D:\work\hotel\all\itinerary-api\runtime\windows\start_monitor.php(33): Workerman\Worker::runAll()
#17 {main}
2025-08-12 09:29:24 pid:1 FastRoute\BadRouteException: Cannot register two routes matching "/api/itinerary/detail/([^/]+)" for method "GET" in D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\DataGenerator\RegexBasedAbstract.php:111
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\DataGenerator\RegexBasedAbstract.php(32): FastRoute\DataGenerator\RegexBasedAbstract->addVariableRoute('GET', Array, Array)
#1 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(44): FastRoute\DataGenerator\RegexBasedAbstract->addRoute('GET', Array, Array)
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(473): FastRoute\RouteCollector->addRoute('GET', '/api/itinerary/...', Array)
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(122): Webman\Route::addRoute('GET', '/api/itinerary/...', Array)
#4 D:\work\hotel\all\itinerary-api\config\route.php(87): Webman\Route::get('/api/itinerary/...', Array)
#5 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#6 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#7 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#8 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#10 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#11 [internal function]: Workerman\Worker->Workerman\{closure}()
#12 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#14 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#15 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#16 D:\work\hotel\all\itinerary-api\runtime\windows\start_webman.php(33): Workerman\Worker::runAll()
#17 {main}
2025-08-12 09:29:27 pid:1 FastRoute\BadRouteException: Cannot register two routes matching "/api/itinerary/detail/([^/]+)" for method "GET" in D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\DataGenerator\RegexBasedAbstract.php:111
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\DataGenerator\RegexBasedAbstract.php(32): FastRoute\DataGenerator\RegexBasedAbstract->addVariableRoute('GET', Array, Array)
#1 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(44): FastRoute\DataGenerator\RegexBasedAbstract->addRoute('GET', Array, Array)
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(473): FastRoute\RouteCollector->addRoute('GET', '/api/itinerary/...', Array)
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(122): Webman\Route::addRoute('GET', '/api/itinerary/...', Array)
#4 D:\work\hotel\all\itinerary-api\config\route.php(87): Webman\Route::get('/api/itinerary/...', Array)
#5 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#6 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#7 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#8 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#10 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#11 [internal function]: Workerman\Worker->Workerman\{closure}()
#12 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#14 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#15 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#16 D:\work\hotel\all\itinerary-api\runtime\windows\start_monitor.php(33): Workerman\Worker::runAll()
#17 {main}
2025-08-12 09:29:27 pid:1 FastRoute\BadRouteException: Cannot register two routes matching "/api/itinerary/detail/([^/]+)" for method "GET" in D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\DataGenerator\RegexBasedAbstract.php:111
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\DataGenerator\RegexBasedAbstract.php(32): FastRoute\DataGenerator\RegexBasedAbstract->addVariableRoute('GET', Array, Array)
#1 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(44): FastRoute\DataGenerator\RegexBasedAbstract->addRoute('GET', Array, Array)
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(473): FastRoute\RouteCollector->addRoute('GET', '/api/itinerary/...', Array)
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(122): Webman\Route::addRoute('GET', '/api/itinerary/...', Array)
#4 D:\work\hotel\all\itinerary-api\config\route.php(87): Webman\Route::get('/api/itinerary/...', Array)
#5 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#6 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#7 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#8 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#10 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#11 [internal function]: Workerman\Worker->Workerman\{closure}()
#12 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#14 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#15 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#16 D:\work\hotel\all\itinerary-api\runtime\windows\start_plugin.webman.redis-queue.consumer.php(33): Workerman\Worker::runAll()
#17 {main}
2025-08-12 09:29:27 pid:1 FastRoute\BadRouteException: Cannot register two routes matching "/api/itinerary/detail/([^/]+)" for method "GET" in D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\DataGenerator\RegexBasedAbstract.php:111
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\DataGenerator\RegexBasedAbstract.php(32): FastRoute\DataGenerator\RegexBasedAbstract->addVariableRoute('GET', Array, Array)
#1 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(44): FastRoute\DataGenerator\RegexBasedAbstract->addRoute('GET', Array, Array)
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(473): FastRoute\RouteCollector->addRoute('GET', '/api/itinerary/...', Array)
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(122): Webman\Route::addRoute('GET', '/api/itinerary/...', Array)
#4 D:\work\hotel\all\itinerary-api\config\route.php(87): Webman\Route::get('/api/itinerary/...', Array)
#5 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#6 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#7 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#8 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#10 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#11 [internal function]: Workerman\Worker->Workerman\{closure}()
#12 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#14 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#15 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#16 D:\work\hotel\all\itinerary-api\runtime\windows\start_webman.php(33): Workerman\Worker::runAll()
#17 {main}
2025-08-12 09:29:30 pid:1 FastRoute\BadRouteException: Cannot register two routes matching "/api/itinerary/detail/([^/]+)" for method "GET" in D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\DataGenerator\RegexBasedAbstract.php:111
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\DataGenerator\RegexBasedAbstract.php(32): FastRoute\DataGenerator\RegexBasedAbstract->addVariableRoute('GET', Array, Array)
#1 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(44): FastRoute\DataGenerator\RegexBasedAbstract->addRoute('GET', Array, Array)
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(473): FastRoute\RouteCollector->addRoute('GET', '/api/itinerary/...', Array)
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(122): Webman\Route::addRoute('GET', '/api/itinerary/...', Array)
#4 D:\work\hotel\all\itinerary-api\config\route.php(87): Webman\Route::get('/api/itinerary/...', Array)
#5 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#6 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#7 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#8 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#10 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#11 [internal function]: Workerman\Worker->Workerman\{closure}()
#12 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#14 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#15 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#16 D:\work\hotel\all\itinerary-api\runtime\windows\start_plugin.webman.redis-queue.consumer.php(33): Workerman\Worker::runAll()
#17 {main}
2025-08-12 09:29:30 pid:1 FastRoute\BadRouteException: Cannot register two routes matching "/api/itinerary/detail/([^/]+)" for method "GET" in D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\DataGenerator\RegexBasedAbstract.php:111
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\DataGenerator\RegexBasedAbstract.php(32): FastRoute\DataGenerator\RegexBasedAbstract->addVariableRoute('GET', Array, Array)
#1 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(44): FastRoute\DataGenerator\RegexBasedAbstract->addRoute('GET', Array, Array)
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(473): FastRoute\RouteCollector->addRoute('GET', '/api/itinerary/...', Array)
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(122): Webman\Route::addRoute('GET', '/api/itinerary/...', Array)
#4 D:\work\hotel\all\itinerary-api\config\route.php(87): Webman\Route::get('/api/itinerary/...', Array)
#5 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#6 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#7 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#8 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#10 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#11 [internal function]: Workerman\Worker->Workerman\{closure}()
#12 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#14 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#15 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#16 D:\work\hotel\all\itinerary-api\runtime\windows\start_monitor.php(33): Workerman\Worker::runAll()
#17 {main}
2025-08-12 09:29:30 pid:1 FastRoute\BadRouteException: Cannot register two routes matching "/api/itinerary/detail/([^/]+)" for method "GET" in D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\DataGenerator\RegexBasedAbstract.php:111
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\DataGenerator\RegexBasedAbstract.php(32): FastRoute\DataGenerator\RegexBasedAbstract->addVariableRoute('GET', Array, Array)
#1 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(44): FastRoute\DataGenerator\RegexBasedAbstract->addRoute('GET', Array, Array)
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(473): FastRoute\RouteCollector->addRoute('GET', '/api/itinerary/...', Array)
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(122): Webman\Route::addRoute('GET', '/api/itinerary/...', Array)
#4 D:\work\hotel\all\itinerary-api\config\route.php(87): Webman\Route::get('/api/itinerary/...', Array)
#5 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#6 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#7 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#8 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#10 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#11 [internal function]: Workerman\Worker->Workerman\{closure}()
#12 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#14 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#15 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#16 D:\work\hotel\all\itinerary-api\runtime\windows\start_webman.php(33): Workerman\Worker::runAll()
#17 {main}
2025-08-12 09:29:33 pid:1 FastRoute\BadRouteException: Cannot register two routes matching "/api/itinerary/detail/([^/]+)" for method "GET" in D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\DataGenerator\RegexBasedAbstract.php:111
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\DataGenerator\RegexBasedAbstract.php(32): FastRoute\DataGenerator\RegexBasedAbstract->addVariableRoute('GET', Array, Array)
#1 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(44): FastRoute\DataGenerator\RegexBasedAbstract->addRoute('GET', Array, Array)
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(473): FastRoute\RouteCollector->addRoute('GET', '/api/itinerary/...', Array)
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(122): Webman\Route::addRoute('GET', '/api/itinerary/...', Array)
#4 D:\work\hotel\all\itinerary-api\config\route.php(87): Webman\Route::get('/api/itinerary/...', Array)
#5 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#6 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#7 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#8 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#10 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#11 [internal function]: Workerman\Worker->Workerman\{closure}()
#12 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#14 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#15 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#16 D:\work\hotel\all\itinerary-api\runtime\windows\start_webman.php(33): Workerman\Worker::runAll()
#17 {main}
2025-08-12 09:29:33 pid:1 FastRoute\BadRouteException: Cannot register two routes matching "/api/itinerary/detail/([^/]+)" for method "GET" in D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\DataGenerator\RegexBasedAbstract.php:111
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\DataGenerator\RegexBasedAbstract.php(32): FastRoute\DataGenerator\RegexBasedAbstract->addVariableRoute('GET', Array, Array)
#1 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(44): FastRoute\DataGenerator\RegexBasedAbstract->addRoute('GET', Array, Array)
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(473): FastRoute\RouteCollector->addRoute('GET', '/api/itinerary/...', Array)
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(122): Webman\Route::addRoute('GET', '/api/itinerary/...', Array)
#4 D:\work\hotel\all\itinerary-api\config\route.php(87): Webman\Route::get('/api/itinerary/...', Array)
#5 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#6 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#7 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#8 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#10 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#11 [internal function]: Workerman\Worker->Workerman\{closure}()
#12 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#14 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#15 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#16 D:\work\hotel\all\itinerary-api\runtime\windows\start_monitor.php(33): Workerman\Worker::runAll()
#17 {main}
2025-08-12 09:29:33 pid:1 FastRoute\BadRouteException: Cannot register two routes matching "/api/itinerary/detail/([^/]+)" for method "GET" in D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\DataGenerator\RegexBasedAbstract.php:111
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\DataGenerator\RegexBasedAbstract.php(32): FastRoute\DataGenerator\RegexBasedAbstract->addVariableRoute('GET', Array, Array)
#1 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(44): FastRoute\DataGenerator\RegexBasedAbstract->addRoute('GET', Array, Array)
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(473): FastRoute\RouteCollector->addRoute('GET', '/api/itinerary/...', Array)
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(122): Webman\Route::addRoute('GET', '/api/itinerary/...', Array)
#4 D:\work\hotel\all\itinerary-api\config\route.php(87): Webman\Route::get('/api/itinerary/...', Array)
#5 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#6 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#7 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#8 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#10 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#11 [internal function]: Workerman\Worker->Workerman\{closure}()
#12 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#14 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#15 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#16 D:\work\hotel\all\itinerary-api\runtime\windows\start_plugin.webman.redis-queue.consumer.php(33): Workerman\Worker::runAll()
#17 {main}
2025-08-12 09:29:36 pid:1 FastRoute\BadRouteException: Cannot register two routes matching "/api/itinerary/detail/([^/]+)" for method "GET" in D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\DataGenerator\RegexBasedAbstract.php:111
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\DataGenerator\RegexBasedAbstract.php(32): FastRoute\DataGenerator\RegexBasedAbstract->addVariableRoute('GET', Array, Array)
#1 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(44): FastRoute\DataGenerator\RegexBasedAbstract->addRoute('GET', Array, Array)
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(473): FastRoute\RouteCollector->addRoute('GET', '/api/itinerary/...', Array)
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(122): Webman\Route::addRoute('GET', '/api/itinerary/...', Array)
#4 D:\work\hotel\all\itinerary-api\config\route.php(87): Webman\Route::get('/api/itinerary/...', Array)
#5 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#6 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#7 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#8 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#10 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#11 [internal function]: Workerman\Worker->Workerman\{closure}()
#12 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#14 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#15 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#16 D:\work\hotel\all\itinerary-api\runtime\windows\start_monitor.php(33): Workerman\Worker::runAll()
#17 {main}
2025-08-12 09:29:36 pid:1 FastRoute\BadRouteException: Cannot register two routes matching "/api/itinerary/detail/([^/]+)" for method "GET" in D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\DataGenerator\RegexBasedAbstract.php:111
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\DataGenerator\RegexBasedAbstract.php(32): FastRoute\DataGenerator\RegexBasedAbstract->addVariableRoute('GET', Array, Array)
#1 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(44): FastRoute\DataGenerator\RegexBasedAbstract->addRoute('GET', Array, Array)
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(473): FastRoute\RouteCollector->addRoute('GET', '/api/itinerary/...', Array)
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(122): Webman\Route::addRoute('GET', '/api/itinerary/...', Array)
#4 D:\work\hotel\all\itinerary-api\config\route.php(87): Webman\Route::get('/api/itinerary/...', Array)
#5 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#6 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#7 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#8 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#10 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#11 [internal function]: Workerman\Worker->Workerman\{closure}()
#12 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#14 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#15 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#16 D:\work\hotel\all\itinerary-api\runtime\windows\start_webman.php(33): Workerman\Worker::runAll()
#17 {main}
2025-08-12 09:29:36 pid:1 FastRoute\BadRouteException: Cannot register two routes matching "/api/itinerary/detail/([^/]+)" for method "GET" in D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\DataGenerator\RegexBasedAbstract.php:111
Stack trace:
#0 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\DataGenerator\RegexBasedAbstract.php(32): FastRoute\DataGenerator\RegexBasedAbstract->addVariableRoute('GET', Array, Array)
#1 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\RouteCollector.php(44): FastRoute\DataGenerator\RegexBasedAbstract->addRoute('GET', Array, Array)
#2 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(473): FastRoute\RouteCollector->addRoute('GET', '/api/itinerary/...', Array)
#3 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(122): Webman\Route::addRoute('GET', '/api/itinerary/...', Array)
#4 D:\work\hotel\all\itinerary-api\config\route.php(87): Webman\Route::get('/api/itinerary/...', Array)
#5 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(496): require_once('D:\\work\\hotel\\a...')
#6 D:\work\hotel\all\itinerary-api\vendor\nikic\fast-route\src\functions.php(25): Webman\Route::Webman\{closure}(Object(FastRoute\RouteCollector))
#7 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\Route.php(491): FastRoute\simpleDispatcher(Object(Closure))
#8 D:\work\hotel\all\itinerary-api\support\bootstrap.php(138): Webman\Route::load(Array)
#9 D:\work\hotel\all\itinerary-api\vendor\workerman\webman-framework\src\support\helpers.php(550): require_once('D:\\work\\hotel\\a...')
#10 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#11 [internal function]: Workerman\Worker->Workerman\{closure}()
#12 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#13 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#14 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#15 D:\work\hotel\all\itinerary-api\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#16 D:\work\hotel\all\itinerary-api\runtime\windows\start_plugin.webman.redis-queue.consumer.php(33): Workerman\Worker::runAll()
#17 {main}
