<?php
/**
 * This file is part of webman.
 *
 * Licensed under The MIT License
 * For full copyright and license information, please see the MIT-LICENSE.txt
 * Redistributions of files must retain the above copyright notice.
 *
 * <AUTHOR>
 * @copyright walkor<<EMAIL>>
 * @link      http://www.workerman.net/
 * @license   http://www.opensource.org/licenses/mit-license.php MIT License
 */

use support\Request;

return [
    'debug'             => env('APP_DEBUG', false),
    'error_reporting'   => E_ALL,
    'default_timezone'  => 'Asia/Shanghai',
    'request_class'     => Request::class,
    'public_path'       => base_path() . DIRECTORY_SEPARATOR . 'public',
    'runtime_path'      => base_path(false) . DIRECTORY_SEPARATOR . 'runtime',
    'controller_suffix' => '',
    'controller_reuse'  => false,
    
    // JWT configuration
    'jwt_secret'        => env('JWT_SECRET', 'your-secret-key'),
    'jwt_ttl'           => env('JWT_TTL', 86400), // Token expiration time in seconds (1 day)
    'jwt_refresh_ttl'   => env('JWT_REFRESH_TTL', 604800), // Refresh token expiration time (7 days)
    
    // API base URL for callbacks
    'base_url'          => env('APP_URL', 'http://localhost:8787'),
];
