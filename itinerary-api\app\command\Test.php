<?php

namespace app\command;

use app\model\ItineraryThemeModel;
use app\model\LiveRoomModel;
use support\Cache;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use app\model\CityInfoModel;
use app\model\ItineraryModel;
use app\model\ProductCategoryModel;
use think\db\Query;
use think\facade\Db;

#[AsCommand('test', '测试脚本')]
class Test extends Command
{
    /**
     * @return void
     */
    protected function configure()
    {

    }


    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $model = new LiveRoomModel();
        $liveRoom = $model->where('id', 1)
            ->where('delete_time', 0)
            ->find();
        var_dump($liveRoom);
        return self::SUCCESS;
    }
}
