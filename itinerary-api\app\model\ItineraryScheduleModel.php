<?php

declare(strict_types=1);

namespace app\model;

use base\base\BaseModel;

class ItineraryScheduleModel extends BaseModel
{
    /**
     * 设置表名
     * @var string
     */
    protected $name = 'itinerary_schedule';

    /**
     * 设置主键
     * @var string
     */
    protected $pk = 'id';

    /**
     * 允许写入的字段
     * @var array
     */
    protected $field = [
        'id',
        'itinerary_id',
        'day',
        'title',
        'description',
        'activities',
        'meals',
        'accommodation',
        'images',
        'type',
        'meeting_point',
        'meeting_time',
        'start_time',
        'meal_type',
        'adult_meal_included',
        'child_meal_included',
        'meal_duration_hours',
        'meal_duration_minutes',
        'meal_price',
        'transport_type',
        'flight_number',
        'departure_city',
        'arrival_city',
        'estimated_duration_hours',
        'estimated_duration_minutes',
        'stay_type',
        'hotel_name',
        'activity_duration_hours',
        'activity_duration_minutes',
        'remark',
        'create_time',
        'update_time',
        'delete_time'
    ];

    /**
     * 类型转换
     * @var array
     */
    protected $type = [
        'id' => 'integer',
        'day' => 'integer',
        'activities' => 'json',
        'meals' => 'json',
        'images' => 'json',
        'meal_type' => 'json',
        'meal_duration_hours' => 'integer',
        'meal_duration_minutes' => 'integer',
        'meal_price' => 'float',
        'estimated_duration_hours' => 'integer',
        'estimated_duration_minutes' => 'integer',
        'activity_duration_hours' => 'integer',
        'activity_duration_minutes' => 'integer',
        'create_time' => 'integer',
        'update_time' => 'integer',
        'delete_time' => 'integer'
    ];

    /**
     * 获取行程安排列表
     * @param string|int $itineraryId 行程ID
     * @return array
     */
    public function getSchedulesByItineraryId($itineraryId): array
    {
        return $this->where('itinerary_id', $itineraryId)
            ->order('day', 'asc')
            ->select()
            ->toArray();
    }

    /**
     * 获取行程安排详情
     * @param int $id 行程安排ID
     * @return array|null
     */
    public function getDetail(int $id): ?array
    {
        $data = $this->where('id', $id)->find();
        return $data ? $data->toArray() : null;
    }

    /**
     * 批量添加行程安排
     * @param array $schedules 行程安排数据
     * @return bool
     */
    public function batchSave(array $schedules): bool
    {
        return $this->saveAll($schedules) ? true : false;
    }
} 