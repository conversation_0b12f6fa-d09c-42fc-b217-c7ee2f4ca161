<?php

declare(strict_types=1);

namespace app\service;

use app\model\ItineraryInventoryModel;
use app\model\ItineraryModel;
use app\model\ItineraryOrderModel;
use app\model\PaymentModel;
use app\model\ReservationModel;
use base\exception\BaseException;
use support\Request;

class BookingService
{
    /**
     * 创建订单
     *
     * @param string $itineraryId 行程ID
     * @param int $userId 用户ID
     * @return array 订单信息和支付信息
     * @throws BaseException
     */
    public function createOrder(string $itineraryId, int $userId): array
    {
        // 1. 获取行程信息
        $itinerary = ItineraryModel::mk()->where('id', $itineraryId)->where('status', 1)->find();
        if (!$itinerary) {
            throw new BaseException('行程不存在或已下线', 400);
        }

        // 2. 创建订单
        $orderModel = new ItineraryOrderModel();
        $orderData = [
            'itinerary_id' => $itineraryId,
            'user_id' => $userId,
            'base_price' => $itinerary->price,
            'total_amount' => $itinerary->price, // 初始总金额为基础价格
            'status' => 'pending'
        ];

        $orderId = $orderModel->createOrder($orderData);

        // 3. 创建支付记录
        $paymentModel = new PaymentModel();
        $paymentData = [
            'order_id' => $orderId,
            'user_id' => $userId,
            'amount' => $itinerary->price,
            'payment_type' => PaymentModel::TYPE_ORDER,
            'payment_method' => PaymentModel::METHOD_ALIPAY
        ];

        $paymentId = $paymentModel->createPayment($paymentData);
        $payment = $paymentModel->getDetail($paymentId);

        // 4. 生成支付宝支付链接
        $alipayService = new AlipayService();
        $subject = "行程预订：{$itinerary->title}";
        $returnUrl = config('app.base_url') . '/api/payment/alipay/return';
        $notifyUrl = config('app.base_url') . '/api/payment/alipay/notify';

        $payUrl = $alipayService->pagePay(
            $payment['payment_no'],
            $payment['amount'],
            $subject,
            $returnUrl,
            $notifyUrl
        );

        return [
            'order_id' => $orderId,
            'payment_id' => $paymentId,
            'payment_no' => $payment['payment_no'],
            'amount' => $payment['amount'],
            'payment_url' => $payUrl,
            'itinerary' => $itinerary->toArray()
        ];
    }

    /**
     * 订单支付
     * @param Request $request
     * @return array
     */
    public function payOrder(Request $request): array
    {
        $params = $request->all();
        if (!isset($params['order_id']) || !isset($params['pay_type']) || !in_array($params['pay_type'], ['wechat', 'alipay'])) {
            throw new BaseException('参数错误', 400);
        }
    }

    /**
     * 创建预约
     *
     * @param int $orderId 订单ID
     * @param array $data 预约信息
     * @return array 预约结果
     * @throws BaseException
     */
    public function createReservation(int $orderId, array $data): array
    {
        // 1. 检查订单状态
        $orderModel = new ItineraryOrderModel();
        $order = $orderModel->getDetail((string)$orderId);

        if (!$order) {
            throw new BaseException('订单不存在', 400);
        }

        if ($order['status'] !== 'paid') {
            throw new BaseException('订单未支付，不能预约', 400);
        }

        // 2. 检查是否已有预约
        $reservationModel = new ReservationModel();
        $existingReservation = $reservationModel->getByOrderId((string)$orderId);

        if ($existingReservation) {
            throw new BaseException('订单已经预约过了', 400);
        }

        // 3. 检查行程日期库存
        $itineraryId = $order['itinerary_id'];
        $travelDate = $data['travel_date'];
        $adultCount = $data['adult_count'] ?? 1;
        $childCount = $data['child_count'] ?? 0;
        $occupancies = $data['occupancies'] ?? 0;

        $inventoryModel = new ItineraryInventoryModel();
        $inventory = $inventoryModel->getDateInventory($itineraryId, $travelDate);

        if ($inventory['status'] != 1) {
            throw new BaseException('所选日期行程不可预订', 400);
        }

        if ($inventory['available'] !== '不限' && $inventory['available'] < ($adultCount + $childCount)) {
            throw new BaseException('所选日期行程库存不足', 400);
        }

        // 4. 计算是否需要加价
        $surchargeRequired = false;
        $surchargeAmount = 0;

        // 旺季加价
        if ($inventory['hasAdditionalPrice'] && $inventory['hotDateSurcharge']) {
            $surchargeRequired = true;
            $surchargeAmount += $inventory['hotDateSurcharge'];
        }

        // 人数加价
        if ($inventory['hasAdditionalPrice'] && ($inventory['adultSurcharge'] || $inventory['childSurcharge'])) {
            $surchargeRequired = true;
            if ($inventory['adultSurcharge']) {
                $surchargeAmount += ($inventory['adultSurcharge'] * $adultCount);
            }
            if ($inventory['childSurcharge']) {
                $surchargeAmount += ($inventory['childSurcharge'] * $childCount);
            }
        }

        // 5. 创建预约
        $reservationData = [
            'order_id' => $orderId,
            'user_id' => $order['user_id'],
            'name' => $data['name'],
            'phone' => $data['phone'],
            'travel_date' => $travelDate,
            'adult_count' => $adultCount,
            'child_count' => $childCount,
            'surcharge_required' => $surchargeRequired,
            'surcharge_amount' => $surchargeAmount,
            'surcharge_paid' => !$surchargeRequired,
            'occupancies' => $occupancies
        ];

        $reservationId = $reservationModel->createReservation($reservationData);

        // 6. 如果需要加价，创建加价支付记录
        // @todo 暂不支付
        $paymentInfo = null;
//        if ($surchargeRequired && $surchargeAmount > 0) {
//            $paymentModel = new PaymentModel();
//            $paymentData = [
//                'order_id' => $orderId,
//                'user_id' => $order['user_id'],
//                'amount' => $surchargeAmount,
//                'payment_type' => PaymentModel::TYPE_SURCHARGE,
//                'payment_method' => PaymentModel::METHOD_ALIPAY
//            ];
//
//            $paymentId = $paymentModel->createPayment($paymentData);
//            $payment = $paymentModel->getDetail($paymentId);
//
//            // 生成支付宝支付链接
//            $alipayService = new AlipayService();
//            $subject = "行程加价支付：{$order['itinerary']['title']}";
//            $returnUrl = config('app.base_url') . '/api/payment/alipay/return';
//            $notifyUrl = config('app.base_url') . '/api/payment/alipay/notify';
//
//            $payUrl = $alipayService->pagePay(
//                $payment['payment_no'],
//                $payment['amount'],
//                $subject,
//                $returnUrl,
//                $notifyUrl
//            );
//
//            $paymentInfo = [
//                'payment_id' => $paymentId,
//                'payment_no' => $payment['payment_no'],
//                'amount' => $payment['amount'],
//                'payment_url' => $payUrl
//            ];
//        }

        // 7. 更新库存已预订数量
        if ($inventory['available'] !== '不限') {
            $this->updateInventoryBookedAmount($itineraryId, $travelDate, $adultCount + $childCount);
        }

        return [
            'reservation_id' => $reservationId,
            'name' => $data['name'],
            'phone' => $data['phone'],
            'travel_date' => $travelDate,
            'adult_count' => $adultCount,
            'child_count' => $childCount,
            'surcharge_required' => $surchargeRequired,
            'surcharge_amount' => $surchargeAmount,
            'payment_info' => $paymentInfo
        ];
    }

    /**
     * 取消预约
     *
     * @param int $reservationId 预约ID
     * @return array 取消结果
     * @throws BaseException
     */
    public function cancelReservation(int $reservationId): array
    {
        // 1. 获取预约信息
        $reservationModel = new ReservationModel();
        $reservation = $reservationModel->getDetail((string)$reservationId);

        if (!$reservation) {
            throw new BaseException('预约不存在', 400);
        }

        if ($reservation['status'] === ReservationModel::STATUS_CANCELLED) {
            throw new BaseException('预约已经取消过了', 400);
        }

        // 2. 获取订单信息
        $orderModel = new ItineraryOrderModel();
        $order = $orderModel->getDetail($reservation['order_id']);

        if (!$order) {
            throw new BaseException('订单不存在', 400);
        }

        // 3. 取消预约
        $result = $reservationModel->cancelReservation((string)$reservationId);

        if (!$result) {
            throw new BaseException('取消预约失败', 500);
        }

        // 4. 处理退款（如果有加价支付）
        $refundInfo = null;
        if ($reservation['surcharge_required'] && $reservation['surcharge_paid'] && $reservation['surcharge_amount'] > 0) {
            // 获取加价支付记录
            $paymentModel = new PaymentModel();
            $payment = $paymentModel->getByOrderId($reservation['order_id'], PaymentModel::TYPE_SURCHARGE);

            if ($payment && $payment['status'] === PaymentModel::STATUS_PAID) {
                // 执行退款
                $alipayService = new AlipayService();
                $refundRequestNo = 'REF' . date('YmdHis') . mt_rand(1000, 9999);

                try {
                    $refundResult = $alipayService->refund(
                        $payment['payment_no'],
                        (string)$payment['amount'],
                        $refundRequestNo
                    );

                    // 更新支付状态为已退款
                    $paymentModel->updateStatus($payment['id'], PaymentModel::STATUS_REFUNDED, [
                        'refund_no' => $refundRequestNo
                    ]);

                    $refundInfo = [
                        'refund_amount' => $payment['amount'],
                        'refund_no' => $refundRequestNo,
                        'refund_time' => date('Y-m-d H:i:s')
                    ];
                } catch (BaseException $e) {
                    // 退款失败，记录日志但不阻止取消预约
                    // 实际项目中需要加入重试机制或人工处理
                }
            }
        }

        // 5. 恢复库存
        $this->updateInventoryBookedAmount(
            $order['itinerary_id'],
            $reservation['travel_date'],
            -($reservation['adult_count'] + $reservation['child_count'])
        );

        return [
            'reservation_id' => $reservationId,
            'cancelled' => true,
            'refund_info' => $refundInfo
        ];
    }

    /**
     * 支付加价订单
     *
     * @param string $paymentId 支付ID
     * @return array 支付结果
     * @throws BaseException
     */
    public function paySurcharge(string $paymentId): array
    {
        // 1. 获取支付记录
        $paymentModel = new PaymentModel();
        $payment = $paymentModel->getDetail($paymentId);

        if (!$payment) {
            throw new BaseException('支付记录不存在', 400);
        }

        if ($payment['payment_type'] !== PaymentModel::TYPE_SURCHARGE) {
            throw new BaseException('不是加价支付记录', 400);
        }

        if ($payment['status'] !== PaymentModel::STATUS_PENDING) {
            throw new BaseException('该支付已处理', 400);
        }

        // 2. 获取订单和预约信息
        $orderModel = new ItineraryOrderModel();
        $order = $orderModel->getDetail($payment['order_id']);

        if (!$order) {
            throw new BaseException('订单不存在', 400);
        }

        $reservationModel = new ReservationModel();
        $reservation = $reservationModel->getByOrderId($payment['order_id']);

        if (!$reservation) {
            throw new BaseException('预约不存在', 400);
        }

        // 3. 生成支付宝支付链接
        $alipayService = new AlipayService();
        $subject = "行程加价支付：{$order['itinerary']['title']}";
        $returnUrl = config('app.base_url') . '/api/payment/alipay/return';
        $notifyUrl = config('app.base_url') . '/api/payment/alipay/notify';

        $payUrl = $alipayService->pagePay(
            $payment['payment_no'],
            $payment['amount'],
            $subject,
            $returnUrl,
            $notifyUrl
        );

        return [
            'payment_id' => $paymentId,
            'payment_no' => $payment['payment_no'],
            'amount' => $payment['amount'],
            'payment_url' => $payUrl
        ];
    }

    /**
     * 处理支付回调
     *
     * @param array $params 回调参数
     * @return bool 处理结果
     * @throws BaseException
     */
    public function handlePaymentNotify(array $params): bool
    {
        if (!isset($params['out_trade_no']) || !isset($params['trade_no']) || !isset($params['success'])) {
            throw new BaseException('回调参数不完整', 400);
        }

        if (!$params['success']) {
            return false;
        }

        // 获取支付记录
        $paymentNo = $params['out_trade_no'];
        $paymentModel = new PaymentModel();
        $payment = $paymentModel->where('payment_no', $paymentNo)
            ->where('status', PaymentModel::STATUS_PENDING)
            ->where('delete_time', 0)
            ->find();

        if (!$payment) {
            return false;
        }

        // 更新支付状态
        $paymentModel->updateStatus($payment['id'], PaymentModel::STATUS_PAID, [
            'third_party_no' => $params['trade_no'],
            'payment_time' => time()
        ]);

        // 处理不同类型的支付
        if ($payment['payment_type'] === PaymentModel::TYPE_ORDER) {
            // 订单支付成功，更新订单状态
            $orderModel = new ItineraryOrderModel();
            $orderModel->updateStatus($payment['order_id'], 'paid');
        } else if ($payment['payment_type'] === PaymentModel::TYPE_SURCHARGE) {
            // 加价支付成功，更新预约加价支付状态
            $reservationModel = new ReservationModel();
            $reservation = $reservationModel->getByOrderId($payment['order_id']);

            if ($reservation) {
                $reservationModel->markSurchargePaid($reservation['id']);
                $reservationModel->updateStatus($reservation['id'], ReservationModel::STATUS_CONFIRMED);
            }
        }

        return true;
    }

    /**
     * 更新库存已预订数量
     *
     * @param string $itineraryId 行程ID
     * @param string $date 日期
     * @param int $changeAmount 变动数量
     * @return bool 更新结果
     */
    private function updateInventoryBookedAmount(string $itineraryId, string $date, int $changeAmount): bool
    {
        if ($changeAmount == 0) {
            return true;
        }

        $inventory = ItineraryInventoryModel::where('itinerary_id', $itineraryId)
            ->where('inventory_date', $date)
            ->where('delete_time', 0)
            ->find();

        if (!$inventory) {
            return false;
        }

        // 如果是减少数量，确保不为负数
        $newAmount = $inventory['booked_amount'] + $changeAmount;
        if ($newAmount < 0) {
            $newAmount = 0;
        }

        return $inventory->save([
            'booked_amount' => $newAmount,
            'update_time' => time()
        ]) > 0;
    }
}
