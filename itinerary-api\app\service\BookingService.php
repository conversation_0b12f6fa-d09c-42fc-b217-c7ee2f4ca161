<?php

declare(strict_types=1);

namespace app\service;

use app\model\ItineraryInventoryModel;
use app\model\ItineraryModel;
use app\model\ItineraryOrderModel;
use app\model\PaymentModel;
use app\model\ReservationModel;
use app\model\User;
use app\model\UserAuthModel;
use base\exception\BaseException;

class BookingService
{
    /**
     * 创建订单
     *
     * @param string $itineraryId 行程ID
     * @param int $userId 用户ID
     * @return array 订单信息和支付信息
     * @throws BaseException
     */
    public function createOrder(string $itineraryId, int $userId, $num): array
    {
        // 1. 获取行程信息
        $itinerary = ItineraryModel::mk()->where('id', $itineraryId)->where('status', ItineraryModel::ONLINE_STATUS)->find();
        if (!$itinerary) {
            throw new BaseException('行程不存在或已下线', 400);
        }

        // 2. 创建订单
        $orderModel = new ItineraryOrderModel();
        $orderData = [
            'itinerary_id' => $itineraryId,
            'user_id' => $userId,
            'base_price' => $itinerary->price,
            'num' => $num,
            'total_amount' => bcmul((string)$itinerary->price, (string)$num, 2), // 初始总金额为基础价格
            'status' => 'pending',
            'order_type' => ItineraryOrderModel::ORDER_TYPE_RESERV
        ];

        $orderId = $orderModel->createOrder($orderData);

        return [
            'order_id' => $orderId,
            'itinerary' => $itinerary->toArray()
        ];
    }

    /**
     * 创建预约
     *
     * @param int $orderId 订单ID
     * @param array $data 预约信息
     * @return array 预约结果
     * @throws BaseException
     */
    public function createReservation(int $orderId, array $data): array
    {
        // 1. 检查订单状态
        $orderModel = new ItineraryOrderModel();
        $order = $orderModel->getDetail((string)$orderId);

        if (!$order) {
            throw new BaseException('订单不存在', 400);
        }

        if ($order['status'] !== 'paid') {
            throw new BaseException('订单未支付，不能预约', 400);
        }

        // 2. 检查是否已有预约
        $reservationModel = new ReservationModel();
        $existingReservation = $reservationModel->getByOrderId((string)$orderId);

        if ($existingReservation && $existingReservation['status'] != 'cancelled') {
            throw new BaseException('订单已经预约过了', 400);
        }

        // 3. 检查行程日期库存
        $itineraryId = $order['itineraryId'];
        $travelDate = $data['travel_date'];
        $adultCount = $data['adult_count'] ?? 1;
        $childCount = $data['child_count'] ?? 0;
        $occupancies = $data['occupancies'] ?? 0;

        $inventoryModel = new ItineraryInventoryModel();
        $inventory = $inventoryModel->getDateInventory($itineraryId, $travelDate);

        if ($inventory['status'] != 1) {
            throw new BaseException('所选日期行程不可预订', 400);
        }

        if ($inventory['available'] !== '不限' && $inventory['available'] < ($adultCount + $childCount)) {
            throw new BaseException('所选日期行程库存不足', 400);
        }

        // 4. 计算是否需要加价
        $surchargeRequired = false;
        $surchargeAmount = 0;

        // 旺季加价
        if ($inventory['hasAdditionalPrice'] && $inventory['hotDateSurcharge']) {
            $surchargeRequired = true;
            $surchargeAmount += $inventory['hotDateSurcharge'];
        }

        // 人数加价
        if ($inventory['hasAdditionalPrice'] && ($inventory['adultSurcharge'] || $inventory['childSurcharge'])) {
            $surchargeRequired = true;
            if ($inventory['adultSurcharge']) {
                $surchargeAmount += ($inventory['adultSurcharge'] * $adultCount);
            }
            if ($inventory['childSurcharge']) {
                $surchargeAmount += ($inventory['childSurcharge'] * $childCount);
            }
        }

        // 5. 创建预约
        $reservationData = [
            'order_id' => $orderId,
            'user_id' => $order['userId'],
            'name' => $data['name'],
            'phone' => $data['phone'],
            'travel_date' => $travelDate,
            'adult_count' => $adultCount,
            'child_count' => $childCount,
            'surcharge_required' => $surchargeRequired,
            'surcharge_amount' => $surchargeAmount,
            'surcharge_paid' => !$surchargeRequired,
            'occupancies' => $occupancies
        ];

        $reservationId = $reservationModel->createReservation($reservationData);

        // 6. 如果需要加价，创建加价支付记录
        // @todo 暂不支付
        $paymentInfo = null;
//        if ($surchargeRequired && $surchargeAmount > 0) {
//            $paymentModel = new PaymentModel();
//            $paymentData = [
//                'order_id' => $orderId,
//                'user_id' => $order['user_id'],
//                'amount' => $surchargeAmount,
//                'payment_type' => PaymentModel::TYPE_SURCHARGE,
//                'payment_method' => PaymentModel::METHOD_ALIPAY
//            ];
//
//            $paymentId = $paymentModel->createPayment($paymentData);
//            $payment = $paymentModel->getDetail($paymentId);
//
//            // 生成支付宝支付链接
//            $alipayService = new AlipayService();
//            $subject = "行程加价支付：{$order['itinerary']['title']}";
//            $returnUrl = config('app.base_url') . '/api/payment/alipay/return';
//            $notifyUrl = config('app.base_url') . '/api/payment/alipay/notify';
//
//            $payUrl = $alipayService->pagePay(
//                $payment['payment_no'],
//                $payment['amount'],
//                $subject,
//                $returnUrl,
//                $notifyUrl
//            );
//
//            $paymentInfo = [
//                'payment_id' => $paymentId,
//                'payment_no' => $payment['payment_no'],
//                'amount' => $payment['amount'],
//                'payment_url' => $payUrl
//            ];
//        }

        // 7. 更新库存已预订数量
        if ($inventory['available'] !== '不限') {
            $this->updateInventoryBookedAmount($itineraryId, $travelDate, $adultCount + $childCount);
        }
        if ($surchargeRequired === false) {
            $orderModel->where(['id' => $orderId])->save(['status' => ItineraryOrderModel::STATUS_BOOKED]);
        }

        return [
            'reservation_id' => $reservationId,
            'name' => $data['name'],
            'phone' => $data['phone'],
            'travel_date' => $travelDate,
            'adult_count' => $adultCount,
            'child_count' => $childCount,
            'surcharge_required' => $surchargeRequired,
            'surcharge_amount' => $surchargeAmount,
            'payment_info' => $paymentInfo
        ];
    }

    /**
     * 取消预约
     *
     * @param int $reservationId 预约ID
     * @return array 取消结果
     * @throws BaseException
     */
    public function cancelReservation(int $reservationId): array
    {
        // 1. 获取预约信息
        $reservationModel = new ReservationModel();
        $reservation = $reservationModel->getDetail((string)$reservationId);

        if (!$reservation) {
            throw new BaseException('预约不存在', 400);
        }

        if (!in_array($reservation['status'] === ReservationModel::STATUS_CANCELLED) {
            throw new BaseException('预约已经取消过了', 400);
        }

        // 2. 获取订单信息
        $orderModel = new ItineraryOrderModel();
        $order = $orderModel->getDetail($reservation['order_id']);

        if (!$order) {
            throw new BaseException('订单不存在', 400);
        }

        // 3. 取消预约
        $result = $reservationModel->cancelReservation((string)$reservationId);

        if (!$result) {
            throw new BaseException('取消预约失败', 500);
        }

        // 4. 处理退款（如果有加价支付）
        $refundInfo = null;
        if ($reservation['surcharge_required'] && $reservation['surcharge_paid'] && $reservation['surcharge_amount'] > 0) {
            // 获取加价支付记录
            $paymentModel = new PaymentModel();
            $payment = $paymentModel->getByOrderId($reservation['order_id'], PaymentModel::TYPE_SURCHARGE);

            if ($payment && $payment['status'] === PaymentModel::STATUS_PAID) {
                // 执行退款
                $alipayService = new AlipayService();
                $refundRequestNo = 'REF' . date('YmdHis') . mt_rand(1000, 9999);

                try {
                    $refundResult = $alipayService->refund(
                        $payment['payment_no'],
                        (string)$payment['amount'],
                        $refundRequestNo
                    );

                    // 更新支付状态为已退款
                    $paymentModel->updateStatus($payment['id'], PaymentModel::STATUS_REFUNDED, [
                        'refund_no' => $refundRequestNo
                    ]);

                    $refundInfo = [
                        'refund_amount' => $payment['amount'],
                        'refund_no' => $refundRequestNo,
                        'refund_time' => date('Y-m-d H:i:s')
                    ];
                } catch (BaseException $e) {
                    // 退款失败，记录日志但不阻止取消预约
                    // 实际项目中需要加入重试机制或人工处理
                }
            }
        }

        // 5. 恢复库存
        $this->updateInventoryBookedAmount(
            $order['itinerary_id'],
            $reservation['travel_date'],
            -($reservation['adult_count'] + $reservation['child_count'])
        );
        // 6. 订单状态恢复
        $orderModel->where(['id' => $reservation['order_id']])->save(['status' => ItineraryOrderModel::STATUS_PAID]);

        return [
            'reservation_id' => $reservationId,
            'cancelled' => true,
            'refund_info' => $refundInfo
        ];
    }

    public function payOrder($orderId, $payType) {
        // 1. 查询订单
        $orderModel = new ItineraryOrderModel();
        $order = $orderModel->getDetail($orderId);
        if (!$order) { throw new BaseException('订单不存在', 400); }
        if ($order['status'] !== 'pending') { throw new BaseException('订单状态不可支付', 400); }

        // 2. 获取/创建支付记录（幂等）
        $paymentModel = new PaymentModel();
        $payment = $paymentModel->getByOrderId($orderId, PaymentModel::TYPE_ORDER);
        if (!$payment || $payment['status'] !== PaymentModel::STATUS_PENDING) {
            $paymentId = $paymentModel->createPayment([
                'order_id' => $orderId,
                'user_id' => $order['userId'],
                'amount' => (float)$order['totalAmount'],
                'payment_type' => PaymentModel::TYPE_ORDER,
                'payment_method' => $payType,
            ]);
            $payment = $paymentModel->getDetail($paymentId);
        } else {
            // 若已有待支付记录但支付方式不同，则切换支付方式
            if (($payment['payment_method'] ?? '') !== $payType) {
                $paymentModel->where('id', $payment['id'])->update([
                    'payment_method' => $payType,
                    'update_time' => time()
                ]);
                $payment = $paymentModel->getDetail((string)$payment['id']);
            }
        }

        // 3. 组织统一支付载荷
        $subject = '行程预订：' . ($order['itinerary']['title'] ?? ('订单' . $orderId));
        $payload = [
            'out_trade_no' => $payment['paymentNo'],
            'total_amount' => (float)$payment['amount'],
            'subject' => $subject,
        ];
        if ($payType === PaymentModel::METHOD_XHS) {
            $userAuth = UserAuthModel::where(['user_id' => $order['userId'], 'platform' => UserAuthModel::PLATFORM_XHS])->find();
            if (empty($userAuth)) {
                throw new BaseException('还未完成小红书授权', 400);
            }
            $payload['platform_id'] = $userAuth->platform_id;
            $payload['itinerary'] = $order['itinerary'];
        }

        // 4. 调用渠道支付
        $channel = \app\service\payment\PaymentFactory::make($payType);
        $payRes = $channel->pay($payload);

        // 5. 统一响应结构
        return [
            'order_id' => $orderId,
            'payment_id' => $payment['id'],
            'payment_no' => $payment['payment_no'],
            'amount' => $payment['amount'],
            'pay_type' => $payType,
            'pay_result' => $payRes,
        ];
    }

    /**
     * 支付加价订单
     *
     * @param string $orderId 支付ID
     * @return array 支付结果
     * @throws BaseException
     */
    public function paySurcharge(string $orderId, $payType): array
    {
        // 检查是否已有预约
        $reservationModel = new ReservationModel();
        $reservation = $reservationModel->getByOrderId($orderId);

        if (empty($reservation)) {
            throw new BaseException('订单未预约', 400);
        }
        if ($reservation['surcharge_required'] == 0) {
            throw new BaseException('订单状态不需要加价', 400);
        }
        if ($reservation['surcharge_paid'] == 1) {
            throw new BaseException('加价已支付', 400);
        }

        $paymentModel = new PaymentModel();
        $paymentData = [
            'order_id' => $orderId,
            'user_id' => $reservation['user_id'],
            'amount' => $reservation['surcharge_amount'],
            'payment_type' => PaymentModel::TYPE_SURCHARGE,
            'payment_method' => $payType
        ];

        $paymentId = $paymentModel->createPayment($paymentData);
        $subject = '订单预约差价：' . ('订单' . $orderId);
        $payload = [
            'out_trade_no' => $paymentModel->payment_no,
            'total_amount' => $paymentModel->amount,
            'subject' => $subject
        ];
        if ($payType === PaymentModel::METHOD_XHS) {
            $userAuth = UserAuthModel::where(['user_id' => $reservation['userId'], 'platform' => UserAuthModel::PLATFORM_XHS])->find();
            if (empty($userAuth)) {
                throw new BaseException('还未完成小红书授权', 400);
            }
            $payload['platform_id'] = $userAuth->platform_id;
            $payload['itinerary'] = [];
        }

        // 4. 调用渠道支付
        $channel = \app\service\payment\PaymentFactory::make($payType);
        $payRes = $channel->pay($payload);

        // 5. 统一响应结构
        return [
            'order_id' => $orderId,
            'payment_id' => $paymentId,
            'payment_no' => $paymentModel->payment_no,
            'amount' => $paymentModel->amount,
            'pay_type' => $payType,
            'pay_result' => $payRes,
        ];
    }

    /**
     * 处理支付回调（旧：支付宝）
     *
     * @param array $params 回调参数
     * @return bool 处理结果
     * @throws BaseException
     */
    public function handlePaymentNotify(array $params): bool
    {
        if (!isset($params['out_trade_no']) || !isset($params['trade_no']) || !isset($params['success'])) {
            throw new BaseException('回调参数不完整', 400);
        }

        if (!$params['success']) {
            return false;
        }

        // 获取支付记录
        $paymentNo = $params['out_trade_no'];
        $paymentModel = new PaymentModel();
        $payment = $paymentModel->where('payment_no', $paymentNo)
            ->where('status', PaymentModel::STATUS_PENDING)
            ->where('delete_time', 0)
            ->find();

        if (!$payment) {
            return false;
        }

        // 统一处理支付成功
        $this->processPaymentPaid($payment->toArray(), (string)($params['trade_no'] ?? ''));

        // 恢复订单状态
        // ItineraryOrderModel::where(['id' => $orderId])->save(['status' => ItineraryOrderModel::STATUS_BOOKED]);

        return true;
    }

    /**
     * 商家接单（确认预约）
     *
     * 规则：
     * - 预约必须存在且未删除
     * - 预约状态需为 pending；cancelled/confirmed/completed 均不允许重复操作
     * - 如存在加价未支付，则不允许接单
     * - 接单成功后：
     *   1) 将预约状态置为 confirmed
     *   2) 将订单状态置为 received
     *
     * @param int $reservationId 预约ID
     * @return array 接单结果
     * @throws BaseException
     */
    public function receiveBooking(int $reservationId, $status, $note): array
    {
        $reservationModel = new ReservationModel();
        $reservation = $reservationModel->getDetail((string)$reservationId);

        if (!$reservation) {
            throw new BaseException('预约不存在', 404);
        }

        if ($reservation['status'] !== ReservationModel::STATUS_PENDING) {
            throw new BaseException('预约已处理', 400);
        }
        if ($reservation['status'] === ReservationModel::STATUS_COMPLETED) {
            throw new BaseException('预约已完成，无法接单', 400);
        }
        if ($reservation['status'] === ReservationModel::STATUS_CONFIRMED) {
            throw new BaseException('预约已接单，无需重复操作', 400);
        }

        // 如需加价但未支付，不允许接单
        if (!empty($reservation['surcharge_required']) && empty($reservation['surcharge_paid'])) {
            throw new BaseException('加价未支付，暂不能操作', 400);
        }

        // 1) 更新预约状态为 confirmed
        $reservationModel->updateStatus((string)$reservationId, $status, $note);

        // 2) 更新订单状态为 received
        if ($status == ReservationModel::STATUS_CONFIRMED) {
            $orderModel = new ItineraryOrderModel();
            $orderId = (string)$reservation['order_id'];
            $order = $orderModel->getDetail($orderId);
            if ($order) {
                $orderModel->updateStatus($orderId, ItineraryOrderModel::STATUS_RECEIVED);
            }
        }

        return [
            'reservation_id' => $reservationId,
            'status' => $status,
            'order_id' => $reservation['orderId'],
        ];
    }

    /**
     * 更新库存已预订数量
     *
     * @param string $itineraryId 行程ID
     * @param string $date 日期
     * @param int $changeAmount 变动数量
     * @return bool 更新结果
     */
    private function updateInventoryBookedAmount(string $itineraryId, string $date, int $changeAmount): bool
    {
        if ($changeAmount == 0) {
            return true;
        }

        $inventory = ItineraryInventoryModel::where('itinerary_id', $itineraryId)
            ->where('inventory_date', $date)
            ->where('delete_time', 0)
            ->find();

        if (!$inventory) {
            return false;
        }

        // 如果是减少数量，确保不为负数
        $newAmount = $inventory['booked_amount'] + $changeAmount;
        if ($newAmount < 0) {
            $newAmount = 0;
        }

        return $inventory->save([
            'booked_amount' => $newAmount,
            'update_time' => time()
        ]) > 0;
    }
}
