<?php
namespace app\model;

use base\base\BaseModel;
use support\Cache;

class CityInfoModel extends BaseModel
{
    /**
     * 境内城市
     * 与模型关联的表名
     *
     * @var string
     */
    protected $name = 'city_info';

    /**
     * 获取所有城市信息
     *
     * @return array
     */
    public static function getAllCityInfo()
    {
        // 优先获取缓存
        $cache = Cache::get('city_info');
        if ($cache) {
            return json_decode($cache, true);
        }
        $data = self::select()->toArray();
        if (!empty($data)) {
            Cache::set('city_info', json_encode($data));
        }
        return $data;
    }

    /**
     * 通过codes获取城市信息
     *
     * @param mixed $codes
     * @return string
     */
    public static function getCityInfoByIds(mixed $codes)
    {
        if (!is_array($codes)) {
            $codes = explode(',', $codes);
        }

        $cityInfo = array_column(self::getAllCityInfo(), 'cityName', 'cityCode');
        $city = [];
        foreach ($codes as $code) {
            $city[] = $cityInfo[$code] ?? '';
        }
        return trim(implode(',', $city), ',');
    }
}
