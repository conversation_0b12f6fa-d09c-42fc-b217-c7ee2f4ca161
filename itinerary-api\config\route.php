<?php
/**
 * This file is part of webman.
 *
 * Licensed under The MIT License
 * For full copyright and license information, please see the MIT-LICENSE.txt
 * Redistributions of files must retain the above copyright notice.
 *
 * <AUTHOR>
 * @copyright walkor<<EMAIL>>
 * @link      http://www.workerman.net/
 * @license   http://www.opensource.org/licenses/mit-license.php MIT License
 */

use app\api\controller\Booking;
use app\api\controller\CityInfo;
use app\api\controller\Inventory;
use app\api\controller\Itinerary;
use app\api\controller\LiveRoom;
use app\api\controller\Order;
use app\api\controller\ProductCategory;
use app\api\controller\Theme;
use app\api\controller\User;
use app\admin\controller\LiveRoomController;
use app\admin\controller\BookingController;
use app\admin\controller\SystemConfigGroupController;
use app\website\controller\Inventory as InventoryApp;
use app\website\controller\Itinerary as ItineraryApp;
use base\middleware\AccessControlTest;
use app\api\controller\ItineraryForUser;
use app\middleware\UserAuth;
use Webman\Route;

Route::options('[{path:.+}]', function () {
    return response('');
})->middleware([
    AccessControlTest::class,
]);

// 用户认证路由
Route::group('/api/user', function () {
    // 无需认证的路由
    Route::post('/register', [User::class, 'register']);
    Route::post('/login', [User::class, 'login']);
    Route::post('/pre-login', [User::class, 'preLogin']);
    Route::post('/auth-login', [User::class, 'authLogin']);

    // 需要认证的路由
    Route::group('', function () {
        Route::get('/profile', [User::class, 'profile']);
        Route::post('/profile', [User::class, 'updateProfile']);
        Route::post('/change-password', [User::class, 'changePassword']);
    })->middleware([
        UserAuth::class,
    ]);
});

// 前端线路相关接口
Route::group('/api/itinerary', function () {
    Route::post('/list', [ItineraryForUser::class, 'list']);
    Route::post('/detail/{id}', [ItineraryForUser::class, 'detail']);
});

// 预订系统路由
Route::group('/api/booking', function () {
    // 需要认证的路由
    Route::group('', function () {
        // 1. 用户下单API
        Route::post('/order', [Booking::class, 'createOrder']);

        // 订单支付
        Route::post('/pay', [Booking::class, 'payOrder']);

        // 2. 预约API
        Route::post('/reservation', [Booking::class, 'createReservation']);

        // 3. 加价支付API
        Route::post('/pay-surcharge', [Booking::class, 'paySurcharge']);

        // 4. 取消预约API
        Route::post('/cancel-reservation', [Booking::class, 'cancelReservation']);
    })->middleware([
        UserAuth::class,
    ]);

    // 支付回调路由（无需认证）
    Route::post('/payment/alipay/notify', [Booking::class, 'alipayNotify']);
    Route::get('/payment/alipay/return', [Booking::class, 'alipayReturn']);
});


Route::get('/api/itinerary/detail/{id}', [Itinerary::class, 'detail']);
Route::put('/api/itinerary/update/{id}', [Itinerary::class, 'update']);
Route::delete('/api/itinerary/delete/{id}', [Itinerary::class, 'delete']);
Route::put('/api/itinerary/online/{id}', [Itinerary::class, 'online']);
Route::put('/api/itinerary/offline/{id}', [Itinerary::class, 'offline']);

// 库存管理路由
Route::get('/api/itinerary/inventory/month', [Inventory::class, 'month']);
Route::post('/api/itinerary/inventory/batch-update', [Inventory::class, 'batchUpdate']);
Route::get('/api/itinerary/inventory/date', [Inventory::class, 'date']);
Route::post('/api/itinerary/inventory/update', [Inventory::class, 'update']);

// 订单管理路由
Route::get('/api/itinerary/order/list', [Order::class, 'list']);
Route::get('/api/itinerary/order/detail/{id}', [Order::class, 'detail']);
Route::post('/api/itinerary/order/create', [Order::class, 'create']);
Route::post('/api/itinerary/order/update-status', [Order::class, 'updateStatus']);
Route::post('/api/itinerary/order/delete/{id}', [Order::class, 'delete']);
Route::post('/api/itinerary/order/batch-delete', [Order::class, 'batchDelete']);
Route::get('/api/itinerary/order/itinerary-options', [Order::class, 'itineraryOptions']);
Route::post('/api/itinerary/order/cancel/{id}', [Order::class, 'cancel']);
Route::post('/api/itinerary/order/complete/{id}', [Order::class, 'complete']);
Route::post('/api/itinerary/order/pay/{id}', [Order::class, 'pay']);

// 城市信息路由
Route::get('/api/cities', [CityInfo::class, 'json']);
Route::get('/api/cities/overseas', [CityInfo::class, 'overseas']);

// 产品分类路由
Route::get('/api/category/list', [ProductCategory::class, 'list']);
Route::get('/api/category/tree', [ProductCategory::class, 'tree']);
Route::get('/api/category/detail/{id}', [ProductCategory::class, 'detail']);
Route::post('/api/category/create', [ProductCategory::class, 'create']);
Route::put('/api/category/update/{id}', [ProductCategory::class, 'update']);
Route::delete('/api/category/delete/{id}', [ProductCategory::class, 'delete']);
Route::put('/api/category/{id}/status', [ProductCategory::class, 'status']);

// 线路主题路由
Route::get('/api/itinerary/theme/list', [Theme::class, 'list']);
Route::get('/api/itinerary/theme/all', [Theme::class, 'all']);
Route::get('/api/itinerary/theme/{id}', [Theme::class, 'detail']);
Route::post('/api/itinerary/theme', [Theme::class, 'create']);
Route::put('/api/itinerary/theme/{id}', [Theme::class, 'update']);
Route::delete('/api/itinerary/theme/{id}', [Theme::class, 'delete']);

// 添加线路主题关联路由
Route::put('/api/itinerary/update-themes/{id}', [Itinerary::class, 'updateThemes']);

Route::get('/website/itinerary/{id}', [ItineraryApp::class, 'detail']);
Route::get('/website/itinerary/inventory/month', [InventoryApp::class, 'month']);
Route::get('/website/itinerary/inventory/date', [InventoryApp::class, 'date']);

// 测试鉴权功能的路由
Route::group('/api/test-auth', function () {
    Route::get('/read', [app\controller\TestAuthController::class, 'testRead']);
    Route::post('/write', [app\controller\TestAuthController::class, 'testWrite']);
    Route::get('/admin', [app\controller\TestAuthController::class, 'testAdmin']);
    Route::post('/multiple', [app\controller\TestAuthController::class, 'testMultiple']);
    Route::get('/public', [app\controller\TestAuthController::class, 'testPublic']);
});

// 后台直播间管理路由
Route::group('/admin', function () {
    Route::group('/user', function () {
        Route::get('', [\app\admin\controller\UserController::class, 'index']);
        Route::post('/disabled/{id}', [\app\admin\controller\UserController::class, 'disabled']);
    });

    Route::group('/live-room', function () {
        // 直播间管理
        Route::get('', [LiveRoomController::class, 'index']); // 获取直播间列表
        Route::post('', [LiveRoomController::class, 'store']); // 创建直播间
        Route::get('/{id}', [LiveRoomController::class, 'show']); // 获取直播间详情
        Route::put('/{id}', [LiveRoomController::class, 'update']); // 更新直播间
        Route::delete('/{id}', [LiveRoomController::class, 'destroy']); // 删除直播间
        Route::post('/batch-delete', [LiveRoomController::class, 'batchDestroy']); // 批量删除直播间

        // 状态管理
        Route::put('/{id}/status', [LiveRoomController::class, 'updateStatus']); // 更新直播间状态

        // 商品管理
        Route::get('/{id}/products', [LiveRoomController::class, 'products']); // 获取直播间商品列表
        Route::post('/{id}/products', [LiveRoomController::class, 'addProduct']); // 添加商品到直播间
        Route::post('/{id}/products/batch', [LiveRoomController::class, 'batchAddProducts']); // 添加商品到直播间
        Route::delete('/{id}/products/{productId}', [LiveRoomController::class, 'removeProduct']); // 移除直播间商品
        Route::post('/{id}/products/{productId}/top', [LiveRoomController::class, 'top']); // 移除直播间商品
        Route::post('/{id}/products/{productId}/sort', [LiveRoomController::class, 'sort']); // 移除直播间商品
    });

    // 预约管理
    Route::group('/booking', function () {
        Route::get('', [BookingController::class, 'index']); // 获取预约列表
        Route::get('/{id}', [BookingController::class, 'show']); // 预约详情
        Route::post('/receive', [BookingController::class, 'receiveBooking']); // 确认接单
        // 取消预约审核
    });
});
// 前端直播间相关接口
Route::group('/api/live-room', function () {
    // 获取直播间商品列表
    Route::get('/{id}/products', [LiveRoom::class, 'products']);
});


// 系统配置管理路由
Route::group('/system-config', function () {
    // 新的分组化管理接口（按分组ID）
    Route::group('/groups', function () {
        Route::post('', [SystemConfigGroupController::class, 'store']); // 创建分组
        Route::get('', [SystemConfigGroupController::class, 'index']); // 分组列表
        Route::put('/{id}', [SystemConfigGroupController::class, 'update']); // 更新分组
        Route::delete('/{id}', [SystemConfigGroupController::class, 'destroy']); // 删除分组（需无配置项）
        Route::get('/{id}/items', [SystemConfigGroupController::class, 'items']); // 指定分组下配置项列表
        Route::get('/{id}', [SystemConfigGroupController::class, 'detail']); // 指定分组下配置项列表
    });

    // 配置管理
    Route::post('', [app\admin\controller\SystemConfigController::class, 'store']); // 创建配置
    Route::get('/types', [app\admin\controller\SystemConfigController::class, 'getTypes']); // 获取配置类型

    // 单个配置操作
    Route::get('/{key}', [app\admin\controller\SystemConfigController::class, 'show']); // 获取单个配置
    Route::put('/{id}', [app\admin\controller\SystemConfigController::class, 'update']); // 更新配置
    Route::delete('/{key}', [app\admin\controller\SystemConfigController::class, 'destroy']); // 删除配置
});
