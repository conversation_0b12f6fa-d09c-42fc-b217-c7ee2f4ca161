<?php

declare(strict_types=1);

namespace app\service;

use Alipay\EasySDK\Kernel\Config;
use Alipay\EasySDK\Kernel\Factory;
use base\exception\BaseException;

class AlipayService
{
    public $client;
    public function __construct()
    {
        // 创建支付宝配置实例
        $options = $this->getOptions();

        // 创建支付宝客户端实例
        $this->client = Factory::setOptions($options);
    }

    function getOptions()
    {
        $options = new Config();
        $options->protocol = 'https';
        $options->gatewayHost = 'openapi.alipay.com';
        $options->signType = 'RSA2';

        $options->appId = '<-- 请填写您的AppId，例如：2019022663440152 -->';

        // 为避免私钥随源码泄露，推荐从文件中读取私钥字符串而不是写入源码中
        $options->merchantPrivateKey = '<-- 请填写您的应用私钥，例如：MIIEvQIBADANB ... ... -->';

        $options->alipayCertPath = '<-- 请填写您的支付宝公钥证书文件路径，例如：/foo/alipayCertPublicKey_RSA2.crt -->';
        $options->alipayRootCertPath = '<-- 请填写您的支付宝根证书文件路径，例如：/foo/alipayRootCert.crt" -->';
        $options->merchantCertPath = '<-- 请填写您的应用公钥证书文件路径，例如：/foo/appCertPublicKey_2019051064521003.crt -->';

        //注：如果采用非证书模式，则无需赋值上面的三个证书路径，改为赋值如下的支付宝公钥字符串即可
        // $options->alipayPublicKey = '<-- 请填写您的支付宝公钥，例如：MIIBIjANBg... -->';

        //可设置异步通知接收服务地址（可选）
        $options->notifyUrl = "<-- 请填写您的支付类接口异步通知接收服务地址，例如：https://www.test.com/callback -->";

        return $options;
    }

    /**
     * 支付宝网页支付
     * @param string $outTradeNo 商户订单号
     * @param float $totalAmount 订单金额
     * @param string $subject 订单标题
     * @param string $returnUrl 同步通知地址
     * @param string $notifyUrl 异步通知地址
     * @return string 支付宝支付页面的HTML/URL
     * @throws BaseException
     */
    public function pagePay(string $outTradeNo, float $totalAmount, string $subject, string $returnUrl, string $notifyUrl): string
    {
        try {
            // 返回一个页面
            $payUrl = "https://mock-alipay.example.com/pay?out_trade_no={$outTradeNo}&amount={$totalAmount}&subject=" . urlencode($subject);

            $this->client->payment()->wap()->pay($subject, $outTradeNo, $totalAmount, $notifyUrl, $returnUrl);

            // 记录日志
            $this->logPaymentRequest($outTradeNo, func_get_args());

            return $payUrl;
        } catch (\Exception $e) {
            throw new BaseException('支付宝支付接口请求失败：' . $e->getMessage(), 500);
        }
    }

    /**
     * 处理支付宝异步通知
     * @param array $params 通知参数
     * @return array 处理结果
     * @throws BaseException
     */
    public function handleNotify(array $params): array {
        try {
            // 模拟验证签名
            $verifyResult = true;

            if (!$verifyResult) {
                throw new BaseException('支付宝通知验签失败', 400);
            }

            // 获取必要参数
            $outTradeNo = $params['out_trade_no'] ?? '';
            $tradeNo = $params['trade_no'] ?? '';
            $tradeStatus = $params['trade_status'] ?? '';

            if (empty($outTradeNo) || empty($tradeNo) || empty($tradeStatus)) {
                throw new BaseException('支付宝通知参数不完整', 400);
            }

            // 判断交易状态
            if ($tradeStatus === 'TRADE_SUCCESS' || $tradeStatus === 'TRADE_FINISHED') {
                return [
                    'out_trade_no' => $outTradeNo,
                    'trade_no' => $tradeNo,
                    'trade_status' => $tradeStatus,
                    'success' => true
                ];
            }

            return [
                'out_trade_no' => $outTradeNo,
                'trade_no' => $tradeNo,
                'trade_status' => $tradeStatus,
                'success' => false
            ];
        } catch (BaseException $e) {
            throw $e;
        } catch (\Exception $e) {
            throw new BaseException('支付宝通知处理失败：' . $e->getMessage(), 500);
        }
    }

    /**
     * 查询支付宝订单
     * @param string $outTradeNo 商户订单号
     * @return array 查询结果
     * @throws BaseException
     */
    public function query(string $outTradeNo): array
    {
        // 实际项目中需要接入支付宝SDK，这里先模拟实现
        try {
            // 模拟查询结果
            return [
                'out_trade_no' => $outTradeNo,
                'trade_no' => 'ALIPAY_' . $outTradeNo,
                'trade_status' => 'TRADE_SUCCESS',
                'total_amount' => '100.00',
                'buyer_pay_amount' => '100.00',
                'buyer_user_id' => '****************',
                'buyer_logon_id' => 'usr***@gmail.com',
                'gmt_payment' => date('Y-m-d H:i:s'),
                'fund_bill_list' => [
                    [
                        'fund_channel' => 'ALIPAYACCOUNT',
                        'amount' => '100.00'
                    ]
                ]
            ];
        } catch (\Exception $e) {
            throw new BaseException('支付宝查询接口请求失败：' . $e->getMessage(), 500);
        }
    }

    /**
     * 支付宝退款
     * @param string $outTradeNo 商户订单号
     * @param string $refundAmount 退款金额
     * @param string $outRequestNo 退款请求号
     * @return array 退款结果
     * @throws BaseException
     */
    public function refund(string $outTradeNo, string $refundAmount, string $outRequestNo): array
    {
        try {
            $aliRes = $this->client->payment()->common()->refund($outTradeNo, $refundAmount);
            return [
                'out_trade_no' => $outTradeNo,
                'trade_no' => 'ALIPAY_' . $outTradeNo,
                'buyer_logon_id' => 'usr***@gmail.com',
                'fund_change' => 'Y',
                'refund_fee' => $refundAmount,
                'gmt_refund_pay' => date('Y-m-d H:i:s')
            ];
        } catch (\Exception $e) {
            throw new BaseException('支付宝退款接口请求失败：' . $e->getMessage(), 500);
        }
    }

    /**
     * 记录支付请求日志
     * @param string $outTradeNo 商户订单号
     * @param array $params 请求参数
     * @return void
     */
    private function logPaymentRequest(string $outTradeNo, array $params): void
    {
        // 实际项目中需要记录日志，这里暂不实现
    }
}
