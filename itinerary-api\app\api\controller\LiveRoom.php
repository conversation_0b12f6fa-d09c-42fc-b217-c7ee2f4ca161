<?php

declare(strict_types=1);

namespace app\api\controller;

use app\model\LiveRoomModel;
use app\model\LiveRoomProductModel;
use support\Request;
use support\Response;

/**
 * 前端直播间API控制器
 */
class LiveRoom {
    /**
     * 获取分页参数
     */
    protected function getPaginationParams(Request $request): array
    {
        return [
            'page' => max(1, (int)$request->input('page', 1)),
            'limit' => min(50, max(1, (int)$request->input('limit', 20)))
        ];
    }

    /**
     * 获取直播间商品列表
     * @param Request $request
     * @return Response
     */
    public function products(Request $request, $id): Response
    {
        try {
            if (!$id) {
                return error(500,'直播间ID不能为空');
            }

            $params = $this->getPaginationParams($request);
            $where = ['status' => LiveRoomProductModel::STATUS_ACTIVE];

            // 是否推荐商品
            $isFeatured = $request->input('is_featured');
            if ($isFeatured !== null) {
                $where['is_featured'] = (int)$isFeatured;
            }

            $productModel = new LiveRoomProductModel();
            $result = $productModel->getListByLiveRoom($id, $where, $params['page'], $params['limit']);

            // 格式化返回数据，计算实际价格
            $list = array_map(function($item) use ($productModel) {
                $actualPrice = $productModel->getActualPrice($item['id']);
                $itinerary = $item['itinerary'] ?? [];

                return [
                    'id' => $item['id'],
                    'product_id' => $item['product_id'],
                    'special_price' => $item['special_price'],
                    'discount_rate' => $item['discount_rate'],
                    'actual_price' => $actualPrice,
                    'stock_limit' => $item['stock_limit'],
                    'sold_count' => $item['sold_count'],
                    'is_featured' => $item['is_featured'],
                    'itinerary' => [
                        'id' => $itinerary['id'] ?? null,
                        'title' => $itinerary['title'] ?? '',
                        'price' => $itinerary['price'] ?? 0,
                        'original_price' => $itinerary['original_price'] ?? 0,
                        'cover_image' => $itinerary['cover_image'] ?? '',
                        'days' => $itinerary['days'] ?? 0,
                        'nights' => $itinerary['nights'] ?? 0,
                        'destination' => $itinerary['destination'] ?? '',
                        'departure_city' => $itinerary['departure_city'] ?? '',
                        'description' => $itinerary['description'] ?? '',
                        'features' => $itinerary['features'] ?? []
                    ]
                ];
            }, $result['list']);

            return success([
                'list' => $list,
                'total' => $result['total'],
                'page' => $result['page'],
                'page_size' => $result['page_size']
            ]);
        } catch (\Exception $e) {
            return error(500, '获取直播间商品列表失败: ' . $e->getMessage());
        }
    }
}
