<?php

declare(strict_types=1);

namespace app\model;

use base\base\BaseModel;

class UserAuthModel extends BaseModel
{
    const PLATFORM_WECHAT = 'wechat';
    const PLATFORM_XHS = 'xhs';
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'user_auths';

    /**
     * The attributes that should be hidden for arrays.
     *
     * @var array
     */
    protected $hidden = ['id', 'credentials', 'create_time', 'update_time', 'delete_time'];

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'user_id', 'platform', 'platform_id', 'nickname', 'avatar', 'credentials'
    ];

    /**
     * Get authorization by platform and platform ID
     *
     * @param string $platform
     * @param string $platformId
     * @return UserAuthModel|null
     */
    public static function getByPlatformAccount(string $platform, string $platformId): ?self
    {
        return self::where([
            'platform' => $platform,
            'platform_id' => $platformId
        ])->find();
    }

    /**
     * Create or update auth record
     *
     * @param int $userId
     * @param string $platform
     * @param string $platformId
     * @param array $data
     * @return UserAuthModel
     */
    public static function createOrUpdate(int $userId, string $platform, string $platformId, array $data): self
    {
        $auth = self::getByPlatformAccount($platform, $platformId);

        if ($auth) {
            $auth->save(array_merge($data, [
                'update_time' => date('Y-m-d H:i:s')
            ]));
            return $auth;
        }

        return self::create(array_merge($data, [
            'user_id' => $userId,
            'platform' => $platform,
            'platform_id' => $platformId
        ]));
    }

    /**
     * Get user relation
     *
     * @return \think\model\relation\BelongsTo
     */
    public function user()
    {
        return $this->belongsTo(UserModel::class, 'user_id', 'id');
    }
}
