<?php

namespace app\model;

use base\base\BaseModel;

/**
 * 系统配置分组模型
 */
class SystemConfigGroup extends BaseModel
{
    /** @var string 表名 */
    protected $name = 'system_config_groups';

    /** @var string 主键 */
    protected $pk = 'id';

    /** 允许写入字段 */
    protected $field = [
        'id',
        'group_name',
        'group_description',
        'sort_order',
        'created_at',
        'updated_at'
    ];

    /** 类型转换 */
    protected $type = [
        'id' => 'integer',
        'sort_order' => 'integer',
        'created_at' => 'integer',
        'updated_at' => 'integer'
    ];
}

