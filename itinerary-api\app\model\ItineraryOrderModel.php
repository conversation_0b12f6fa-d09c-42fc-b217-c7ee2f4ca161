<?php

declare(strict_types=1);

namespace app\model;

use base\base\BaseModel;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\model\relation\HasOne;

class ItineraryOrderModel extends BaseModel
{
    /**
     * 设置表名
     * @var string
     */
    protected $name = 'itinerary_order';

    /**
     * 设置主键
     * @var string
     */
    protected $pk = 'id';

    /**
     * 允许写入的字段
     * @var array
     */
    protected $field = [
        'id',
        'itinerary_id',
        'order_no',
        'user_id',
        'name',
        'phone',
        'travel_date',
        'adult_count',
        'child_count',
        'base_price',
        'hot_date_surcharge',
        'adult_surcharge',
        'child_surcharge',
        'room_count',
        'single_room_count',
        'upgrade_room_count',
        'single_room_surcharge',
        'upgrade_room_surcharge',
        'remark',
        'total_amount',
        'status',
        'create_time',
        'update_time',
        'delete_time'
    ];

    public function itinerary(): HasOne
    {
        return $this->hasOne(ItineraryModel::class, 'id', 'itinerary_id');
    }
    /**
     * 类型转换
     * @var array
     */
    protected $type = [
        'id' => 'integer',
        'adult_count' => 'integer',
        'child_count' => 'integer',
        'room_count' => 'integer',
        'single_room_count' => 'integer',
        'upgrade_room_count' => 'integer',
        'base_price' => 'float',
        'hot_date_surcharge' => 'float',
        'adult_surcharge' => 'float',
        'child_surcharge' => 'float',
        'single_room_surcharge' => 'float',
        'upgrade_room_surcharge' => 'float',
        'total_amount' => 'float',
        'travel_date' => 'date',
        'create_time' => 'integer',
        'update_time' => 'integer',
        'delete_time' => 'integer'
    ];

    /**
     * 生成订单编号
     * @return string
     */
    public static function generateOrderNo(): string
    {
        return 'IT' . date('YmdHis') . mt_rand(1000, 9999);
    }

    /**
     * 获取订单列表
     * @param array $where 查询条件
     * @param int $page 页码
     * @param int $pageSize 每页数量
     * @return array
     */
    public function getList(array $where = [], int $page = 1, int $pageSize = 10): array
    {
        $query = $this->where($where)->where('delete_time', 0);
        
        $total = $query->count();
        $list = $query->page($page, $pageSize)
        ->order('create_time', 'desc')
        ->append(['create_time','id'])
        ->select()->toArray();
        
        // 关联行程信息
        if (!empty($list)) {
            $itineraryIds = array_column($list, 'itineraryId');
            $itineraries = ItineraryModel::mk()->whereIn('id', $itineraryIds)->column('*', 'id');
            
            foreach ($list as &$item) {
                $item['itinerary'] = $itineraries[$item['itineraryId']] ?? null;
                
                // 状态文本
                $item['status_text'] = $this->getStatusText($item['status']);
            }
        }
        
        return [
            'list' => $list,
            'total' => $total,
            'page' => $page,
            'page_size' => $pageSize
        ];
    }

    /**
     * 获取订单详情
     * @param string $id 订单ID
     * @return array|null
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function getDetail(string $id): ?array
    {
        $order = $this->where('id', $id)->append(['id','create_time','update_time'])->find();
        
        if (!$order) {
            return null;
        }
        
        $order = $order->toArray();
        
        // 获取关联的行程信息
        if (!empty($order['itineraryId'])) {
            $itinerary = ItineraryModel::mk()->where('id', $order['itineraryId'])->find();
            if ($itinerary) {
                $order['itinerary'] = $itinerary->toArray();
            }
        }
        
        // 状态文本
        $order['statusText'] = $this->getStatusText($order['status']);
        
        return $order;
    }

    /**
     * 创建订单
     * @param array $data 订单数据
     * @return string 订单ID
     */
    public function createOrder(array $data): string
    {
        $data['order_no'] = self::generateOrderNo();
        $data['create_time'] = $data['update_time'] = time();
        $data['status'] = 'pending';
        
        // 确保总金额已经计算好（在控制器中计算）
        if (!isset($data['total_amount'])) {
            throw new \Exception('订单总金额未提供');
        }
        
        $this->save($data);
        return (string)$this->id;
    }

    /**
     * 更新订单状态
     * @param string $id 订单ID
     * @param string $status 订单状态
     * @return bool
     */
    public function updateStatus(string $id, string $status): bool
    {
        return $this->where('id', $id)->where('delete_time', 0)->update([
            'status' => $status,
            'update_time' => time()
        ]) > 0;
    }

    /**
     * 获取状态文本
     * @param string $status 状态
     * @return string
     */
    private function getStatusText(string $status): string
    {
        $statusMap = [
            'pending' => '待支付',
            'paid' => '已支付',
            'cancelled' => '已取消',
            'completed' => '已完成'
        ];
        
        return $statusMap[$status] ?? $status;
    }
} 