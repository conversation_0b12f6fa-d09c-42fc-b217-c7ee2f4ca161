<?php

namespace app\service;

use support\Log;
use support\think\Db;
use Throwable;

/**
 * 基础服务类
 * 提供通用的服务功能
 */
abstract class BaseService
{
    /**
     * 数据库事务执行
     *
     * @param callable $callback
     * @return mixed
     * @throws Throwable
     */
    protected function transaction(callable $callback)
    {
        return Db::transaction($callback);
    }

    /**
     * 记录日志
     *
     * @param string $level
     * @param string $message
     * @param array $context
     */
    protected function log(string $level, string $message, array $context = [])
    {
        Log::$level($message, $context);
    }

    /**
     * 记录信息日志
     *
     * @param string $message
     * @param array $context
     */
    protected function logInfo(string $message, array $context = [])
    {
        $this->log('info', $message, $context);
    }

    /**
     * 记录错误日志
     *
     * @param string $message
     * @param array $context
     */
    protected function logError(string $message, array $context = [])
    {
        $this->log('error', $message, $context);
    }

    /**
     * 记录警告日志
     *
     * @param string $message
     * @param array $context
     */
    protected function logWarning(string $message, array $context = [])
    {
        $this->log('warning', $message, $context);
    }

    /**
     * 构建成功响应
     *
     * @param mixed $data
     * @param string $message
     * @return array
     */
    protected function success($data = null, string $message = 'success')
    {
        return [
            'code' => 200,
            'message' => $message,
            'data' => $data,
            'timestamp' => time(),
        ];
    }

    /**
     * 构建错误响应
     *
     * @param string $message
     * @param int $code
     * @param mixed $errors
     * @return array
     */
    protected function error(string $message, int $code = 400, $errors = null)
    {
        $response = [
            'code' => $code,
            'message' => $message,
            'timestamp' => time(),
        ];

        if ($errors !== null) {
            $response['errors'] = $errors;
        }

        return $response;
    }

    /**
     * 验证必需参数
     *
     * @param array $data
     * @param array $required
     * @throws \InvalidArgumentException
     */
    protected function validateRequired(array $data, array $required)
    {
        $missing = [];

        foreach ($required as $field) {
            if (!isset($data[$field]) || $data[$field] === '' || $data[$field] === null) {
                $missing[] = $field;
            }
        }

        if (!empty($missing)) {
            throw new \InvalidArgumentException('缺少必需参数: ' . implode(', ', $missing));
        }
    }

    /**
     * 验证日期格式
     *
     * @param string $date
     * @param string $format
     * @return bool
     */
    protected function validateDate(string $date, string $format = 'Y-m-d')
    {
        $d = \DateTime::createFromFormat($format, $date);
        return $d && $d->format($format) === $date;
    }

    /**
     * 验证日期范围
     *
     * @param string $startDate
     * @param string $endDate
     * @throws \InvalidArgumentException
     */
    protected function validateDateRange(string $startDate, string $endDate)
    {
        if (!$this->validateDate($startDate)) {
            throw new \InvalidArgumentException('开始日期格式错误');
        }

        if (!$this->validateDate($endDate)) {
            throw new \InvalidArgumentException('结束日期格式错误');
        }

        if ($startDate > $endDate) {
            throw new \InvalidArgumentException('开始日期不能晚于结束日期');
        }
    }

    /**
     * 分页处理
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param int $page
     * @param int $limit
     * @return array
     */
    protected function paginate($query, int $page = 1, int $limit = 20)
    {
        $page = max(1, $page);
        $limit = min(100, max(1, $limit));

        $total = $query->count();
        $totalPages = ceil($total / $limit);

        $items = $query->offset(($page - 1) * $limit)
            ->limit($limit)
            ->get();

        return [
            'items' => $items,
            'pagination' => [
                'current_page' => $page,
                'per_page' => $limit,
                'total' => $total,
                'total_pages' => $totalPages,
                'has_more' => $page < $totalPages,
            ]
        ];
    }

    /**
     * 过滤空值
     *
     * @param array $data
     * @return array
     */
    protected function filterEmpty(array $data)
    {
        return array_filter($data, function ($value) {
            return $value !== null && $value !== '';
        });
    }

    /**
     * 格式化金额
     *
     * @param float $amount
     * @param int $decimals
     * @return float
     */
    protected function formatAmount(float $amount, int $decimals = 2)
    {
        return round($amount, $decimals);
    }

    /**
     * 生成唯一ID
     *
     * @param string $prefix
     * @return string
     */
    protected function generateUniqueId(string $prefix = '')
    {
        return $prefix . date('YmdHis') . mt_rand(1000, 9999);
    }

    /**
     * 检查权限
     *
     * @param mixed $user
     * @param string $permission
     * @return bool
     */
    protected function checkPermission($user, string $permission)
    {
        if (!$user) {
            return false;
        }

        // 这里可以根据实际的权限系统实现
        if (method_exists($user, 'hasPermission')) {
            return $user->hasPermission($permission);
        }

        return true; // 临时返回true，实际应该根据权限系统实现
    }

    /**
     * 获取当前用户
     *
     * @return mixed
     */
    protected function getCurrentUser()
    {
        // 这里应该从请求上下文中获取当前用户
        // 临时返回null，实际应该根据认证系统实现
        return null;
    }
}
