<?php

namespace app\api\controller;

use app\model\CitiesModel;
use app\model\CityInfoModel;
use app\model\ItineraryDayModel;
use app\model\ItineraryModel;
use app\model\ItineraryScheduleModel;
use app\model\ProductCategoryModel;
use base\auth\Auth;
use support\Request;
use support\Response;
use think\facade\Db;

class Itinerary
{
    /**
     * 获取行程列表
     * @param Request $request
     * @return Response
     */
    #[Auth('itinerary:itinerary:read')]
    public function list(Request $request): Response
    {
        $page = $request->input('page', 1);
        $pageSize = $request->input('pageSize', 10);
        $status = $request->input('status');
        $keyword = $request->input('keyword');
        $destination = $request->input('destination');
        $departureCity = $request->input('departureCity');
        $categoryId = $request->input('categoryId');
        $productType = $request->input('productType');

        // 构建查询条件
        $query = ItineraryModel::mk()
            ->field([
                'id', 'title', 'price', 'original_price', 'days', 'nights', 'destination', 'departure_city',
                'product_type', 'cover_image', 'status', 'travel_mode', 'group_type', 'min_group_size',
                'max_group_size', 'collect_type', 'deposit', 'promotion_price', 'category_id', 'category_path',
                'valid_from', 'valid_to', 'is_long_term', 'single_room_surcharge', 'room_upgrade_surcharge',
                'create_time', 'update_time',
                // 主题相关字段
                'themes_ids',
                // 交易规则相关字段
                'consumable_date_type', 'consumable_days', 'consumable_dates',
                // 限购规则
                'purchase_limit_single', 'purchase_limit_per_order', 'purchase_limit_rules', 'purchase_limit_details',
                // 预约规则
                'earliest_booking_days', 'advance_booking_required', 'advance_booking_range',
                'advance_booking_time', 'advance_booking_time_unit',
                // 取消规则
                'has_no_cancellation_after_appointment', 'refund_before_appointment', 'auto_refund_after_expiration',
                'has_breach_of_contract_terms', 'breach_of_contract_terms', 'breach_of_contract_base'
            ])
            ->where('delete_time', 0);

        // 应用筛选条件
        if (!empty($status)) {
            $query = $query->where('status', $status);
        }
        if (!empty($keyword)) {
            $query = $query->where('title', 'like', "%$keyword%");
        }
        if (!empty($destination)) {
            $query = $query->where('destination', $destination);
        }
        if (!empty($departureCity)) {
            $query = $query->whereFindInSet('departure_city', $departureCity);
        }
        if (!empty($categoryId)) {
            $query = $query->where('category_id', $categoryId);
        }
        if (!empty($productType)) {
            $query = $query->where('product_type', $productType);
        }

        // 执行查询
        $total = $query->count();
        $list = $query->page($page, $pageSize)
        ->order('id', 'desc')->append(['id'])
            ->withAttr('themes_ids', function ($value) {
                return str2Arr($value);
            })
        ->select();

        // 转换为前端格式
        $items = [];
        foreach ($list as $item) {
            $items[] = $this->convertToFrontend($item->toArray());
        }

        return success([
            'list' => $items,
            'total' => $total,
            'page' => (int)$page,
            'pageSize' => (int)$pageSize,
        ]);
    }

    /**
     * 获取行程详情
     * @param Request $request
     * @param string $id
     * @return Response
     */
    public function detail(Request $request, string $id = ''): Response
    {
        // 尝试从路径获取ID，如果没有则从请求参数中获取
        if (empty($id)) {
            $id = $request->input('id');
        }

        if (!$id) {
            return error(400, '参数错误');
        }

        // 查询行程基本信息
        $detail = ItineraryModel::mk()
            ->with(['scheduleDays'=> ['schedules'], 'inventory'])
            ->field([
                'id', 'title', 'price', 'original_price', 'days', 'nights', 'destination', 'departure_city',
                'product_type', 'cover_image', 'images', 'image_description', 'description', 'features', 'status',
                'travel_mode', 'group_type', 'min_group_size', 'max_group_size', 'collect_type', 'deposit',
                'promotion_price', 'category_id', 'category_path', 'valid_from', 'valid_to', 'is_long_term',
                'single_room_surcharge', 'room_upgrade_surcharge', 'create_time', 'update_time',
                // 主题相关字段
                'themes_ids',
                // 交易规则相关字段
                'consumable_date_type', 'consumable_days', 'consumable_dates',
                // 限购规则
                'purchase_limit_single', 'purchase_limit_per_order', 'purchase_limit_rules', 'purchase_limit_details',
                // 预约规则
                'earliest_booking_days', 'advance_booking_required', 'advance_booking_range',
                'advance_booking_time', 'advance_booking_time_unit',
                // 取消规则
                'has_no_cancellation_after_appointment', 'refund_before_appointment', 'auto_refund_after_expiration',
                'has_breach_of_contract_terms', 'breach_of_contract_terms', 'breach_of_contract_base'
            ])
            ->where('id', $id)
            ->append(['id'])
            ->withAttr('themes_ids', function ($value) {
                return str2Arr($value);
            })
            ->find();

        if (!$detail) {
            return error(400, '行程不存在');
        }

        $result = $detail->toArray();

        // 处理是否长期有效
        $result['isLongTerm'] = (bool)($result['isLongTerm'] ?? false);

        // 处理scheduleDays结构
        $scheduleDays = [];
        if (!empty($result['scheduleDays'])) {
            foreach ($result['scheduleDays'] as $day) {
                $dayItem = [
                    'day' => (int)$day['day'],
                    'title' => $day['title'],
                    'description' => $day['description'] ?? '',
                    'meetingPoint' => $day['meetingPoint'] ?? '',
                    'meetingTime' => $day['meetingTime'] ?? '',
                    'schedule' => []
                ];

                // 处理每天的行程安排
                if (!empty($day['schedules'])) {
                    foreach ($day['schedules'] as $schedule) {
                        $scheduleItem = [
                            'day' => (int)$day['day'],
                            'title' => $schedule['title'],
                            'description' => $schedule['description'] ?? '',
                            'activities' => !empty($schedule['activities']) ? $schedule['activities'] : [],
                            'meals' => !empty($schedule['meals']) ? $schedule['meals'] : [],
                            'accommodation' => $schedule['accommodation'] ?? '',
                            'images' => !empty($schedule['images']) ? $schedule['images'] : [],
                            'type' => $schedule['type'] ?? '',
                            'startTime' => $schedule['startTime'] ?? '',
                            'mealType' => !empty($schedule['mealType']) ? $schedule['mealType'] : [],
                            'adultMealIncluded' => $schedule['adultMealIncluded'] ?? '',
                            'childMealIncluded' => $schedule['childMealIncluded'] ?? '',
                            'mealPrice' => (float)($schedule['mealPrice'] ?? 0),
                            'transportType' => $schedule['transportType'] ?? '',
                            'flightNumber' => $schedule['flightNumber'] ?? '',
                            'departureCity' => $schedule['departureCity'] ?? '',
                            'arrivalCity' => $schedule['arrivalCity'] ?? '',
                            'stayType' => $schedule['stayType'] ?? '',
                            'hotelName' => $schedule['hotelName'] ?? '',
                            'remark' => $schedule['remark'] ?? ''
                        ];

                        // 处理嵌套对象
                        $scheduleItem['mealDuration'] = [
                            'hours' => (int)($schedule['mealDurationHours'] ?? 0),
                            'minutes' => (int)($schedule['mealDurationMinutes'] ?? 0)
                        ];

                        $scheduleItem['estimatedDuration'] = [
                            'hours' => (int)($schedule['estimatedDurationHours'] ?? 0),
                            'minutes' => (int)($schedule['estimatedDurationMinutes'] ?? 0)
                        ];

                        $scheduleItem['activityDuration'] = [
                            'hours' => (int)($schedule['activityDurationHours'] ?? 0),
                            'minutes' => (int)($schedule['activityDurationMinutes'] ?? 0)
                        ];

                        $dayItem['schedule'][] = $scheduleItem;
                    }
                }

                $scheduleDays[] = $dayItem;
            }
        }

        // 替换schedulesDay为scheduleDays以符合接口定义
        unset($result['schedulesDay']);
        $result['scheduleDays'] = $scheduleDays;

        // 转换驼峰命名
        // $result = $this->convertToFrontend($result);

        return success($result);
    }

    /**
     * 创建行程
     * @param Request $request
     * @return Response
     */
    public function create(Request $request): Response
    {
        $data = $request->post();

        // 开启事务
        Db::startTrans();
        try {
            // 处理行程基本信息
            $itineraryData = [
                'title' => $data['title'],
                'price' => $data['price'],
                'original_price' => $data['originalPrice'] ?? 0,
                'days' => $data['days'],
                'nights' => $data['nights'],
                'destination' => $data['destination'],
                'departure_city' => $data['departureCity'],
                'product_type' => $data['productType'],
                'cover_image' => $data['coverImage'],
                'images' => json_encode($data['images']),
                'image_description' => isset($data['imageDescription']) ? json_encode($data['imageDescription']) : null,
                'description' => $data['description'],
                'features' => isset($data['features']) ? json_encode($data['features']) : null,
                'status' => $data['status'],
                'travel_mode' => $data['travelMode'],
                'group_type' => $data['groupType'],
                'min_group_size' => $data['minGroupSize'],
                'max_group_size' => $data['maxGroupSize'],
                'collect_type' => $data['collectType'] === 'capitation' ? 'capitation' : 'collectAll',
                'deposit' => $data['deposit'],
                'promotion_price' => $data['promotionPrice'],
                'category_id' => $data['categoryId'],
                'category_path' => json_encode($data['categoryPath']),
                'is_long_term' => $data['isLongTerm'] ? 1 : 0,
                'valid_from' => !empty($data['validFrom']) ? date('Y-m-d', strtotime($data['validFrom']))  : null,
                'valid_to' =>    !empty($data['validTo']) ? date('Y-m-d', strtotime($data['validTo']))  : null,
                'single_room_surcharge' => $data['singleRoomSurcharge'] ?? 0,
                'room_upgrade_surcharge' => $data['roomUpgradeSurcharge'] ?? 0,

                // 交易规则相关字段
                'consumable_date_type' => $data['consumableDateType'] ?? null,
                'consumable_days' => $data['consumableDays'] ?? null,
                'consumable_dates' => isset($data['consumableDates']) ? json_encode($data['consumableDates']) : null,

                // 限购规则
                'purchase_limit_single' => isset($data['purchaseLimitSingle']) ? ($data['purchaseLimitSingle'] ? 1 : 0) : null,
                'purchase_limit_per_order' => $data['purchaseLimitPerOrder'] ?? null,
                'purchase_limit_rules' => isset($data['purchaseLimitRules']) ? ($data['purchaseLimitRules'] ? 1 : 0) : null,
                'purchase_limit_details' => $data['purchaseLimitDetails'] ?? null,

                // 预约规则
                'earliest_booking_days' => $data['earliestBookingDays'] ?? null,
                'advance_booking_required' => isset($data['advanceBookingRequired']) ? ($data['advanceBookingRequired'] ? 1 : 0) : null,
                'advance_booking_range' => $data['advanceBookingRange'] ?? null,
                'advance_booking_time' => $data['advanceBookingTime'] ?? null,
                'advance_booking_time_unit' => $data['advanceBookingTimeUnit'] ?? null,

                // 取消规则
                'has_no_cancellation_after_appointment' => isset($data['hasNoCancellationAfterAppointment']) ? ($data['hasNoCancellationAfterAppointment'] ? 1 : 0) : null,
                'refund_before_appointment' => isset($data['refundBeforeAppointment']) ? ($data['refundBeforeAppointment'] ? 1 : 0) : null,
                'auto_refund_after_expiration' => isset($data['autoRefundAfterExpiration']) ? ($data['autoRefundAfterExpiration'] ? 1 : 0) : null,
                'has_breach_of_contract_terms' => isset($data['hasBreachOfContractTerms']) ? ($data['hasBreachOfContractTerms'] ? 1 : 0) : null,
                'breach_of_contract_terms' => isset($data['breachOfContractTerms']) ? json_encode($data['breachOfContractTerms']) : null,
                'breach_of_contract_base' => $data['breachOfContractBase'] ?? null,
            ];

            // 创建行程
            $itinerary = ItineraryModel::mk();
            $itinerary->save($itineraryData);
            $itineraryId = $itinerary->id;

            if (!empty($data['scheduleDays'])) {
                foreach ($data['scheduleDays'] as $dayInfo) {
                    $scheduleDay = [
                        'day' => $dayInfo['day'],
                        'itinerary_id' => $itineraryId,
                        'title' => $dayInfo['title'],
                        'description' => $dayInfo['description'] ?? '',
                        'meeting_point' => $dayInfo['meetingPoint'] ?? null,
                        'meeting_time' => isset($dayInfo['meetingTime']) ? date('H:i:s', strtotime($dayInfo['meetingTime'])) : null,
                    ];
                    $itineraryDay = ItineraryDayModel::mk();
                    $itineraryDay->save($scheduleDay);
                    $itineraryDayId = $itineraryDay->id;
                    // 处理行程安排
                    if (!empty($dayInfo['schedule'])) {
                        foreach ($dayInfo['schedule'] as $schedule) {
                            $scheduleData = [
                                'itinerary_id' => $itineraryId,
                                'itinerary_day_id' => $itineraryDayId,
                                'title' => $schedule['title'],
                                'description' => $schedule['description'] ?? '',
                                'activities' => json_encode($schedule['activities'] ?? []),
                                'meals' => json_encode($schedule['meals'] ?? []),
                                'accommodation' => $schedule['accommodation'] ?? '',
                                'images' => isset($schedule['images']) ? json_encode($schedule['images']) : null,
                                'type' => $schedule['type'],
                                'start_time' => isset($schedule['startTime']) ? date('H:i:s', strtotime($schedule['startTime'])) : null,
                                'meal_type' => isset($schedule['mealType']) ? json_encode($schedule['mealType']) : null,
                                'adult_meal_included' => $schedule['adultMealIncluded'] ?? null,
                                'child_meal_included' => $schedule['childMealIncluded'] ?? null,
                                'meal_price' => $schedule['mealPrice'] ?? null,
                                'transport_type' => $schedule['transportType'] ?? null,
                                'flight_number' => $schedule['flightNumber'] ?? null,
                                'departure_city' => $schedule['departureCity'] ?? null,
                                'arrival_city' => $schedule['arrivalCity'] ?? null,
                                'estimated_duration_hours' => $schedule['estimatedDuration']['hours'] ?? 0,
                                'estimated_duration_minutes' => $schedule['estimatedDuration']['minutes'] ?? 0,
                                'stay_type' => $schedule['stayType'] ?? null,
                                'hotel_name' => $schedule['hotelName'] ?? null,
                                'activity_duration_hours' => $schedule['activityDuration']['hours'] ?? 0,
                                'activity_duration_minutes' => $schedule['activityDuration']['minutes'] ?? 0,
                                'remark' => $schedule['remark'] ?? '',
                                'create_time' => time(),
                                'update_time' => time()
                            ];

                            ItineraryScheduleModel::mk()->save($scheduleData);
                        }
                    }
                }
            }
            Db::commit();
            return success(['id' => $itineraryId]);
        } catch (\Exception $e) {
            Db::rollback();
            throw  $e;
            return error(400, '创建失败：' . $e->getMessage());
        }
    }

    /**
     * 更新行程
     * @param Request $request
     * @param string $id
     * @return Response
     */
    public function update(Request $request, string $id = ''): Response
    {
        // 尝试从路径获取ID，如果没有则从请求参数中获取
        if (empty($id)) {
            $id = $request->input('id');
        }

        if (!$id) {
            return error(400, '参数错误');
        }

        $data = $request->post();

        // 验证行程是否存在
        $detail = ItineraryModel::mk()->where('id', $id)->append(['id'])->find();;
        if (!$detail) {
            return error(400, '行程不存在');
        }
        $itineraryId = $detail['id'];
        // 开启事务
        Db::startTrans();
        try {
            // 处理行程基本信息
            $itineraryData = [
                'id' => $id,
                'title' => $data['title'],
                'price' => $data['price'],
                'original_price' => $data['originalPrice'],
                'days' => $data['days'],
                'nights' => $data['nights'],
                'destination' => $data['destination'],
                'departure_city' => $data['departureCity'],
                'product_type' => $data['productType'],
                'cover_image' => $data['coverImage'],
                'images' => json_encode($data['images']),
                'image_description' => isset($data['imageDescription']) ? json_encode($data['imageDescription']) : null,
                'description' => $data['description'],
                'features' => isset($data['features']) ? json_encode($data['features']) : null,
                'status' => $data['status'],
                'travel_mode' => $data['travelMode'],
                'group_type' => $data['groupType'],
                'min_group_size' => $data['minGroupSize'],
                'max_group_size' => $data['maxGroupSize'],
                'collect_type' => $data['collectType'] === 'capitation' ? 'capitation' : 'collectAll',
                'deposit' => $data['deposit'],
                'promotion_price' => $data['promotionPrice'],
                'category_id' => $data['categoryId'],
                'category_path' => json_encode($data['categoryPath']),
                'is_long_term' => $data['isLongTerm'] ? 1 : 0,
                'valid_from' => !empty($data['validFrom']) ? date('Y-m-d', strtotime($data['validFrom']))  : null,
                'valid_to' =>    !empty($data['validTo']) ? date('Y-m-d', strtotime($data['validTo']))  : null,
                'single_room_surcharge' => $data['singleRoomSurcharge'] ?? 0,
                'room_upgrade_surcharge' => $data['roomUpgradeSurcharge'] ?? 0,

                // 交易规则相关字段
                'consumable_date_type' => $data['consumableDateType'] ?? null,
                'consumable_days' => $data['consumableDays'] ?? null,
                'consumable_dates' => isset($data['consumableDates']) ? json_encode($data['consumableDates']) : null,

                // 限购规则
                'purchase_limit_single' => isset($data['purchaseLimitSingle']) ? ($data['purchaseLimitSingle'] ? 1 : 0) : null,
                'purchase_limit_per_order' => $data['purchaseLimitPerOrder'] ?? null,
                'purchase_limit_rules' => isset($data['purchaseLimitRules']) ? ($data['purchaseLimitRules'] ? 1 : 0) : null,
                'purchase_limit_details' => $data['purchaseLimitDetails'] ?? null,

                // 预约规则
                'earliest_booking_days' => $data['earliestBookingDays'] ?? null,
                'advance_booking_required' => isset($data['advanceBookingRequired']) ? ($data['advanceBookingRequired'] ? 1 : 0) : null,
                'advance_booking_range' => $data['advanceBookingRange'] ?? null,
                'advance_booking_time' => $data['advanceBookingTime'] ?? null,
                'advance_booking_time_unit' => $data['advanceBookingTimeUnit'] ?? null,

                // 取消规则
                'has_no_cancellation_after_appointment' => isset($data['hasNoCancellationAfterAppointment']) ? ($data['hasNoCancellationAfterAppointment'] ? 1 : 0) : null,
                'refund_before_appointment' => isset($data['refundBeforeAppointment']) ? ($data['refundBeforeAppointment'] ? 1 : 0) : null,
                'auto_refund_after_expiration' => isset($data['autoRefundAfterExpiration']) ? ($data['autoRefundAfterExpiration'] ? 1 : 0) : null,
                'has_breach_of_contract_terms' => isset($data['hasBreachOfContractTerms']) ? ($data['hasBreachOfContractTerms'] ? 1 : 0) : null,
                'breach_of_contract_terms' => isset($data['breachOfContractTerms']) ? json_encode($data['breachOfContractTerms']) : null,
                'breach_of_contract_base' => $data['breachOfContractBase'] ?? null,
            ];

            // 更新行程
            $detail->save($itineraryData);

            // 处理行程安排
            if (!empty($data['scheduleDays'])) {
                // 先删除原有行程安排
                $scheduleDayModel = new ItineraryDayModel();
                $scheduleDayModel->where('itinerary_id', $id)->delete();
                $scheduleModel = new ItineraryScheduleModel();
                $scheduleModel->where('itinerary_id', $id)->delete();

                foreach ($data['scheduleDays'] as $dayInfo) {
                    $scheduleDay = [
                        'day' => $dayInfo['day'],
                        'itinerary_id' => $itineraryId,
                        'title' => $dayInfo['title'],
                        'description' => $dayInfo['description'] ?? '',
                        'meeting_point' => $dayInfo['meetingPoint'] ?? null,
                        'meeting_time' => isset($dayInfo['meetingTime']) ? date('H:i:s', strtotime($dayInfo['meetingTime'])) : null,
                    ];
                    $itineraryDay = ItineraryDayModel::mk();
                    $itineraryDay->save($scheduleDay);
                    $itineraryDayId = $itineraryDay->id;
                    // 处理行程安排
                    if (!empty($dayInfo['schedule'])) {
                        foreach ($dayInfo['schedule'] as $schedule) {
                            $scheduleData = [
                                'itinerary_id' => $itineraryId,
                                'itinerary_day_id' => $itineraryDayId,
                                'title' => $schedule['title'],
                                'description' => $schedule['description'] ?? '',
                                'activities' => json_encode($schedule['activities'] ?? []),
                                'meals' => json_encode($schedule['meals'] ?? []),
                                'accommodation' => $schedule['accommodation'] ?? '',
                                'images' => isset($schedule['images']) ? json_encode($schedule['images']) : null,
                                'type' => $schedule['type'],
                                'start_time' => isset($schedule['startTime']) ? date('H:i:s', strtotime($schedule['startTime'])) : null,
                                'meal_type' => isset($schedule['mealType']) ? json_encode($schedule['mealType']) : null,
                                'adult_meal_included' => $schedule['adultMealIncluded'] ?? null,
                                'child_meal_included' => $schedule['childMealIncluded'] ?? null,
                                'meal_price' => $schedule['mealPrice'] ?? null,
                                'transport_type' => $schedule['transportType'] ?? null,
                                'flight_number' => $schedule['flightNumber'] ?? null,
                                'departure_city' => $schedule['departureCity'] ?? null,
                                'arrival_city' => $schedule['arrivalCity'] ?? null,
                                'estimated_duration_hours' => $schedule['estimatedDuration']['hours'] ?? 0,
                                'estimated_duration_minutes' => $schedule['estimatedDuration']['minutes'] ?? 0,
                                'stay_type' => $schedule['stayType'] ?? null,
                                'hotel_name' => $schedule['hotelName'] ?? null,
                                'activity_duration_hours' => $schedule['activityDuration']['hours'] ?? 0,
                                'activity_duration_minutes' => $schedule['activityDuration']['minutes'] ?? 0,
                                'remark' => $schedule['remark'] ?? '',
                            ];

                            ItineraryScheduleModel::mk()->save($scheduleData);
                        }
                    }
                }
            }

            Db::commit();

            return success();
        } catch (\Exception $e) {
            Db::rollback();
            return json([
                'code' => 500,
                'msg' => '更新失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 删除行程
     * @param Request $request
     * @param string $id
     * @return Response
     */
    public function delete(Request $request, string $id = ''): Response
    {
        // 尝试从路径获取ID，如果没有则从请求参数中获取
        if (empty($id)) {
            $id = $request->input('id');
        }

        if (!$id) {
            return json([
                'code' => 400,
                'msg' => '参数错误'
            ]);
        }

        // 验证行程是否存在
        $model = new ItineraryModel();
        $detail = $model->getDetail($id);
        if (!$detail) {
            return json([
                'code' => 404,
                'msg' => '行程不存在'
            ]);
        }

        // 删除行程
        $result = $model->where('id', $id)->delete();

        // 删除关联的行程安排
        $scheduleModel = new ItineraryScheduleModel();
        $scheduleModel->where('itinerary_id', $id)->delete();

        return json([
            'code' => 200,
            'msg' => '删除成功'
        ]);
    }

    /**
     * 获取行程安排列表
     * @param Request $request
     * @return Response
     */
    public function scheduleList(Request $request): Response
    {
        $itineraryId = $request->input('itineraryId');

        if (!$itineraryId) {
            return json([
                'code' => 400,
                'msg' => '参数错误'
            ]);
        }

        // 验证行程是否存在
        $itineraryModel = new ItineraryModel();
        $itinerary = $itineraryModel->getDetail($itineraryId);
        if (!$itinerary) {
            return json([
                'code' => 404,
                'msg' => '行程不存在'
            ]);
        }

        $model = new ItineraryScheduleModel();
        $list = $model->getSchedulesByItineraryId($itineraryId);

        return json([
            'code' => 0,
            'msg' => '获取成功',
            'data' => [
                'list' => $list,
                'itinerary' => $itinerary
            ]
        ]);
    }

    /**
     * 获取行程安排详情
     * @param Request $request
     * @return Response
     */
    public function scheduleDetail(Request $request): Response
    {
        $id = $request->input('id');

        if (!$id) {
            return json([
                'code' => 400,
                'msg' => '参数错误'
            ]);
        }

        $model = new ItineraryScheduleModel();
        $detail = $model->getDetail($id);

        if (!$detail) {
            return json([
                'code' => 404,
                'msg' => '行程安排不存在'
            ]);
        }

        // 获取所属行程信息
        $itineraryModel = new ItineraryModel();
        $itinerary = $itineraryModel->getDetail($detail['itinerary_id']);
        $detail['itinerary'] = $itinerary;

        return json([
            'code' => 0,
            'msg' => '获取成功',
            'data' => $detail
        ]);
    }

    /**
     * 保存行程安排
     * @param int $itineraryId 行程ID
     * @param array $schedules 行程安排数据
     * @return bool
     */
    private function saveSchedules(int $itineraryId, array $schedules): bool
    {
        if (empty($schedules)) {
            return false;
        }

        // 验证行程是否存在
        $itineraryModel = new ItineraryModel();
        $itinerary = $itineraryModel->getDetail($itineraryId);
        if (!$itinerary) {
            return false;
        }

        $scheduleModel = new ItineraryScheduleModel();

        // 先删除原有行程安排
        $scheduleModel->where('itinerary_id', $itineraryId)->delete();

        // 添加时间戳和行程ID
        $now = time();
        foreach ($schedules as &$schedule) {
            // 转换字段为下划线格式
            $schedule = $this->convertScheduleFieldsToDB($schedule);

            // 验证天数是否合法
            if (isset($schedule['day']) && ($schedule['day'] < 1 || $schedule['day'] > $itinerary['days'])) {
                continue; // 跳过无效的行程安排
            }

            $schedule['itinerary_id'] = $itineraryId;
            $schedule['create_time'] = $schedule['update_time'] = $now;
        }

        // 保存行程安排
        return $scheduleModel->batchSave($schedules);
    }

    /**
     * 更新单个行程安排
     * @param Request $request
     * @return Response
     */
    public function updateSchedule(Request $request): Response
    {
        $id = $request->input('id');
        $itineraryId = $request->input('itineraryId');

        if (!$id || !$itineraryId) {
            return json([
                'code' => 400,
                'msg' => '参数错误'
            ]);
        }

        $data = $request->post();
        unset($data['id'], $data['itineraryId']);

        // 转换字段为下划线格式
        $data = $this->convertScheduleFieldsToDB($data);

        // 验证行程是否存在
        $itineraryModel = new ItineraryModel();
        $itinerary = $itineraryModel->getDetail($itineraryId);
        if (!$itinerary) {
            return json([
                'code' => 404,
                'msg' => '行程不存在'
            ]);
        }

        // 验证行程安排是否存在
        $scheduleModel = new ItineraryScheduleModel();
        $schedule = $scheduleModel->getDetail($id);
        if (!$schedule) {
            return json([
                'code' => 404,
                'msg' => '行程安排不存在'
            ]);
        }

        // 验证行程安排是否属于该行程
        if ($schedule['itinerary_id'] != $itineraryId) {
            return json([
                'code' => 403,
                'msg' => '无权限修改此行程安排'
            ]);
        }

        // 验证天数是否合法
        if (isset($data['day']) && ($data['day'] < 1 || $data['day'] > $itinerary['days'])) {
            return json([
                'code' => 400,
                'msg' => '天数超出行程范围'
            ]);
        }

        // 添加更新时间
        $data['update_time'] = time();

        // 更新行程安排
        $result = $scheduleModel->where('id', $id)->update($data);

        return json([
            'code' => 0,
            'msg' => '更新成功'
        ]);
    }

    /**
     * 删除行程安排
     * @param Request $request
     * @return Response
     */
    public function deleteSchedule(Request $request): Response
    {
        $id = $request->input('id');
        $itineraryId = $request->input('itineraryId');

        if (!$id || !$itineraryId) {
            return json([
                'code' => 400,
                'msg' => '参数错误'
            ]);
        }

        // 验证行程是否存在
        $itineraryModel = new ItineraryModel();
        $itinerary = $itineraryModel->getDetail($itineraryId);
        if (!$itinerary) {
            return json([
                'code' => 404,
                'msg' => '行程不存在'
            ]);
        }

        // 验证行程安排是否存在
        $scheduleModel = new ItineraryScheduleModel();
        $schedule = $scheduleModel->getDetail($id);
        if (!$schedule) {
            return json([
                'code' => 404,
                'msg' => '行程安排不存在'
            ]);
        }

        // 验证行程安排是否属于该行程
        if ($schedule['itinerary_id'] != $itineraryId) {
            return json([
                'code' => 403,
                'msg' => '无权限删除此行程安排'
            ]);
        }

        // 删除行程安排
        $result = $scheduleModel->where('id', $id)->delete();

        return json([
            'code' => 0,
            'msg' => '删除成功'
        ]);
    }


    /**
     * 将小驼峰转换为下划线格式（行程安排字段）
     * @param array $data
     * @return array
     */
    private function convertScheduleFieldsToDB(array $data): array
    {
        $result = [];
        $map = [
            'itineraryId' => 'itinerary_id',
            'meetingPoint' => 'meeting_point',
            'meetingTime' => 'meeting_time',
            'startTime' => 'start_time',
            'mealType' => 'meal_type',
            'adultMealIncluded' => 'adult_meal_included',
            'childMealIncluded' => 'child_meal_included',
            'mealPrice' => 'meal_price',
            'transportType' => 'transport_type',
            'flightNumber' => 'flight_number',
            'departureCity' => 'departure_city',
            'arrivalCity' => 'arrival_city',
            'stayType' => 'stay_type',
            'hotelName' => 'hotel_name',
            'createTime' => 'create_time',
            'updateTime' => 'update_time',
            'deleteTime' => 'delete_time'
        ];

        foreach ($data as $key => $value) {
            if ($key === 'mealDuration' && is_array($value)) {
                // 处理嵌套对象 mealDuration
                $result['meal_duration_hours'] = $value['hours'] ?? 0;
                $result['meal_duration_minutes'] = $value['minutes'] ?? 0;
            } else if ($key === 'estimatedDuration' && is_array($value)) {
                // 处理嵌套对象 estimatedDuration
                $result['estimated_duration_hours'] = $value['hours'] ?? 0;
                $result['estimated_duration_minutes'] = $value['minutes'] ?? 0;
            } else if ($key === 'activityDuration' && is_array($value)) {
                // 处理嵌套对象 activityDuration
                $result['activity_duration_hours'] = $value['hours'] ?? 0;
                $result['activity_duration_minutes'] = $value['minutes'] ?? 0;
            } else if (isset($map[$key])) {
                $result[$map[$key]] = $value;
            } else {
                $result[$key] = $value;
            }
        }

        return $result;
    }

    /**
     * 上架行程
     * @param Request $request
     * @param string $id
     * @return Response
     */
    public function online(Request $request, string $id = ''): Response
    {
        // 尝试从路径获取ID，如果没有则从请求参数中获取
        if (empty($id)) {
            $id = $request->input('id');
        }

        if (!$id) {
            return error(400, '参数错误');
        }

        // 验证行程是否存在
        $detail = ItineraryModel::mk()->where('id', $id)->find();
        if (!$detail) {
            return error(400, '行程不存在');
        }

        // 更新状态
        $detail->save([
            'status' => 'online',
        ]);

        return success([
            'id' => $detail['id'],
            'status' => 'online',
        ], '上架成功');
    }

    /**
     * 下架行程
     * @param Request $request
     * @param string $id
     * @return Response
     */
    public function offline(Request $request, string $id = ''): Response
    {
        // 尝试从路径获取ID，如果没有则从请求参数中获取
        if (empty($id)) {
            $id = $request->input('id');
        }

        if (!$id) {
            return error(400, '参数错误');
        }

        // 验证行程是否存在
        // 验证行程是否存在
        $detail = ItineraryModel::mk()->where('id', $id)->find();
        if (!$detail) {
            return error(400, '行程不存在');
        }
        // 更新状态
        $detail->save([
            'status' => 'offline',
        ]);

        return success([], '上架成功');
    }

    /**
     * 批量删除行程
     * @param Request $request
     * @return Response
     */
    public function batchDelete(Request $request): Response
    {
        $ids = $request->post('ids', []);

        if (empty($ids) || !is_array($ids)) {
            return json([
                'code' => 400,
                'msg' => '参数错误'
            ]);
        }

        $model = new ItineraryModel();
        $scheduleModel = new ItineraryScheduleModel();

        // 删除行程
        $result = $model->whereIn('id', $ids)->delete();

        // 删除关联的行程安排
        $scheduleModel->whereIn('itinerary_id', $ids)->delete();

        return json([
            'code' => 0,
            'msg' => '批量删除成功'
        ]);
    }

    /**
     * 批量上架行程
     * @param Request $request
     * @return Response
     */
    public function batchOnline(Request $request): Response
    {
        $ids = $request->post('ids', []);

        if (empty($ids) || !is_array($ids)) {
            return json([
                'code' => 400,
                'msg' => '参数错误'
            ]);
        }

        $model = new ItineraryModel();

        // 更新状态
       $model->whereIn('id', $ids)->update([
            'status' => 'online'
        ]);

        return success();
    }

    /**
     * 批量下架行程
     * @param Request $request
     * @return Response
     */
    public function batchOffline(Request $request): Response
    {
        $ids = $request->post('ids', []);

        if (empty($ids) || !is_array($ids)) {
            return json([
                'code' => 400,
                'msg' => '参数错误'
            ]);
        }

        $model = new ItineraryModel();

        // 更新状态
         $model->whereIn('id', $ids)->update([
            'status' => 'offline',
            'update_time' => time()
        ]);

        return success();
    }

    /**
     * 将数据库格式转换为前端格式
     * @param array $data
     * @return array
     */
    private function convertToFrontend(array $data): array
    {
        $result = [];
        $map = [
            'original_price' => 'originalPrice',
            'departure_city' => 'departureCity',
            'product_type' => 'productType',
            'cover_image' => 'coverImage',
            'travel_mode' => 'travelMode',
            'group_type' => 'groupType',
            'min_group_size' => 'minGroupSize',
            'max_group_size' => 'maxGroupSize',
            'collect_type' => 'collectType',
            'promotion_price' => 'promotionPrice',
            'category_id' => 'categoryId',
            'category_path' => 'categoryPath',
            'valid_from' => 'validFrom',
            'valid_to' => 'validTo',
            'is_long_term' => 'isLongTerm',
            'create_time' => 'createdTime',
            'update_time' => 'updatedTime',
            'delete_time' => 'deleteTime',
            'single_room_surcharge' => 'singleRoomSurcharge',
            'room_upgrade_surcharge' => 'roomUpgradeSurcharge',

            // 交易规则相关字段
            'consumable_date_type' => 'consumableDateType',
            'consumable_days' => 'consumableDays',
            'consumable_dates' => 'consumableDates',

            // 限购规则
            'purchase_limit_single' => 'purchaseLimitSingle',
            'purchase_limit_per_order' => 'purchaseLimitPerOrder',
            'purchase_limit_rules' => 'purchaseLimitRules',
            'purchase_limit_details' => 'purchaseLimitDetails',

            // 预约规则
            'earliest_booking_days' => 'earliestBookingDays',
            'advance_booking_required' => 'advanceBookingRequired',
            'advance_booking_range' => 'advanceBookingRange',
            'advance_booking_time' => 'advanceBookingTime',
            'advance_booking_time_unit' => 'advanceBookingTimeUnit',

            // 取消规则
            'has_no_cancellation_after_appointment' => 'hasNoCancellationAfterAppointment',
            'refund_before_appointment' => 'refundBeforeAppointment',
            'auto_refund_after_expiration' => 'autoRefundAfterExpiration',
            'has_breach_of_contract_terms' => 'hasBreachOfContractTerms',
            'breach_of_contract_terms' => 'breachOfContractTerms',
            'breach_of_contract_base' => 'breachOfContractBase'
        ];

        foreach ($data as $key => $value) {
            if (isset($map[$key])) {
                $result[$map[$key]] = $value;
            } else {
                $result[$key] = $value;
            }
        }

        // 处理时间戳转为ISO格式
        if (isset($result['createdTime']) && is_numeric($result['createdTime'])) {
            $result['createdTime'] = date('c', $result['createdTime']);
        }
        if (isset($result['updatedTime']) && is_numeric($result['updatedTime'])) {
            $result['updatedTime'] = date('c', $result['updatedTime']);
        }

        // 处理布尔值字段
        $booleanFields = [
            'isLongTerm', 'purchaseLimitSingle', 'purchaseLimitRules', 'advanceBookingRequired',
            'hasNoCancellationAfterAppointment', 'refundBeforeAppointment',
            'autoRefundAfterExpiration', 'hasBreachOfContractTerms'
        ];

        foreach ($booleanFields as $field) {
            if (isset($result[$field])) {
                $result[$field] = (bool)$result[$field];
            }
        }

        // 处理JSON字段
        if (isset($result['categoryPath']) && is_string($result['categoryPath'])) {
            $result['categoryPath'] = json_decode($result['categoryPath'], true);
        }

        if (isset($result['features']) && is_string($result['features'])) {
            $result['features'] = json_decode($result['features'], true);
        }

        if (isset($result['images']) && is_string($result['images'])) {
            $result['images'] = json_decode($result['images'], true);
        }

        if (isset($result['imageDescription']) && is_string($result['imageDescription'])) {
            $result['imageDescription'] = json_decode($result['imageDescription'], true);
        }

        if (isset($result['consumableDates']) && is_string($result['consumableDates'])) {
            $result['consumableDates'] = json_decode($result['consumableDates'], true);
        }

        if (isset($result['breachOfContractTerms']) && is_string($result['breachOfContractTerms'])) {
            $result['breachOfContractTerms'] = json_decode($result['breachOfContractTerms'], true);
        }

        // 行程信息
        $result['destinationText'] = CitiesModel::getCityInfoByIds($result['destination']);
        $result['departureCityText'] = CityInfoModel::getCityInfoByIds($result['departureCity']);

        return $result;
    }

    /**
     * 将行程安排从数据库格式转换为前端格式
     * @param array $data
     * @return array
     */
    private function convertScheduleToFrontend(array $data): array
    {
        $result = [];
        $map = [
            'itinerary_id' => 'itineraryId',
            'meeting_point' => 'meetingPoint',
            'meeting_time' => 'meetingTime',
            'start_time' => 'startTime',
            'meal_type' => 'mealType',
            'adult_meal_included' => 'adultMealIncluded',
            'child_meal_included' => 'childMealIncluded',
            'meal_price' => 'mealPrice',
            'transport_type' => 'transportType',
            'flight_number' => 'flightNumber',
            'departure_city' => 'departureCity',
            'arrival_city' => 'arrivalCity',
            'stay_type' => 'stayType',
            'hotel_name' => 'hotelName',
            'create_time' => 'createTime',
            'update_time' => 'updateTime',
            'delete_time' => 'deleteTime'
        ];

        foreach ($data as $key => $value) {
            if ($key === 'meal_duration_hours' || $key === 'meal_duration_minutes') {
                // 跳过这些字段，在下面处理
                continue;
            } else if ($key === 'estimated_duration_hours' || $key === 'estimated_duration_minutes') {
                // 跳过这些字段，在下面处理
                continue;
            } else if ($key === 'activity_duration_hours' || $key === 'activity_duration_minutes') {
                // 跳过这些字段，在下面处理
                continue;
            } else if (isset($map[$key])) {
                $result[$map[$key]] = $value;
            } else {
                $result[$key] = $value;
            }
        }

        // 处理嵌套对象
        if (isset($data['meal_duration_hours']) || isset($data['meal_duration_minutes'])) {
            $result['mealDuration'] = [
                'hours' => intval($data['meal_duration_hours'] ?? 0),
                'minutes' => intval($data['meal_duration_minutes'] ?? 0)
            ];
        }

        if (isset($data['estimated_duration_hours']) || isset($data['estimated_duration_minutes'])) {
            $result['estimatedDuration'] = [
                'hours' => intval($data['estimated_duration_hours'] ?? 0),
                'minutes' => intval($data['estimated_duration_minutes'] ?? 0)
            ];
        }

        if (isset($data['activity_duration_hours']) || isset($data['activity_duration_minutes'])) {
            $result['activityDuration'] = [
                'hours' => intval($data['activity_duration_hours'] ?? 0),
                'minutes' => intval($data['activity_duration_minutes'] ?? 0)
            ];
        }

        return $result;
    }

    /**
     * 获取分类树结构
     * @param Request $request
     */
    public function tree(Request $request)
    {
        try {
            $model = new ProductCategoryModel();

            // 获取所有分类，然后构建树形结构
            $categories = $model->where([
                'delete_time' => 0
            ])->select();

            // 组织分类范围信息
            $categoryScopeInfo = [];
            foreach ($categories as $category) {
                if (!empty($category['allow_access_scope']) || !empty($category['no_access_scope']) || !empty($category['detail_url'])) {
                    $categoryScopeInfo[$category['category_id']] = [
                        'allow_access_product_scope' => $category['allow_access_scope'] ?? '',
                        'no_access_product_scope' => $category['no_access_scope'] ?? '',
                        'detail_url' => $category['detail_url'] ?? '暂无'
                    ];
                }
            }

            // 构建树形结构
            $categoryTreeList = $this->buildCategoryTree($categories, 0);

            // 构建响应数据
            $responseData = [
                'BaseResp' => [
                    'StatusCode' => 0,
                    'StatusMessage' => ''
                ],
                'category_scope_info' => $categoryScopeInfo,
                'category_tree_list' => $categoryTreeList,
                'status_code' => 0,
                'status_msg' => '',
                'now' => time() * 1000,
                'hit_grey' => true,
                'is_travel_history_good' => false
            ];

            return success($responseData);
        } catch (\Exception $e) {
            return json([
                'BaseResp' => [
                    'StatusCode' => 1,
                    'StatusMessage' => $e->getMessage()
                ],
                'status_code' => 1,
                'status_msg' => '获取失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 构建分类树结构
     * @param   $categories
     * @param int $parentId 父级ID
     * @return array
     */
    private function buildCategoryTree(  $categories, int $parentId): array
    {
        $tree = [];

        foreach ($categories as $category) {
            if ($category['parent_id'] == $parentId) {
                $item = [
                    'ParentId' => $parentId,
                    'category_id' => $category['category_id'],
                    'category_name' => $category['category_name'],
                    'access_status' => $category['access_status'],
                    'category_discard' => (bool)$category['category_discard']
                ];

                // 添加可选字段
                if (!empty($category['price_limit'])) {
                    $item['price_limit'] = $category['price_limit'];
                }

                // 如果状态是受限，添加拒绝原因
                if ($category['access_status'] == 2) {
                    $item['reject_reason'] = [
                        'reject_title' => 'category_need_merchant_category',
                        'reject_chinese_title' => '当前类目未开通',
                        'reject_content' => [
                            '如需创建商品,请新增辅营类目或修改主营类目',
                            '建议新增经营类目：' . $category['category_name']
                        ]
                    ];
                }

                // 递归添加子分类
                $children = $this->buildCategoryTree($categories, $category['category_id']);
                $item['sub_category_list'] = $children;

                $tree[] = $item;
            }
        }

        return $tree;
    }

    /**
     * 更新线路主题
     * @param Request $request
     * @param string $id
     * @return Response
     */
    public function updateThemes(Request $request, string $id = ''): Response
    {
        // 尝试从路径获取ID，如果没有则从请求参数中获取
        if (empty($id)) {
            $id = $request->input('id');
        }

        if (!$id) {
            return error(400, '参数错误');
        }

        // 获取主题ID数组
        $themeIds = $request->post('themeIds', []);

        // 验证线路是否存在
        $itinerary = ItineraryModel::mk()->where('id', $id)->find();
        if (!$itinerary) {
            return error(404, '线路不存在');
        }

        // 将主题ID数组转换为逗号分隔的字符串
        $themesIdStr = '';
        if (!empty($themeIds)) {
            if (is_array($themeIds)) {
                $themesIdStr = implode(',', $themeIds);
            } else {
                $themesIdStr = (string)$themeIds;
            }
        }

        // 更新线路的主题
        $result = $itinerary->save([
            'themes_ids' => $themesIdStr,
        ]);

        if ($result) {
            return success(['id' => $id], '更新主题成功');
        } else {
            return error(500, '更新主题失败');
        }
    }
}
