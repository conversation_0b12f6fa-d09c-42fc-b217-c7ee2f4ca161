<?php

namespace app\admin\controller;

use app\model\SystemConfig;
use app\model\SystemConfigGroup;
use support\Request;
use support\Response;

class SystemConfigGroupController extends BaseController
{
    /** 创建分组 */
    public function store(Request $request): Response
    {
        try {
            $data = $this->getInput($request);
            $errors = $this->validateRequired($data, ['group_name']);
            if ($errors) { return error('参数验证失败', 400, $errors); }

            // 唯一性检查
            if (SystemConfigGroup::where('group_name', $data['group_name'])->find()) {
                return error('分组名称已存在');
            }

            $now = time();
            $group = SystemConfigGroup::create([
                'group_name' => $data['group_name'],
                'group_description' => $data['group_description'] ?? null,
                'sort_order' => (int)($data['sort_order'] ?? 0),
                'created_at' => $now,
                'updated_at' => $now,
            ]);

            return success(['group' => $group]);
        } catch (\Exception $e) {
            return $this->handleException($e, '创建配置分组');
        }
    }

    /** 分组列表 */
    public function index(Request $request): Response
    {
        try {
            $groups = SystemConfigGroup::order('sort_order', 'asc')->order('id', 'asc')->select();
            $list = [];
            foreach ($groups as $g) {
                $list[] = [
                    'id' => $g->id,
                    'group_name' => $g->group_name,
                    'group_description' => $g->group_description,
                    'sort_order' => (int)$g->sort_order,
                    'config_count' => SystemConfig::where('group_id', $g->id)->count(),
                    'created_at' => $g->created_at,
                    'updated_at' => $g->updated_at,
                ];
            }
            return success(['groups' => $list, 'total' => count($list)]);
        } catch (\Exception $e) {
            return $this->handleException($e, '获取配置分组列表');
        }
    }

    /** 更新分组 */
    public function update(Request $request, $id): Response
    {
        try {
            if (!$id) { return error('分组ID不能为空'); }
            $data = $this->getInput($request);

            // 若更名需校验唯一
            if (!empty($data['group_name'])) {
                $exists = SystemConfigGroup::where('group_name', $data['group_name'])->where('id', '<>', $id)->find();
                if ($exists) { return error('分组名称已存在'); }
            }

            $data['updated_at'] = time();
            SystemConfigGroup::where('id', $id)->update($data);
            return success(['id' => (int)$id], '更新成功');
        } catch (\Exception $e) {
            return $this->handleException($e, '更新配置分组');
        }
    }

    /** 删除分组（需检查是否有关联配置项） */
    public function destroy(Request $request, $id): Response
    {
        try {
            if (!$id) { return error('分组ID不能为空'); }
            $count = SystemConfig::where('group_id', $id)->count();
            if ($count > 0) { return error('该分组下仍有关联的配置项，无法删除'); }
            SystemConfigGroup::where('id', $id)->delete();
            return success([], '删除成功');
        } catch (\Exception $e) {
            return $this->handleException($e, '删除配置分组');
        }
    }

    /** 指定分组下的配置项列表 */
    public function items(Request $request, $id): Response
    {
        try {
            if (!$id) { return error('分组ID不能为空'); }
            $items = SystemConfig::where('group_id', $id)->order(['sort_order','config_key'])->select();
            $list = [];
            foreach ($items as $c) { $list[] = $c->getSummaryInfo(); }
            return success(['configs' => $list, 'total' => count($list)]);
        } catch (\Exception $e) {
            return $this->handleException($e, '获取分组配置项');
        }
    }
}

