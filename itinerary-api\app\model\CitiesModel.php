<?php
namespace app\model;

use base\base\BaseModel;
use support\Cache;

class CitiesModel extends BaseModel
{
    /**
     * 境外城市
     * 与模型关联的表名
     *
     * @var string
     */
    protected $name = 'cities';

    /**
     * 获取所有城市信息
     *
     * @return array
     */
    public static function getAllCityInfo()
    {
        // 优先获取缓存
        $key = 'cities';
        $cache = Cache::get($key);
        if ($cache) {
            return json_decode($cache, true);
        }
        $data = self::select()->toArray();
        if (!empty($data)) {
            Cache::set($key, json_encode($data));
        }
        return $data;
    }

    /**
     * 通过codes获取城市信息
     *
     * @param mixed $codes
     * @return string
     */
    public static function getCityInfoByIds(mixed $codes)
    {
        if (!is_array($codes)) {
            $codes = explode(',', $codes);
        }

        $allCity = array_column(self::getAllCityInfo(), 'name', 'cityCode');
        if (!array_intersect($codes, array_keys($allCity))) {
            return CityInfoModel::getCityInfoByIds($codes);
        }

        $city = [];
        foreach ($codes as $code) {
            $city[] = $allCity[$code] ?? '';
        }
        return trim(implode(',', $city), ',');
    }
}
