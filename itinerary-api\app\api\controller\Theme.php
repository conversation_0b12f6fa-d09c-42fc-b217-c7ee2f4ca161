<?php

namespace app\api\controller;

use app\model\ItineraryThemeModel;
use support\Request;
use support\Response;

class Theme
{
    /**
     * 获取线路主题列表
     * @param Request $request
     * @return Response
     */
    public function list(Request $request): Response
    {
        // 获取参数
        $page = max(1, (int)$request->input('page', 1));
        $pageSize = min(50, max(1, (int)$request->input('pageSize', 10)));
        $keyword = $request->input('keyword', '');

        $where = [];

        // 关键词搜索
        if (!empty($keyword)) {
            $where[] = ['name', 'like', "%{$keyword}%"];
        }

        // 查询数据
        $themeModel = ItineraryThemeModel::mk();
        $query = $themeModel->where($where);

        $total = $query->count();
        $list = $query->page($page, $pageSize)
            ->order('id', 'desc')
            ->append(['id'])
            ->select()
            ->toArray();

        // 构建返回数据
        $result = [
            'list' => $list,
            'total' => $total,
            'page' => $page,
            'pageSize' => $pageSize,
        ];

        return json(['code' => 200, 'msg' => 'success', 'data' => $result]);
    }

    /**
     * 获取线路主题详情
     * @param int $id
     * @return Response
     */
    public function detail($id): Response
    {
        $id = (int)$id;
        $theme = ItineraryThemeModel::mk()->append(['id'])->find($id);

        if (!$theme) {
            return json(['code' => 404, 'msg' => '主题不存在']);
        }

        return json(['code' => 200, 'msg' => 'success', 'data' => $theme]);
    }

    /**
     * 创建线路主题
     * @param Request $request
     * @return Response
     */
    public function create(Request $request): Response
    {
        $input = $request->all();

        // 验证参数
        if (empty($input['name'])) {
            return json(['code' => 400, 'msg' => '主题名称不能为空']);
        }

        // 检查名称是否已存在
        $exists = ItineraryThemeModel::mk()->append(['id'])->where('name', $input['name'])->find();
        if ($exists) {
            return json(['code' => 400, 'msg' => '主题名称已存在']);
        }

        // 创建数据
        $data = [
            'name' => $input['name'],
            'icon' => $input['icon'] ?? '',
            'image_url' => $input['imageUrl'] ?? '',
        ];

        $id = ItineraryThemeModel::mk()->save($data);

        return json(['code' => 200, 'msg' => 'success', 'data' => ['id' => $id]]);
    }

    /**
     * 更新线路主题
     * @param Request $request
     * @param int $id
     * @return Response
     */
    public function update(Request $request, $id): Response
    {
        $id = (int)$id;
        $input = $request->all();

        // 验证主题是否存在
        $theme = ItineraryThemeModel::mk()->append(['id'])->find($id);
        if (!$theme) {
            return json(['code' => 404, 'msg' => '主题不存在']);
        }

        // 构建更新数据
        $data = [];

        if (isset($input['name'])) {
            if (empty($input['name'])) {
                return json(['code' => 400, 'msg' => '主题名称不能为空']);
            }

            // 检查名称是否已被其他主题使用
            $exists = ItineraryThemeModel::mk()->where('name', $input['name'])
                ->where('id', '<>', $id)
                ->append(['id'])
                ->find();
            if ($exists) {
                return json(['code' => 400, 'msg' => '主题名称已存在']);
            }

            $data['name'] = $input['name'];
        }

        if (isset($input['icon'])) {
            $data['icon'] = $input['icon'];
        }

        if (isset($input['imageUrl'])) {
            $data['image_url'] = $input['imageUrl'];
        }

        if (empty($data)) {
            return json(['code' => 400, 'msg' => '没有需要更新的数据']);
        }

        $data['update_time'] = time();

        // 更新数据
        $result = ItineraryThemeModel::mk()->where('id', $id)->update($data);

        return json(['code' => 200, 'msg' => 'success', 'data' => (bool)$result]);
    }

    /**
     * 删除线路主题
     * @param int $id
     * @return Response
     */
    public function delete($id): Response
    {
        $id = (int)$id;

        // 验证主题是否存在
        $theme = ItineraryThemeModel::mk()->append(['id'])->find($id);
        if (!$theme) {
            return json(['code' => 404, 'msg' => '主题不存在']);
        }

        // 执行删除操作
        $result = ItineraryThemeModel::mk()->where('id', $id)->delete();

        return json(['code' => 200, 'msg' => 'success', 'data' => (bool)$result]);
    }

    /**
     * 获取所有主题（不分页）
     * @return Response
     */
    public function all(): Response
    {
        $list = ItineraryThemeModel::mk()
            ->order('id', 'desc')
            ->append(['id'])
            ->select()
            ->toArray();

        return json(['code' => 200, 'msg' => 'success', 'data' => $list]);
    }
} 