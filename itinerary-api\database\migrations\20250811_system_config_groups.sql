-- 系统配置分组表
CREATE TABLE IF NOT EXISTS `system_config_groups` (
  `id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
  `group_name` VARCHAR(100) NOT NULL COMMENT '分组名称',
  `group_description` VARCHAR(255) DEFAULT NULL COMMENT '分组描述',
  `sort_order` INT NOT NULL DEFAULT 0 COMMENT '排序权重',
  `created_at` INT UNSIGNED NOT NULL DEFAULT 0,
  `updated_at` INT UNSIGNED NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_group_name` (`group_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统配置分组';

-- 为系统配置表添加 group_id 字段（如不存在）
ALTER TABLE `system_configs`
  ADD COLUMN IF NOT EXISTS `group_id` INT UNSIGNED NULL DEFAULT NULL COMMENT '配置分组ID' AFTER `config_group`;

-- 可选：为 group_id 建索引
ALTER TABLE `system_configs`
  ADD INDEX IF NOT EXISTS `idx_group_id` (`group_id`);

