<?php

namespace app\website\controller;

use base\auth\Auth;
use base\auth\AuthHttpClient;
use DI\Attribute\Inject;
use support\Request;
use support\Response;

class Oidc
{
    #[Inject]
    private AuthHttpClient $client;

    public function verify(Request $request): Response
    {
       $code = $request->post('code');
       return success($this->client->verify($code));
    }

    #[Auth('booking:oidc:userinfo')]
    public function userinfo(): Response
    {
        return success(userinfo()->toArray());
    }


    public function logout(): Response
    {
        return success($this->client->logout());
    }

    public function refreshToken(): Response
    {
        return success($this->client->refreshToken()) ;
    }

    public function codes()
    {
        return success([]);
    }
}