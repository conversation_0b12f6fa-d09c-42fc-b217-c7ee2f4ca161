<?php
namespace app\website\controller;

use support\Cache;
use support\Request;
use app\model\CityInfoModel;
use support\Response;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\facade\Db;

class CityInfo
{
    /**
     * 获取城市列表（按省份分组）
     *
     * @param Request $request
     * @return Response
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function json(Request $request)
    {
        // 定义缓存键
        $cacheKey = 'city_info_json';
        
        // 检查缓存是否存在
        if (Cache::has($cacheKey)) {
            return success(Cache::get($cacheKey));
        }

        // 获取所有城市数据
        $cityInfos = CityInfoModel::mk()
            ->field(['province_name', 'province_code', 'city_name', 'city_code', 'city_initial'])
            ->select();
        
        // 按省份分组处理数据
        $result = [];
        $provinceMap = [];
        
        foreach ($cityInfos as $cityInfo) {
            $provinceCode = $cityInfo->province_code;
            $provinceName = $cityInfo->province_name;
            $cityInitial = strtoupper($cityInfo->city_initial);
            
            // 如果省份不存在，则添加新的省份
            if (!isset($provinceMap[$provinceCode])) {
                $provinceData = [
                    'code' => $provinceCode,
                    'name' => $provinceName,
                    'children' => []
                ];
                $result[] = $provinceData;
                $provinceMap[$provinceCode] = count($result) - 1;
            }
            
            // 添加城市到对应省份的children中
            $cityData = [
                'code' => $cityInfo->city_code,
                'name' => $cityInfo->city_name,
                'index' => $cityInitial
            ];
            $result[$provinceMap[$provinceCode]]['children'][] = $cityData;
        }
        
        // 将结果存入缓存（缓存7天）
        Cache::set($cacheKey, $result, 86400 * 7);
        
        return success($result);
    }

    /**
     * 国外城市信息（按国家分组）
     * 
     * @param Request $request
     * @return Response
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function overseas(Request $request)
    {
        // 定义缓存键
        $cacheKey = 'city_info_overseas';
        
        // 检查缓存是否存在
        if (Cache::has($cacheKey)) {
            return success(Cache::get($cacheKey));
        }
        
        // 缓存不存在，查询数据库
        // 获取所有国家及其城市数据
        $countries = Db::table('countries')
            ->alias('c')
            ->field([
                'c.id as country_id', 
                'c.name as country_name', 
                'c.name_en as country_name_en', 
                'c.initial as country_initial'
            ])
            ->select();
        
        $result = [];
        $countryMap = [];
        
        // 构建国家层级
        foreach ($countries as $country) {
            $countryData = [
                'code' => $country['country_id'],
                'name' => $country['country_name']  . "({$country['country_name_en']})",
                'children' => []
            ];
            
            $result[] = $countryData;
            $countryMap[$country['country_id']] = count($result) - 1;
        }
        
        // 获取并关联城市数据
        $cities = Db::table('cities')
            ->field([
                'id', 
                'country_id',
                'name', 
                'name_en',
                'city_code',
                'poi_id', 
                'initial'
            ])
            ->select();
        
        // 将城市添加到对应国家下
        foreach ($cities as $city) {
            $countryId = $city['country_id'];
            
            // 如果国家存在于结果集中
            if (isset($countryMap[$countryId])) {
                $cityInitial = strtoupper($city['initial'] ?: substr($city['name'], 0, 1));
                
                $cityData = [
                    'name' => $city['name'] . "({$city['name_en']})",
                    'code' => $city['city_code'],
                    'index' => $cityInitial
                ];
                
                $result[$countryMap[$countryId]]['children'][] = $cityData;
            }
        }
        
        // 排序：先按国家首字母排序，再对每个国家下的城市按首字母排序
        foreach ($result as &$country) {
            usort($country['children'], function($a, $b) {
                return $a['index'] <=> $b['index'];
            });
        }
        
        // 将结果存入缓存（缓存1天）
        Cache::set($cacheKey, $result, 86400 * 7);
        
        return success($result);
    }

    /**
     * 获取出发城市列表（按省份分组，与json方法格式保持一致）
     *
     * @param Request $request
     * @return Response
     */
    public function departureCitiesJson(Request $request): Response
    {
        // 获取缓存的出发城市数据
        $cacheKey = 'departure_cities';
        
        if (!Cache::has($cacheKey)) {
            return success();
        }

        $cityInfos = Cache::get($cacheKey, []);
        
        // 按省份分组处理数据
        $result = [];
        $provinceMap = [];

        foreach ($cityInfos as $cityInfo) {
            // 跳过境外城市
            if (isset($cityInfo['isOverseas']) && $cityInfo['isOverseas']) {
                continue;
            }
            
            $provinceCode = $cityInfo['provinceCode'] ?? '';
            $provinceName = $cityInfo['provinceName'] ?? '';
            $cityInitial = isset($cityInfo['cityInitial']) ? strtoupper($cityInfo['cityInitial']) : '';
            
            if (empty($provinceCode) || empty($provinceName)) {
                continue;
            }
            
            // 如果省份不存在，则添加新的省份
            if (!isset($provinceMap[$provinceCode])) {
                $provinceData = [
                    'code' => $provinceCode,
                    'name' => $provinceName,
                    'children' => []
                ];
                $result[] = $provinceData;
                $provinceMap[$provinceCode] = count($result) - 1;
            }
            
            // 添加城市到对应省份的children中
            $cityData = [
                'code' => $cityInfo['cityCode'],
                'name' => $cityInfo['cityName'],
                'index' => $cityInitial,
                'count' => $cityInfo['count'] ?? 0  // 保留count信息，可用于前端展示热门程度
            ];
            $result[$provinceMap[$provinceCode]]['children'][] = $cityData;
        }
        
        // 对每个省份内的城市按出现次数（热度）排序
        foreach ($result as &$province) {
            usort($province['children'], function($a, $b) {
                return $b['count'] - $a['count'];
            });
        }
        
        return success($result);
    }

    /**
     * 获取所有线路目标城市列表（不区分境内外）
     *
     * @param Request $request
     * @return Response
     */
    public function destinationCities(Request $request): Response
    {
        // 获取缓存中的目的地数据
        $destinations = Cache::get('destinations', []);
        
        if (empty($destinations)) {
            return success([]);
        }
        
        // 按首字母分组处理数据
        $result = [];
        
        foreach ($destinations as $destination) {
            if (empty($destination['cityName']) || empty($destination['cityCode'])) {
                continue;
            }
            
            $cityInitial = isset($destination['cityInitial']) ? strtoupper($destination['cityInitial']) : 
                           (isset($destination['cityName']) ? strtoupper(substr($destination['cityName'], 0, 1)) : 'A');
            
            // 确保首字母是有效的英文字母
            if (!preg_match('/^[A-Z]$/', $cityInitial)) {
                $cityInitial = 'A';
            }
            
            // 如果该首字母分组不存在，创建它
            if (!isset($result[$cityInitial])) {
                $result[$cityInitial] = [];
            }
            
            // 添加城市到相应的首字母分组
            $result[$cityInitial][] = [
                'code' => $destination['cityCode'],
                'name' => $destination['cityName'],
                'isOverseas' => $destination['isOverseas'] ?? false,
                'count' => $destination['count'] ?? 0  // 保留count信息，可用于排序
            ];
        }
        
        // 对每个首字母组内的城市按出现次数（热度）排序
        foreach ($result as &$cities) {
            usort($cities, function($a, $b) {
                return $b['count'] - $a['count'];
            });
        }
        
        // 按首字母顺序排序分组
        ksort($result);
        
        return success($result);
    }
    
    /**
     * 获取所有目的地，并按区域分类（包括国内和国际）
     *
     * @param Request $request
     * @return Response
     */
    public function destinationsByRegion(Request $request): Response
    {
        // 获取缓存中按区域分类的目的地数据
        $destinationsByRegion = Cache::get('destinations_by_region', []);
        
        if (empty($destinationsByRegion)) {
            return success([]);
        }
        
        return success($destinationsByRegion);
    }

    /**
     * 获取按省份或国家分组的目的地城市列表（不区分国内外）
     *
     * @param Request $request
     * @return Response
     */
    public function destinationCitiesByProvince(Request $request): Response
    {
        // 获取缓存中的目的地数据
        $destinations = Cache::get('destinations', []);
        
        if (empty($destinations)) {
            return success([]);
        }
        
        // 按省份/国家分组处理数据
        $regionGroups = []; // 统一处理省份和国家分组
        
        foreach ($destinations as $destination) {
            if (empty($destination['cityName']) || empty($destination['cityCode'])) {
                continue;
            }
            
            // 构建目的地基本数据
            $destData = [
                'code' => $destination['cityCode'],
                'name' => $destination['cityName'],
                'count' => $destination['count'] ?? 0,
                'index' => $destination['index'],
                'isOverseas' => $destination['isOverseas'] ?? false
            ];
            
            // 确定分组键（省份或国家）
            if (isset($destination['isOverseas']) && $destination['isOverseas']) {
                // 境外目的地，使用国家作为分组键
                $regionName = $destination['countryName'] ?? '其他';
                $regionCode = $destination['countryCode'] ?? '';
            } else {
                // 国内目的地，使用省份作为分组键
                $regionName = $destination['provinceName'] ?? '其他';
                $regionCode = $destination['provinceCode'] ?? '';
            }
            
            // 确保分组名称非空
            if (empty($regionName)) {
                $regionName = '其他';
            }
            
            // 创建或更新分组
            if (!isset($regionGroups[$regionName])) {
                $regionGroups[$regionName] = [
                    'code' => $regionCode,
                    'name' => $regionName,
                    'isOverseas' => $destination['isOverseas'] ?? false,
                    'index' => $destination['index'],
                    'children' => []
                ];
            }
            
            // 添加目的地到相应分组
            $regionGroups[$regionName]['children'][] = $destData;
        }
        
        // 对每个省份/国家内的城市按出现次数（热度）排序
        foreach ($regionGroups as &$region) {
            usort($region['children'], function($a, $b) {
                return $b['count'] - $a['count'];
            });
        }
        
        // 将分组数据转为索引数组
        $result = array_values($regionGroups);
        
        // 对分组按省份/国家名称字母顺序排序
        usort($result, function($a, $b) {
            return strcmp($a['name'], $b['name']);
        });
        
        return success($result);
    }
} 