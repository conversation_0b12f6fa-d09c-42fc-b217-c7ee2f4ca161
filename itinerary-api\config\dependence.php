<?php
/**
 * This file is part of webman.
 *
 * Licensed under The MIT License
 * For full copyright and license information, please see the MIT-LICENSE.txt
 * Redistributions of files must retain the above copyright notice.
 *
 * <AUTHOR>
 * @copyright walkor<<EMAIL>>
 * @link      http://www.workerman.net/
 * @license   http://www.opensource.org/licenses/mit-license.php MIT License
 */

use GuzzleHttp\Client;

return [
    Client::class          => new  Client([
        'verify'  => false,
        'timeout' => 3,
        'http_errors' => false,
    ]),
    'iam_api_domain' => env('AUTH.IAM_API_DOMAIN', 'http://localhost:10000'),
    'appid' => env('AUTH.APPID'),
    'app_secret' => env('AUTH.SECRET'),
];