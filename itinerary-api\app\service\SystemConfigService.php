<?php

namespace app\service;

use app\model\SystemConfig;
use support\think\Db;
use support\Log;
use support\Cache;

/**
 * 系统配置服务类
 */
class SystemConfigService extends BaseService
{
    /**
     * 缓存前缀
     */
    const CACHE_PREFIX = 'system_config_';

    /**
     * 缓存过期时间（秒）
     */
    const CACHE_TTL = 3600;

    /**
     * 获取所有配置
     */
    public function getAllConfigs($includeEncrypted = false)
    {
        try {
            $cacheKey = self::CACHE_PREFIX . 'all_' . ($includeEncrypted ? 'with_encrypted' : 'no_encrypted');

            $configs = Cache::get($cacheKey);
            if ($configs === null) {
                $query = SystemConfig::where('1=1');

                if (!$includeEncrypted) {
                    $query->where('is_encrypted', false);
                }

                $configList = $query->select();

                // ThinkPHP没有内置的集合操作，需要手动分组
                $groupedConfigs = [];
                foreach ($configList as $config) {
                    $group = $config->config_group;
                    if (!isset($groupedConfigs[$group])) {
                        $groupedConfigs[$group] = [];
                    }
                    $groupedConfigs[$group][] = $config->getSummaryInfo();
                }
                $configs = $groupedConfigs;

                Cache::set($cacheKey, $configs, self::CACHE_TTL);
            }

            // 计算总数量
            $total = 0;
            foreach ($configs as $group => $items) {
                $total += count($items);
            }

            return $this->success([
                'configs' => $configs,
                'total' => $total
            ]);

        } catch (\Exception $e) {
            Log::error('获取系统配置失败', [
                'include_encrypted' => $includeEncrypted,
                'error' => $e->getMessage()
            ]);

            return $this->error('获取系统配置失败：' . $e->getMessage() . $e->getFile() . $e->getLine());
        }
    }

    /**
     * 获取分组配置
     */
    public function getGroupConfigs($group)
    {
        try {
            $cacheKey = self::CACHE_PREFIX . 'group_' . $group;

                            $configs = Cache::get($cacheKey);
            if ($configs === null) {
                $configs = SystemConfig::getGroupConfigs($group);
                Cache::set($cacheKey, $configs, self::CACHE_TTL);
            }

            return $this->success([
                'group' => $group,
                'configs' => $configs,
                'total' => count($configs)
            ]);

        } catch (\Exception $e) {
            Log::error('获取分组配置失败', [
                'group' => $group,
                'error' => $e->getMessage()
            ]);

            return $this->error('获取分组配置失败：' . $e->getMessage());
        }
    }

    /**
     * 获取单个配置
     */
    public function getConfig($key)
    {
        try {
            $cacheKey = self::CACHE_PREFIX . 'single_' . $key;

            $config = Cache::get($cacheKey);
            if ($config === null) {
                $configModel = SystemConfig::where('config_key', $key)->find();

                if (!$configModel) {
                    return $this->error('配置不存在', 404);
                }

                $config = $configModel->getSummaryInfo();
                Cache::set($cacheKey, $config, self::CACHE_TTL);
            }

            return $this->success(['config' => $config]);

        } catch (\Exception $e) {
            Log::error('获取配置失败', [
                'key' => $key,
                'error' => $e->getMessage()
            ]);

            return $this->error('获取配置失败：' . $e->getMessage());
        }
    }

    /**
     * 更新配置
     */
    public function updateConfig($key, $value, $userId = null)
    {
        try {
            Db::startTrans();

            $config = SystemConfig::where('config_key', $key)->find();

            if (!$config) {
                return $this->error('配置不存在', 404);
            }

            if (!$config->canEdit()) {
                return $this->error('该配置不允许编辑', 403);
            }

            // 验证配置值
            $validation = $config->validateValue($value);
            if (!$validation['valid']) {
                return $this->error('配置值验证失败：' . ($validation['message'] ?? '无效的值'));
            }

            // 记录原值（用于日志）
            $oldValue = $config->config_value;

            // 设置新值
            $config->setRealValue($value);
            $config->updated_by = $userId;
            $config->updated_at = time();
            $config->save();

            // 清除相关缓存
            $this->clearConfigCache($key, $config->config_group);

            // 记录配置变更日志
            $this->logConfigChange($key, $oldValue, $value, $userId);

            Db::commit();

            return $this->success([
                'config' => $config->refresh()->getSummaryInfo(),
                'message' => '配置更新成功'
            ]);

        } catch (\Exception $e) {
            Db::rollback();

            Log::error('更新配置失败', [
                'key' => $key,
                'value' => $value,
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);

            return $this->error('更新配置失败：' . $e->getMessage());
        }
    }

    /**
     * 批量更新配置
     */
    public function batchUpdateConfigs($configs, $userId = null)
    {
        try {
            Db::startTrans();

            $results = [];
            $errors = [];

            foreach ($configs as $key => $value) {
                $config = SystemConfig::where('config_key', $key)->find();

                if (!$config) {
                    $errors[$key] = '配置不存在';
                    continue;
                }

                if (!$config->canEdit()) {
                    $errors[$key] = '该配置不允许编辑';
                    continue;
                }

                // 验证配置值
                $validation = $config->validateValue($value);
                if (!$validation['valid']) {
                    $errors[$key] = '配置值验证失败：' . ($validation['message'] ?? '无效的值');
                    continue;
                }

                // 记录原值
                $oldValue = $config->config_value;

                // 设置新值
                $config->setRealValue($value);
                $config->updated_by = $userId;
                $config->updated_at = time();
                $config->save();

                $results[$key] = [
                    'success' => true,
                    'old_value' => $oldValue,
                    'new_value' => $value
                ];

                // 记录配置变更日志
                $this->logConfigChange($key, $oldValue, $value, $userId);
            }

            // 清除所有配置缓存
            $this->clearAllConfigCache();

            Db::commit();

            return $this->success([
                'results' => $results,
                'errors' => $errors,
                'success_count' => count($results),
                'error_count' => count($errors),
                'message' => '批量更新完成'
            ]);

        } catch (\Exception $e) {
            Db::rollback();

            Log::error('批量更新配置失败', [
                'configs' => $configs,
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);

            return $this->error('批量更新配置失败：' . $e->getMessage());
        }
    }

    /**
     * 重置配置为默认值
     */
    public function resetConfig($key, $userId = null)
    {
        try {
            $config = SystemConfig::where('config_key', $key)->find();

            if (!$config) {
                return $this->error('配置不存在', 404);
            }

            if (!$config->canEdit()) {
                return $this->error('该配置不允许编辑', 403);
            }

            $oldValue = $config->config_value;
            $defaultValue = $config->default_value;

            $success = SystemConfig::resetToDefault($key, $userId);

            if (!$success) {
                return $this->error('重置配置失败');
            }

            // 清除相关缓存
            $this->clearConfigCache($key, $config->config_group);

            // 记录配置变更日志
            $this->logConfigChange($key, $oldValue, $defaultValue, $userId, 'reset');

            return $this->success([
                'config' => $config->refresh()->getSummaryInfo(),
                'message' => '配置已重置为默认值'
            ]);

        } catch (\Exception $e) {
            Log::error('重置配置失败', [
                'key' => $key,
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);

            return $this->error('重置配置失败：' . $e->getMessage());
        }
    }

    /**
     * 创建新配置
     */
    public function createConfig($data, $userId = null)
    {
        try {
            // 检查配置键是否已存在
            $existingConfig = SystemConfig::where('config_key', $data['config_key'])->find();
            if ($existingConfig) {
                return $this->error('配置键已存在');
            }

            $configData = array_merge($data, [
                'created_by' => $userId,
                'updated_by' => $userId,
                'created_at' => time(),
                'updated_at' => time()
            ]);

            $config = SystemConfig::create($configData);

            // 清除相关缓存
            $this->clearAllConfigCache();

            return $this->success([
                'config' => $config->getSummaryInfo(),
                'message' => '配置创建成功'
            ]);

        } catch (\Exception $e) {
            Log::error('创建配置失败', [
                'data' => $data,
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);

            return $this->error('创建配置失败：' . $e->getMessage());
        }
    }

    /**
     * 删除配置
     */
    public function deleteConfig($key, $userId = null)
    {
        try {
            $config = SystemConfig::where('config_key', $key)->find();

            if (!$config) {
                return $this->error('配置不存在', 404);
            }

            if (!$config->canEdit()) {
                return $this->error('该配置不允许删除', 403);
            }

            $config->delete();

            // 清除相关缓存
            $this->clearConfigCache($key, $config->config_group);

            // 记录删除日志
            $this->logConfigChange($key, $config->config_value, null, $userId, 'delete');

            return $this->success(['message' => '配置删除成功']);

        } catch (\Exception $e) {
            Log::error('删除配置失败', [
                'key' => $key,
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);

            return $this->error('删除配置失败：' . $e->getMessage());
        }
    }

    /**
     * 获取配置分组列表
     */
    public function getConfigGroups()
    {
        try {
            $groups = SystemConfig::field('DISTINCT config_group')
                ->order('config_group')
                ->group('config_group')
                ->column('config_group');

            $result = [];
            foreach ($groups as $group) {
                $config = new SystemConfig();
                $config->config_group = $group;
                $result[] = [
                    'group' => $group,
                    'name' => $config->config_group_name,
                    'count' => SystemConfig::where('config_group', $group)->count()
                ];
            }

            return $this->success([
                'groups' => $result,
                'total' => count($result)
            ]);

        } catch (\Exception $e) {
            Log::error('获取配置分组失败', [
                'error' => $e->getMessage()
            ]);

            return $this->error('获取配置分组失败：' . $e->getMessage());
        }
    }

    /**
     * 清除配置缓存
     */
    private function clearConfigCache($key, $group = null)
    {
        // 清除单个配置缓存
        Cache::delete(self::CACHE_PREFIX . 'single_' . $key);

        // 清除分组缓存
        if ($group) {
            Cache::delete(self::CACHE_PREFIX . 'group_' . $group);
        }

        // 清除所有配置缓存
        Cache::delete(self::CACHE_PREFIX . 'all_with_encrypted');
        Cache::delete(self::CACHE_PREFIX . 'all_no_encrypted');
    }

    /**
     * 清除所有配置缓存
     */
    private function clearAllConfigCache()
    {
        // $pattern = self::CACHE_PREFIX . '*';
        // 这里需要根据具体的缓存实现来清除匹配的缓存
        // 暂时清除已知的缓存键
        Cache::delete(self::CACHE_PREFIX . 'all_with_encrypted');
        Cache::delete(self::CACHE_PREFIX . 'all_no_encrypted');
    }

    /**
     * 记录配置变更日志
     */
    private function logConfigChange($key, $oldValue, $newValue, $userId = null, $action = 'update')
    {
        Log::info('系统配置变更', [
            'config_key' => $key,
            'action' => $action,
            'old_value' => $oldValue,
            'new_value' => $newValue,
            'user_id' => $userId,
            'timestamp' => date('Y-m-d H:i:s', time())
        ]);
    }
}
