<?php

namespace app\website\controller;

use app\model\ItineraryInventoryModel;
use app\model\ItineraryModel;
use app\model\ItineraryOrderModel;
use base\auth\Auth;
use support\Request;
use support\Response;

class Order
{
    /**
     * 根据线路ID及出行人数，获取价格明细及线路简略信息
     * 
     * @param Request $request
     * @return Response
     */
    public function getPriceDetail(Request $request): Response
    {
        // 获取参数
        $itineraryId = $request->input('itineraryId', 0);
        $personCount = max(1, (int)$request->input('personCount', 1));
        // 儿童人数
        $childCount = max(0, (int)$request->input('childCount', 0));
        // 升级房型数量
        $upgradeRoomCount = max(0, (int)$request->input('upgradeRoomCount', 0));
        // 需要补单房差的房间数量
        $needUpgradeRoomCount = $request->input('needUpgradeRoomCount', 0);
        // 计划出行日期
        $planDepartureDate = $request->input('planDepartureDate', '');

        if (empty($itineraryId)) {
            return json(['code' => 400, 'msg' => '线路ID不能为空', 'data' => null]);
        }
        
        // 验证日期格式
        if (!empty($planDepartureDate)) {
            $dateObj = \DateTime::createFromFormat('Y-m-d', $planDepartureDate);
            if (!$dateObj || $dateObj->format('Y-m-d') !== $planDepartureDate) {
                return json(['code' => 400, 'msg' => '出行日期格式不正确，应为YYYY-MM-DD格式', 'data' => null]);
            }
            
            // 检查日期是否是过去日期
            $today = new \DateTime();
            $today->setTime(0, 0, 0); // 设置为当天的开始时间
            if ($dateObj < $today) {
                return json(['code' => 400, 'msg' => '出行日期不能是过去的日期', 'data' => null]);
            }
        }

        // 获取线路信息
        $itinerary = ItineraryModel::mk()
            ->where('id', $itineraryId)
            ->where('status', ItineraryModel::ONLINE_STATUS)
            ->field('id, title, cover_image, price, original_price, days, nights, departure_city, destination, single_room_surcharge, room_upgrade_surcharge, child_price')
            ->append(['id'])
            ->find();
            
        if (empty($itinerary)) {
            return json(['code' => 404, 'msg' => '线路不存在或已下线', 'data' => null]);
        }
        
        $itinerary = $itinerary->toArray();
        
        // 库存检查和节假日加价
        $stockInfo = $this->checkStockAndHolidaySurcharge($itineraryId, $planDepartureDate, $personCount, $childCount);
        
        if ($stockInfo['error']) {
            return json(['code' => 400, 'msg' => $stockInfo['message'], 'data' => null]);
        }
        
        // 计算价格明细
        $unitPrice = $itinerary['price']; // 成人单人价格
        $originalUnitPrice = $itinerary['originalPrice'] ?: $unitPrice; // 成人单人原价
        $childUnitPrice = $itinerary['childPrice'] ?: ($unitPrice * 0.8); // 儿童单价，如果没有设置则为成人价的80%
        
        // 应用节假日加价
        if ($stockInfo['isHoliday']) {
            $holidaySurcharge = $stockInfo['holidaySurcharge'];
            $unitPrice += $holidaySurcharge;
            $childUnitPrice += ($holidaySurcharge * 0.8); // 儿童节假日加价通常是成人的80%
        }
        
        // 计算基础费用
        $adultTotalPrice = $unitPrice * $personCount; // 成人总费用
        $childTotalPrice = $childUnitPrice * $childCount; // 儿童总费用
        $baseTotalPrice = $adultTotalPrice + $childTotalPrice; // 基础总费用
        
        // 计算房型和单房差费用
        $singleRoomPrice = $itinerary['singleRoomSurcharge'] ?: ($unitPrice * 0.3); // 单房差费用，如果没有设置则为成人价的30%
        $upgradeRoomPrice = $itinerary['roomUpgradeSurcharge'] ?: ($unitPrice * 0.5); // 升级房型费用，如果没有设置则为成人价的50%
        
        // 计算需要单房差的情况
        $autoSingleRoomCount = 0;
        if ($personCount % 2 == 1 && $needUpgradeRoomCount <= 0 && $upgradeRoomCount <= 0) {
            // 如果成人人数是奇数，并且没有主动选择单房差或升级房型，则自动计算一间单房差
            $autoSingleRoomCount = 1;
        }
        
        // 计算单房差总费用
        $singleRoomTotalPrice = $singleRoomPrice * $needUpgradeRoomCount;
        // 计算升级房型总费用
        $upgradeRoomTotalPrice = $upgradeRoomPrice * $upgradeRoomCount;
        // 计算自动单房差总费用
        $autoSingleRoomTotalPrice = $singleRoomPrice * $autoSingleRoomCount;
        
        // 计算原总价
        $originalAdultTotalPrice = $originalUnitPrice * $personCount;
        $originalChildTotalPrice = $childTotalPrice; // 儿童价通常没有折扣，所以原价等于现价
        $originalBaseTotalPrice = $originalAdultTotalPrice + $originalChildTotalPrice;
        $originalTotalPrice = $originalBaseTotalPrice + $singleRoomTotalPrice + $upgradeRoomTotalPrice + $autoSingleRoomTotalPrice;
        
        // 计算总价
        $totalPrice = $baseTotalPrice + $singleRoomTotalPrice + $upgradeRoomTotalPrice + $autoSingleRoomTotalPrice;
        
        // 计算折扣信息
        $discount = 0;
        $discountRate = 0;
        if ($originalBaseTotalPrice > $baseTotalPrice) {
            $discount = $originalBaseTotalPrice - $baseTotalPrice;
            $discountRate = round(($originalUnitPrice - $unitPrice) / $originalUnitPrice * 100);
        }
        
        // 构建价格明细项
        $priceItems = [];
        
        // 添加成人费用
        $priceItems[] = [
            'name' => '成人费用',
            'price' => $unitPrice,
            'count' => $personCount,
            'totalPrice' => $adultTotalPrice,
        ];
        
        // 如果有儿童，添加儿童费用
        if ($childCount > 0) {
            $priceItems[] = [
                'name' => '儿童费用',
                'price' => $childUnitPrice,
                'count' => $childCount,
                'totalPrice' => $childTotalPrice,
            ];
        }
        
        // 如果是节假日加价，添加节假日加价费用
        if ($stockInfo['isHoliday']) {
            $priceItems[] = [
                'name' => '节假日加价',
                'price' => $stockInfo['holidaySurcharge'],
                'count' => $personCount,
                'totalPrice' => $stockInfo['holidaySurcharge'] * $personCount,
                'holidayName' => $stockInfo['holidayName']
            ];
            
            if ($childCount > 0) {
                $priceItems[] = [
                    'name' => '儿童节假日加价',
                    'price' => $stockInfo['holidaySurcharge'] * 0.8,
                    'count' => $childCount,
                    'totalPrice' => $stockInfo['holidaySurcharge'] * 0.8 * $childCount,
                    'holidayName' => $stockInfo['holidayName']
                ];
            }
        }
        
        // 如果有需要的单房差，添加单房差费用
        if ($needUpgradeRoomCount > 0) {
            $priceItems[] = [
                'name' => '单房差',
                'price' => $singleRoomPrice,
                'count' => $needUpgradeRoomCount,
                'totalPrice' => $singleRoomTotalPrice,
            ];
        }
        
        // 如果有升级房型，添加升级房型费用
        if ($upgradeRoomCount > 0) {
            $priceItems[] = [
                'name' => '升级房型',
                'price' => $upgradeRoomPrice,
                'count' => $upgradeRoomCount,
                'totalPrice' => $upgradeRoomTotalPrice,
            ];
        }
        
        // 如果自动计算了单房差，添加自动单房差费用
        if ($autoSingleRoomCount > 0) {
            $priceItems[] = [
                'name' => '单房差(单数出行)',
                'price' => $singleRoomPrice,
                'count' => $autoSingleRoomCount,
                'totalPrice' => $autoSingleRoomTotalPrice,
                'isAuto' => true
            ];
        }
        
        // 构建价格明细
        $priceDetail = [
            'unitPrice' => $unitPrice, // 单人价格
            'originalUnitPrice' => $originalUnitPrice, // 单人原价
            'childUnitPrice' => $childUnitPrice, // 儿童单价
            'singleRoomPrice' => $singleRoomPrice, // 单房差价格
            'upgradeRoomPrice' => $upgradeRoomPrice, // 升级房型价格
            'personCount' => $personCount, // 成人人数
            'childCount' => $childCount, // 儿童人数
            'needUpgradeRoomCount' => $needUpgradeRoomCount, // 需要单房差数量
            'upgradeRoomCount' => $upgradeRoomCount, // 升级房型数量
            'autoSingleRoomCount' => $autoSingleRoomCount, // 自动计算的单房差数量
            'baseTotalPrice' => $baseTotalPrice, // 基础总费用(成人+儿童)
            'totalPrice' => $totalPrice, // 总价
            'originalTotalPrice' => $originalTotalPrice, // 原总价
            'discount' => $discount, // 优惠金额
            'discountRate' => $discountRate, // 折扣率（百分比）
            'priceItems' => $priceItems, // 价格项目明细
            'stock' => $stockInfo['stock'], // 库存信息
            'planDepartureDate' => $planDepartureDate, // 计划出行日期
            'isHoliday' => $stockInfo['isHoliday'], // 是否节假日
            'holidayName' => $stockInfo['holidayName'] // 节假日名称
        ];
        
        // 构建返回数据
        $data = [
            'itinerary' => [
                'id' => $itinerary['id'],
                'title' => $itinerary['title'],
                'coverImage' => $itinerary['coverImage'],
                'days' => $itinerary['days'],
                'nights' => $itinerary['nights'],
                'departureCity' => $itinerary['departureCity'],
                'destination' => $itinerary['destination'],
            ],
            'priceDetail' => $priceDetail
        ];
        
        return json(['code' => 200, 'msg' => 'success', 'data' => $data]);
    }
    
    /**
     * 检查指定日期的库存和节假日加价情况
     * 
     * @param int $itineraryId 线路ID
     * @param string $departureDate 出发日期
     * @param int $adultCount 成人数
     * @param int $childCount 儿童数
     * @return array 库存和节假日信息
     */
    private function checkStockAndHolidaySurcharge($itineraryId, $departureDate, $adultCount, $childCount): array
    {
        $result = [
            'error' => false,
            'message' => '',
            'stock' => 0,
            'isHoliday' => false,
            'holidayName' => '',
            'holidaySurcharge' => 0,
            'adultSurcharge' => 0,
            'childSurcharge' => 0,
            'hasDateSurcharge' => false
        ];
        
        // 如果没有提供出发日期，则默认有库存且无加价
        if (empty($departureDate)) {
            $result['stock'] = 999; // 默认库存充足
            return $result;
        }
        
        // 查询库存表获取当天库存
        $inventoryInfo = ItineraryInventoryModel::mk()
            ->where('itinerary_id', $itineraryId)
            ->where('inventory_date', $departureDate)
            ->find();
            
        // 如果没有库存记录，默认没有库存
        if (empty($inventoryInfo)) {
            $result['error'] = true;
            $result['message'] = '所选日期暂无可预订库存';
            $result['stock'] = 0; // 默认无库存
            return $result;
        }
            
        // 转换为数组
        $inventoryInfo = $inventoryInfo->toArray();
        
        // 检查库存状态
        if ($inventoryInfo['status'] === '0') {
            $result['error'] = true;
            $result['message'] = '所选日期不可预订';
            return $result;
        }
        
        // 计算可用库存
        $totalAmount = $inventoryInfo['totalAmount'] ?? null;
        $bookedAmount = $inventoryInfo['bookedAmount'] ?? 0;
        
        // 如果总库存为NULL，表示不限制库存
        if ($totalAmount === null) {
            $result['stock'] = 999; // 表示库存充足
        } else {
            $result['stock'] = max(0, $totalAmount - $bookedAmount);
            
            // 检查库存是否足够
            $totalPersons = $adultCount + $childCount;
            if ($result['stock'] < $totalPersons) {
                $result['error'] = true;
                $result['message'] = "所选日期库存不足，仅剩{$result['stock']}个名额";
                return $result;
            }
        }
        
        // 检查是否有热门日期加价
        if ($inventoryInfo['hasHotDateSurcharge']) {
            $result['isHoliday'] = true;
            $result['holidayName'] = '热门日期';
            $result['holidaySurcharge'] = $inventoryInfo['hotDateSurchargeAmount'] ?? 0;
            $result['hasDateSurcharge'] = true;
        }
        
        // 检查是否有出行人加价
        if ($inventoryInfo['hasTravelerSurcharge']) {
            $result['adultSurcharge'] = $inventoryInfo['adultSurchargeAmount'] ?? 0;
            $result['childSurcharge'] = $inventoryInfo['childSurchargeAmount'] ?? 0;
        }
        
        return $result;
    }

    /**
     * 创建订单
     * 
     * @param Request $request
     * @return Response
     */
    #[Auth('app:order:save')]
    public function save(Request $request): Response
    {
        // 获取基本参数
        $itineraryId = $request->input('itineraryId', 0);
        $personCount = max(1, (int)$request->input('personCount', 1));
        $childCount = max(0, (int)$request->input('childCount', 0));
        $upgradeRoomCount = max(0, (int)$request->input('upgradeRoomCount', 0));
        $needUpgradeRoomCount = $request->input('needUpgradeRoomCount', 0);
        $planDepartureDate = $request->input('planDepartureDate', '');
        
        // 获取联系人信息
        $contactName = $request->input('name', '');
        $contactPhone = $request->input('phone', '');
//        $contactEmail = $request->input('contactEmail', '');
        $remark = $request->input('orderNote', '');
        
        // 获取旅客信息（可能是多个旅客的数组）
        $travelers = $request->input('travelers', []);
        
        // 基本验证
        if (empty($itineraryId)) {
            return json(['code' => 400, 'msg' => '线路ID不能为空', 'data' => null]);
        }
        
        if (empty($contactName)) {
            return json(['code' => 400, 'msg' => '联系人姓名不能为空', 'data' => null]);
        }
        
        if (empty($contactPhone)) {
            return json(['code' => 400, 'msg' => '联系人电话不能为空', 'data' => null]);
        }
        
        // 验证手机号格式
        if (!preg_match('/^1[3-9]\d{9}$/', $contactPhone)) {
            return json(['code' => 400, 'msg' => '手机号格式不正确', 'data' => null]);
        }
        
        // 验证日期格式
        if (empty($planDepartureDate)) {
            return json(['code' => 400, 'msg' => '出行日期不能为空', 'data' => null]);
        }
        
        $dateObj = \DateTime::createFromFormat('Y-m-d', $planDepartureDate);
        if (!$dateObj || $dateObj->format('Y-m-d') !== $planDepartureDate) {
            return json(['code' => 400, 'msg' => '出行日期格式不正确，应为YYYY-MM-DD格式', 'data' => null]);
        }
        
        // 检查日期是否是过去日期
        $today = new \DateTime();
        $today->setTime(0, 0, 0); // 设置为当天的开始时间
        if ($dateObj < $today) {
            return json(['code' => 400, 'msg' => '出行日期不能是过去的日期', 'data' => null]);
        }
        
        // 验证旅客信息
        $totalTravelers = $personCount + $childCount;
        
        // 旅客信息非必填，但如果提供了，则进行验证
        if (!empty($travelers)) {
            // 验证每个旅客的必要信息
            foreach ($travelers as $traveler) {
                if (!empty($traveler['idCard']) && !$this->validateIdCard($traveler['idCard'])) {
                    return json(['code' => 400, 'msg' => '身份证号格式不正确', 'data' => null]);
                }
            }
        }
        
        // 获取线路信息
        $itinerary = ItineraryModel::mk()
            ->where('id', $itineraryId)
            ->where('status', ItineraryModel::ONLINE_STATUS)
            ->field('id, title, cover_image, price, original_price, days, nights, departure_city, destination, single_room_surcharge, room_upgrade_surcharge, child_price')
            ->append(['id'])
            ->find();
            
        if (empty($itinerary)) {
            return json(['code' => 404, 'msg' => '线路不存在或已下线', 'data' => null]);
        }
        
        $itinerary = $itinerary->toArray();
        
        // 库存检查和节假日加价
        $stockInfo = $this->checkStockAndHolidaySurcharge($itineraryId, $planDepartureDate, $personCount, $childCount);
        
        if ($stockInfo['error']) {
            return json(['code' => 400, 'msg' => $stockInfo['message'], 'data' => null]);
        }
        
        // 计算价格明细
        $unitPrice = $itinerary['price']; // 成人单人价格
        $originalUnitPrice = $itinerary['originalPrice'] ?: $unitPrice; // 成人单人原价
        $childUnitPrice = $itinerary['childPrice'] ?: ($unitPrice * 0.8); // 儿童单价，如果没有设置则为成人价的80%
        
        // 应用节假日加价或出行人加价
        if ($stockInfo['isHoliday']) {
            $holidaySurcharge = $stockInfo['holidaySurcharge'];
            $unitPrice += $holidaySurcharge;
            $childUnitPrice += ($holidaySurcharge * 0.8); // 儿童节假日加价通常是成人的80%
        }
        
        // 应用出行人加价
        $unitPrice += $stockInfo['adultSurcharge'];
        $childUnitPrice += $stockInfo['childSurcharge'];
        
        // 计算基础费用
        $adultTotalPrice = $unitPrice * $personCount; // 成人总费用
        $childTotalPrice = $childUnitPrice * $childCount; // 儿童总费用
        $baseTotalPrice = $adultTotalPrice + $childTotalPrice; // 基础总费用
        
        // 计算房型和单房差费用
        $singleRoomPrice = $itinerary['singleRoomSurcharge'] ?: ($unitPrice * 0.3); // 单房差费用，如果没有设置则为成人价的30%
        $upgradeRoomPrice = $itinerary['roomUpgradeSurcharge'] ?: ($unitPrice * 0.5); // 升级房型费用，如果没有设置则为成人价的50%
        
        // 计算需要单房差的情况
        $autoSingleRoomCount = 0;
        if ($personCount % 2 == 1 && $needUpgradeRoomCount <= 0 && $upgradeRoomCount <= 0) {
            // 如果成人人数是奇数，并且没有主动选择单房差或升级房型，则自动计算一间单房差
            $autoSingleRoomCount = 1;
        }
        
        // 计算单房差总费用
        $singleRoomTotalPrice = $singleRoomPrice * $needUpgradeRoomCount;
        // 计算升级房型总费用
        $upgradeRoomTotalPrice = $upgradeRoomPrice * $upgradeRoomCount;
        // 计算自动单房差总费用
        $autoSingleRoomTotalPrice = $singleRoomPrice * $autoSingleRoomCount;
        
        // 计算订单总价
        $totalPrice = $baseTotalPrice + $singleRoomTotalPrice + $upgradeRoomTotalPrice + $autoSingleRoomTotalPrice;
        
        // 生成唯一订单号（年月日+随机数）
        $orderNo = date('YmdHis') . mt_rand(1000, 9999);
        
       
        // 开始事务
        \think\facade\Db::startTrans();
        
        try {
            // 创建订单
            $model = new ItineraryOrderModel();
            ItineraryOrderModel::mk()->save([
                'itinerary_id' => $itineraryId,
                'order_no' => $orderNo,
                'user_id' => userinfo()->userId(),
                'name' => $contactName,
                'phone' => $contactPhone,
                'travel_date' => $planDepartureDate,
                'adult_count' => $personCount,
                'child_count' => $childCount,
                'base_price' => $baseTotalPrice,
                'adult_price' => $unitPrice,
                'child_price' => $childUnitPrice,
                'hot_date_surcharge' => $stockInfo['holidaySurcharge'],
                'adult_surcharge' => $stockInfo['adultSurcharge'],
                'child_surcharge' => $stockInfo['childSurcharge'],
                'room_count' => $needUpgradeRoomCount + $autoSingleRoomCount,
                'single_room_count' => $needUpgradeRoomCount,
                'upgrade_room_count' => $upgradeRoomCount,
                'single_room_surcharge' => $singleRoomPrice,
                'upgrade_room_surcharge' => $upgradeRoomPrice,
                'remark' => $remark,
                'total_amount' => $totalPrice,
                'status' => 'pending',
            ]);
            $orderId = $model->id;
            // 保存旅客信息
            // if (!empty($travelers)) {
            //     foreach ($travelers as $index => $traveler) {
            //         // 只保存需要的旅客数量，并且确保有姓名
            //         if ($index < $totalTravelers && !empty($traveler['name'])) {
            //             $travelerData = [
            //                 'order_id' => $orderId,
            //                 'name' => $traveler['name'],
            //                 'gender' => $traveler['gender'] ?? '',
            //                 'id_card' => $traveler['idCard'] ?? '',
            //                 'phone' => $traveler['phone'] ?? '',
            //                 'is_child' => $index >= $personCount ? 1 : 0, // 如果索引超过成人数量，则标记为儿童
            //                 'create_time' => time(),
            //                 'update_time' => time()
            //             ];
                        
            //             $travelerId = \think\facade\Db::table('order_travelers')->insertGetId($travelerData);
                        
            //             if (!$travelerId) {
            //                 throw new \Exception('保存旅客信息失败');
            //             }
            //         }
            //     }
            // }
            
            // 更新库存
            if (!empty($planDepartureDate)) {
                $inventory = ItineraryInventoryModel::mk()
                    ->where('itinerary_id', $itineraryId)
                    ->where('inventory_date', $planDepartureDate)
                    ->find();
                    
                if ($inventory) {
                    // 更新已预约数量
                    $inventory->booked_amount = $inventory->booked_amount + $totalTravelers;
                    $inventory->save();
                }
            }
            
            // 提交事务
            \think\facade\Db::commit();
            
            // 返回订单ID和订单号
            return json(['code' => 200, 'msg' => '订单创建成功', 'data' => [
                'order_id' => $orderId,
                'order_no' => $orderNo,
                'total_price' => $totalPrice
            ]]);
            
        } catch (\Exception $e) {
            // 回滚事务
            \think\facade\Db::rollback();
            
            return json(['code' => 500, 'msg' => '订单创建失败: ' . $e->getMessage(), 'data' => null]);
        }
    }
    
    /**
     * 验证身份证号码
     * 
     * @param string $idCard
     * @return bool
     */
    private function validateIdCard(string $idCard): bool
    {
        // 简单验证身份证号码格式（18位）
        if (strlen($idCard) != 18) {
            return false;
        }
        
        // 验证前17位是否都是数字
        if (!preg_match('/^\d{17}[\dX]$/', $idCard)) {
            return false;
        }
        
        return true;
    }

    /**
     * 获取用户订单列表
     * 
     * @param Request $request
     * @return Response
     */
    #[Auth('app:order:list')]
    public function list(Request $request): Response
    {
        // 获取当前用户ID
        $userId = userinfo()->userId();
        
        // 获取查询参数
        $page = max(1, (int)$request->input('page', 1));
        $pageSize = min(50, max(1, (int)$request->input('pageSize', 10)));
        $status = $request->input('status', ''); // 订单状态筛选
        
        // 构建查询条件
        $query = ItineraryOrderModel::mk()
            ->where('user_id', $userId)
            ->withJoin(['itinerary' => function($query) {
                $query->field('id,title,cover_image,days,nights,departure_city,destination');
            }]);
        
        // 如果指定了状态，添加状态筛选
        if (!empty($status)) {
            $query = $query->where('status', $status);
        }
        
        // 执行查询
        $total = $query->count();
        $orders = $query->order('create_time', 'desc')
            ->page($page, $pageSize)
            ->append(['id','create_time'])
            ->select()
            ->toArray();
            
        // 处理订单数据，添加状态文本和时间格式化
        foreach ($orders as &$order) {
            // 添加状态文本
            $order['statusText'] = $this->getOrderStatusText($order['status']);

        }

        // 构建返回数据
        $result = [
            'list' => $orders,
            'total' => $total,
            'page' => $page,
            'pageSize' => $pageSize,
        ];
        
        return json(['code' => 200, 'msg' => 'success', 'data' => $result]);
    }
    
    /**
     * 获取订单状态对应的文本
     * 
     * @param string $status 状态代码
     * @return string 状态文本
     */
    private function getOrderStatusText(string $status): string
    {
        $statusMap = [
            'pending' => '待支付',
            'paid' => '已支付',
            'confirmed' => '已确认',
            'completed' => '已完成',
            'cancelled' => '已取消',
            'refunding' => '退款中',
            'refunded' => '已退款'
        ];
        
        return $statusMap[$status] ?? '未知状态';
    }

    /**
     * 取消订单
     * 
     * @param Request $request
     * @return Response
     */
    #[Auth('app:order:cancel')]
    public function cancel(Request $request): Response
    {
        // 获取订单ID
        $orderNo = $request->input('orderNo', 0);
        if (empty($orderNo)) {
            return json(['code' => 400, 'msg' => '订单ID不能为空', 'data' => null]);
        }
        
        // 获取当前用户ID
        $userId = userinfo()->userId();
        
        // 查询订单
        $order = ItineraryOrderModel::mk()
            ->where('order_no', $orderNo)
            ->where('user_id', $userId)
            ->find();
            
        if (empty($order)) {
            return json(['code' => 404, 'msg' => '订单不存在或无权操作', 'data' => null]);
        }
        
        // 检查订单状态是否可取消
        $allowCancelStatus = ['pending']; // 只有待支付状态可以取消
        if (!in_array($order->status, $allowCancelStatus)) {
            return json(['code' => 400, 'msg' => '当前订单状态不可取消', 'data' => null]);
        }
        
        // 开始事务
        \think\facade\Db::startTrans();
        
        try {
            // 更新订单状态
            $order->status = 'cancelled';
            $order->cancel_time = time();
            $order->cancel_reason = $request->input('reason', '用户主动取消');
            $result = $order->save();
            
            if (!$result) {
                throw new \Exception('取消订单失败');
            }
            
            // 恢复库存
            if (!empty($order->travel_date)) {
                $inventory = ItineraryInventoryModel::mk()
                    ->where('itinerary_id', $order->itinerary_id)
                    ->where('inventory_date', $order->travel_date)
                    ->find();
                    
                if ($inventory) {
                    // 计算需要恢复的库存数量
                    $restoreCount = $order->adult_count + $order->child_count;
                    
                    // 更新库存
                    $inventory->booked_amount = max(0, $inventory->booked_amount - $restoreCount);
                    $inventory->save();
                }
            }
            
            // 提交事务
            \think\facade\Db::commit();
            
            return json(['code' => 200, 'msg' => '订单取消成功', 'data' => null]);
            
        } catch (\Exception $e) {
            // 回滚事务
            \think\facade\Db::rollback();
            
            return json(['code' => 500, 'msg' => '订单取消失败: ' . $e->getMessage(), 'data' => null]);
        }
    }

    /**
     * 获取订单详情及线路简略信息
     * 
     * @param Request $request
     * @return Response
     */
    #[Auth('app:order:detail')]
    public function detail(Request $request): Response
    {
        // 获取订单ID或订单号
        $orderId = $request->input('orderId', 0);
        $orderNo = $request->input('orderNo', '');
        
        if (empty($orderId) && empty($orderNo)) {
            return json(['code' => 400, 'msg' => '订单ID或订单号不能为空', 'data' => null]);
        }
        
        // 获取当前用户ID
        $userId = userinfo()->userId();
        
        // 构建查询条件
        $query = ItineraryOrderModel::mk()->where('user_id', $userId);
        
        if (!empty($orderId)) {
            $query = $query->where('id', $orderId);
        } else {
            $query = $query->where('order_no', $orderNo);
        }
        
        // 查询订单
        $order = $query      ->append(['id','create_time'])->find();
        
        if (empty($order)) {
            return json(['code' => 404, 'msg' => '订单不存在或无权查看', 'data' => null]);
        }
        
        // 将对象转为数组
        $order = $order->toArray();
        
        // 查询线路信息
        $itinerary = ItineraryModel::mk()
            ->where('id', $order['itineraryId'])
            ->field('id, title, cover_image, price, original_price, days, nights, departure_city, destination, single_room_surcharge, room_upgrade_surcharge, child_price')
            ->append(['id'])
            ->find();
            
        if (empty($itinerary)) {
            $itinerary = []; // 如果线路不存在，返回空对象
        } else {
            $itinerary = $itinerary->toArray();
        }
        
        // 计算价格明细
        $unitPrice = $order['adultPrice']; // 成人单人价格
        $childUnitPrice = $order['childPrice']; // 儿童单价
        $personCount = $order['adultCount']; // 成人数量
        $childCount = $order['childCount']; // 儿童数量
        $singleRoomPrice = $order['singleRoomSurcharge']; // 单房差价格
        $upgradeRoomPrice = $order['upgradeRoomSurcharge']; // 升级房型价格
        
        // 计算基础费用
        $adultTotalPrice = $unitPrice * $personCount; // 成人总费用
        $childTotalPrice = $childUnitPrice * $childCount; // 儿童总费用
        $baseTotalPrice = $adultTotalPrice + $childTotalPrice; // 基础总费用
        
        // 计算房型和单房差费用
        $singleRoomCount = $order['singleRoomCount']; // 单房差数量
        $upgradeRoomCount = $order['upgradeRoomCount']; // 升级房型数量
        $totalRoomCount = $order['roomCount']; // 总房间数
        
        // 可能存在自动添加的单房差
        $autoSingleRoomCount = $totalRoomCount - $singleRoomCount - $upgradeRoomCount;
        $autoSingleRoomCount = max(0, $autoSingleRoomCount);
        
        // 计算单房差总费用
        $singleRoomTotalPrice = $singleRoomPrice * $singleRoomCount;
        // 计算升级房型总费用
        $upgradeRoomTotalPrice = $upgradeRoomPrice * $upgradeRoomCount;
        // 计算自动单房差总费用
        $autoSingleRoomTotalPrice = $singleRoomPrice * $autoSingleRoomCount;
        
        // 构建价格明细项
        $priceItems = [];
        
        // 添加成人费用
        $priceItems[] = [
            'name' => '成人费用',
            'price' => $unitPrice,
            'count' => $personCount,
            'totalPrice' => $adultTotalPrice,
        ];
        
        // 如果有儿童，添加儿童费用
        if ($childCount > 0) {
            $priceItems[] = [
                'name' => '儿童费用',
                'price' => $childUnitPrice,
                'count' => $childCount,
                'totalPrice' => $childTotalPrice,
            ];
        }
        
        // 如果有热门日期加价，添加加价费用
        if (!empty($order['hotDateSurcharge']) && $order['hotDateSurcharge'] > 0) {
            $priceItems[] = [
                'name' => '热门日期加价',
                'price' => $order['hotDateSurcharge'],
                'count' => $personCount,
                'totalPrice' => $order['hotDateSurcharge'] * $personCount,
            ];
            
            if ($childCount > 0) {
                $priceItems[] = [
                    'name' => '儿童热门日期加价',
                    'price' => $order['hotDateSurcharge'] * 0.8,
                    'count' => $childCount,
                    'totalPrice' => $order['hotDateSurcharge'] * 0.8 * $childCount,
                ];
            }
        }
        
        // 如果有出行人加价，添加加价费用
        if (!empty($order['adultSurcharge']) && $order['adultSurcharge'] > 0) {
            $priceItems[] = [
                'name' => '成人加价',
                'price' => $order['adultSurcharge'],
                'count' => $personCount,
                'totalPrice' => $order['adultSurcharge'] * $personCount,
            ];
        }
        
        if (!empty($order['childSurcharge']) && $order['childSurcharge'] > 0 && $childCount > 0) {
            $priceItems[] = [
                'name' => '儿童加价',
                'price' => $order['childSurcharge'],
                'count' => $childCount,
                'totalPrice' => $order['childSurcharge'] * $childCount,
            ];
        }
        
        // 如果有单房差，添加单房差费用
        if ($singleRoomCount > 0) {
            $priceItems[] = [
                'name' => '单房差',
                'price' => $singleRoomPrice,
                'count' => $singleRoomCount,
                'totalPrice' => $singleRoomTotalPrice,
            ];
        }
        
        // 如果有升级房型，添加升级房型费用
        if ($upgradeRoomCount > 0) {
            $priceItems[] = [
                'name' => '升级房型',
                'price' => $upgradeRoomPrice,
                'count' => $upgradeRoomCount,
                'totalPrice' => $upgradeRoomTotalPrice,
            ];
        }
        
        // 如果有自动单房差，添加自动单房差费用
        if ($autoSingleRoomCount > 0) {
            $priceItems[] = [
                'name' => '单房差(单数出行)',
                'price' => $singleRoomPrice,
                'count' => $autoSingleRoomCount,
                'totalPrice' => $autoSingleRoomTotalPrice,
                'isAuto' => true
            ];
        }
        
        // 构建价格明细
        $priceDetail = [
            'unitPrice' => $unitPrice, // 单人价格
            'childUnitPrice' => $childUnitPrice, // 儿童单价
            'singleRoomPrice' => $singleRoomPrice, // 单房差价格
            'upgradeRoomPrice' => $upgradeRoomPrice, // 升级房型价格
            'personCount' => $personCount, // 成人人数
            'childCount' => $childCount, // 儿童人数
            'singleRoomCount' => $singleRoomCount, // 需要单房差数量
            'upgradeRoomCount' => $upgradeRoomCount, // 升级房型数量
            'autoSingleRoomCount' => $autoSingleRoomCount, // 自动计算的单房差数量
            'baseTotalPrice' => $baseTotalPrice, // 基础总费用(成人+儿童)
            'totalPrice' => $order['totalAmount'], // 总价
            'priceItems' => $priceItems, // 价格项目明细
            'planDepartureDate' => $order['travelDate'], // 计划出行日期
        ];
        
        // 获取订单状态文本
        $order['statusText'] = $this->getOrderStatusText($order['status']);
        
        // 构建返回数据
        $data = [
            'order' => [
                'id' => $order['id'],
                'orderNo' => $order['orderNo'],
                'status' => $order['status'],
                'statusText' => $order['statusText'],
                'createTime' => $order['createTime'],
                'name' => $order['name'],
                'phone' => $order['phone'],
                'travelDate' => $order['travelDate'],
                'remark' => $order['remark'],
            ],
            'itinerary' => !empty($itinerary) ? [
                'id' => $itinerary['id'],
                'title' => $itinerary['title'],
                'coverImage' => $itinerary['coverImage'],
                'days' => $itinerary['days'],
                'nights' => $itinerary['nights'],
                'departureCity' => $itinerary['departureCity'],
                'destination' => $itinerary['destination'],
            ] : null,
            'priceDetail' => $priceDetail
        ];
        
        return json(['code' => 200, 'msg' => 'success', 'data' => $data]);
    }
}