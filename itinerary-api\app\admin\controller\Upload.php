<?php

namespace app\admin\controller;


use support\Request;
use yzh52521\Filesystem\Facade\Filesystem;

class Upload
{
    public function image(Request $request)
    {
        try {
            $file = $request->file('file');
            $file_extension = $file->getUploadExtension();
            if (!in_array($file_extension, ['jpg', 'png', 'jpeg', 'gif', 'webp'])) {
                throw new \Exception('图片格式错误');
            }
            $path = Filesystem::putFile('upload/image', $file);
            //指定选定器外网
            $fileUrl = Filesystem::url($path);
            $data = [
                'urlPath' => $fileUrl,
                'path' => $path,
            ];
            return success($data);
        } catch (\Exception $e) {
            return errorNew($e->getMessage());
        }
    }

    public function images(Request $request)
    {
        try {
            $files = $request->file();
            if (empty($files)) {
                throw new \Exception('图片不能为空');
            }
            $data = [];
            foreach ($files as $file) {
                $file_extension = $file->getUploadExtension();
                if (!in_array($file_extension, ['jpg', 'png', 'jpeg', 'gif', 'webp'])) {
                    throw new \Exception('图片格式错误');
                }
                $path = Filesystem::putFile('upload/image', $file);
                //指定选定器外网
                $fileUrl = Filesystem::url($path);
                $data[] = [
                    'url_path' => $fileUrl,
                    'path' => $path,
                ];

            }
            return success($data);
        } catch (\Exception $e) {
            return errorNew($e->getMessage());

        }


    }
}