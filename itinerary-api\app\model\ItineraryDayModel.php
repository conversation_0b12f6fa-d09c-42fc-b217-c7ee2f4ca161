<?php

namespace app\model;

use base\base\BaseModel;
use think\model\relation\HasMany;

class ItineraryDayModel extends BaseModel
{
    /**
     * 设置表名
     * @var string
     */
    protected $name = 'itinerary_day';

    /**
     * 设置主键
     * @var string
     */
    protected $pk = 'id';

    public function schedules(): HasMany
    {
        return $this->hasMany(ItineraryScheduleModel::class ,'itinerary_day_id','id');
    }
}
