<?php

namespace app\website\controller;

use app\model\ItineraryDayModel;
use app\model\ItineraryModel;
use app\model\ItineraryScheduleModel;
use app\model\ProductCategoryModel;
use support\Request;
use support\Response;
use think\facade\Db;

class Itinerary
{
    /**
     * 获取行程详情
     * @param Request $request
     * @param string $id
     * @return Response
     */
    public function detail(Request $request, string $id = ''): Response
    {
        // 尝试从路径获取ID，如果没有则从请求参数中获取
        if (empty($id)) {
            $id = $request->input('id');
        }

        if (!$id) {
            return error(400, '参数错误');
        }

        // 查询行程基本信息
        $detail = ItineraryModel::mk()
            ->with(['scheduleDays'=> ['schedules']])
            ->field([
                'id', 'title', 'price', 'original_price', 'days', 'nights', 'destination', 'departure_city',
                'product_type', 'cover_image', 'images', 'image_description', 'description', 'features', 'status',
                'travel_mode', 'group_type', 'min_group_size', 'max_group_size', 'collect_type', 'deposit',
                'promotion_price', 'category_id', 'category_path', 'valid_from', 'valid_to', 'is_long_term',
                'single_room_surcharge', 'room_upgrade_surcharge', 'create_time', 'update_time',
                // 交易规则相关字段
                'consumable_date_type', 'consumable_days', 'consumable_dates',
                // 限购规则
                'purchase_limit_single', 'purchase_limit_per_order', 'purchase_limit_rules', 'purchase_limit_details',
                // 预约规则
                'earliest_booking_days', 'advance_booking_required', 'advance_booking_range',
                'advance_booking_time', 'advance_booking_time_unit',
                // 取消规则
                'has_no_cancellation_after_appointment', 'refund_before_appointment', 'auto_refund_after_expiration',
                'has_breach_of_contract_terms', 'breach_of_contract_terms', 'breach_of_contract_base'
            ])
            ->where('id', $id)
            ->append(['id'])
            ->find();

        if (!$detail) {
            return error(400, '行程不存在');
        }

        $result = $detail->toArray();

        // 处理是否长期有效
        $result['isLongTerm'] = (bool)($result['isLongTerm'] ?? false);

        // 处理scheduleDays结构
        $scheduleDays = [];
        if (!empty($result['scheduleDays'])) {
            foreach ($result['scheduleDays'] as $day) {
                $dayItem = [
                    'day' => (int)$day['day'],
                    'title' => $day['title'],
                    'description' => $day['description'] ?? '',
                    'meetingPoint' => $day['meetingPoint'] ?? '',
                    'meetingTime' => $day['meetingTime'] ?? '',
                    'schedule' => []
                ];

                // 处理每天的行程安排
                if (!empty($day['schedules'])) {
                    foreach ($day['schedules'] as $schedule) {
                        $scheduleItem = [
                            'day' => (int)$day['day'],
                            'title' => $schedule['title'],
                            'description' => $schedule['description'] ?? '',
                            'activities' => !empty($schedule['activities']) ? $schedule['activities'] : [],
                            'meals' => !empty($schedule['meals']) ? $schedule['meals'] : [],
                            'accommodation' => $schedule['accommodation'] ?? '',
                            'images' => !empty($schedule['images']) ? $schedule['images'] : [],
                            'type' => $schedule['type'] ?? '',
                            'startTime' => $schedule['startTime'] ?? '',
                            'mealType' => !empty($schedule['mealType']) ? $schedule['mealType'] : [],
                            'adultMealIncluded' => $schedule['adultMealIncluded'] ?? '',
                            'childMealIncluded' => $schedule['childMealIncluded'] ?? '',
                            'mealPrice' => (float)($schedule['mealPrice'] ?? 0),
                            'transportType' => $schedule['transportType'] ?? '',
                            'flightNumber' => $schedule['flightNumber'] ?? '',
                            'departureCity' => $schedule['departureCity'] ?? '',
                            'arrivalCity' => $schedule['arrivalCity'] ?? '',
                            'stayType' => $schedule['stayType'] ?? '',
                            'hotelName' => $schedule['hotelName'] ?? '',
                            'remark' => $schedule['remark'] ?? ''
                        ];

                        // 处理嵌套对象
                        $scheduleItem['mealDuration'] = [
                            'hours' => (int)($schedule['mealDurationHours'] ?? 0),
                            'minutes' => (int)($schedule['mealDurationMinutes'] ?? 0)
                        ];

                        $scheduleItem['estimatedDuration'] = [
                            'hours' => (int)($schedule['estimatedDurationHours'] ?? 0),
                            'minutes' => (int)($schedule['estimatedDurationMinutes'] ?? 0)
                        ];

                        $scheduleItem['activityDuration'] = [
                            'hours' => (int)($schedule['activityDurationHours'] ?? 0),
                            'minutes' => (int)($schedule['activityDurationMinutes'] ?? 0)
                        ];

                        $dayItem['schedule'][] = $scheduleItem;
                    }
                }

                $scheduleDays[] = $dayItem;
            }
        }

        // 替换schedulesDay为scheduleDays以符合接口定义
        unset($result['schedulesDay']);
        $result['scheduleDays'] = $scheduleDays;

        // 转换驼峰命名
        // $result = $this->convertToFrontend($result);

        return success($result);
    }
} 