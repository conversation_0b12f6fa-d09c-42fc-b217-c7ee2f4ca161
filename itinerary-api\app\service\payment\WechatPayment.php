<?php

declare(strict_types=1);

namespace app\service\payment;

use base\exception\BaseException;

class WechatPayment implements PaymentInterface
{
    public function __construct()
    {
        // TODO: 通过配置初始化 EasyWeChat 实例
    }

    public function pay(array $payload): array
    {
        // payload: out_trade_no, total_amount, subject, trade_type(jsapi|native|h5), notify_url, return_url?, openid?
        // 这里先返回模拟数据
        $tradeType = $payload['trade_type'] ?? 'h5';
        $payInfo = [];
        switch ($tradeType) {
            case 'jsapi':
                $payInfo = [
                    'appId' => 'wx123',
                    'timeStamp' => (string)time(),
                    'nonceStr' => uniqid('wx'),
                    'package' => 'prepay_id=mock_prepay_id',
                    'signType' => 'RSA',
                    'paySign' => 'mock_sign',
                ];
                break;
            case 'native':
                $payInfo = [
                    'code_url' => 'weixin://wxpay/bizpayurl?pr=xxxx',
                ];
                break;
            default: // h5
                $payInfo = [
                    'h5_url' => 'https://wxpay.example.com/h5pay?out_trade_no='. $payload['out_trade_no']
                ];
        }
        return [
            'channel' => 'wechat',
            'out_trade_no' => $payload['out_trade_no'],
            'amount' => $payload['total_amount'],
            'trade_type' => $tradeType,
            'pay_info' => $payInfo,
        ];
    }

    public function refund(array $payload): array
    {
        // payload: out_trade_no, refund_amount, out_refund_no
        return [ 'success' => true, 'channel' => 'wechat' ];
    }

    public function query(array $payload): array
    {
        // payload: out_trade_no
        return [ 'trade_state' => 'SUCCESS', 'channel' => 'wechat' ];
    }

    public function notify(array $params): array
    {
        // 验签与解析，返回统一结构
        return [ 'success' => true, 'out_trade_no' => $params['out_trade_no'] ?? '', 'trade_no' => $params['transaction_id'] ?? '', 'raw' => $params ];
    }

    public function cancel(array $payload): array
    {
        return [ 'success' => true, 'channel' => 'wechat' ];
    }
}

