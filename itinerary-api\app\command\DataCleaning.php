<?php

namespace app\command;

use app\model\ItineraryThemeModel;
use support\Cache;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use app\model\CityInfoModel;
use app\model\ItineraryModel;
use app\model\ProductCategoryModel;
use think\db\Query;
use think\facade\Db;

#[AsCommand('Data:cleaning', 'Data cleaning')]
class DataCleaning extends Command
{
    /**
     * @return void
     */
    protected function configure()
    {   
        // 添加选项以执行特定的清洗任务
        $this->addOption('task', 't', InputOption::VALUE_OPTIONAL, '指定要执行的清洗任务，多个任务用逗号分隔');
    }
    // 必须执行的任务
    // 1 缓存城市
    // 2 缓存线路品类
    public function init($output)
    {
        $this->cityInfo();
        $this->overseas();
        $this->processProductCategories($output);
    }

     /**
     * 获取城市列表（按省份分组）
     *
     */
    public function cityInfo()
    {
        // 定义缓存键
        $cacheKey = 'city_info_json';

        // 检查缓存是否存在
        if (Cache::has($cacheKey)) {
            return;
        }

        // 缓存不存在，查询数据库
        // 获取所有城市数据
        $cityInfos = CityInfoModel::mk()
            ->field(['province_name', 'province_code', 'city_name', 'city_code', 'city_initial'])
            ->select();
        
        // 按省份分组处理数据
        $result = [];
        $provinceMap = [];
        
        foreach ($cityInfos as $cityInfo) {
            $provinceCode = $cityInfo->province_code;
            $provinceName = $cityInfo->province_name;
            $cityInitial = strtoupper($cityInfo->city_initial);
            
            // 如果省份不存在，则添加新的省份
            if (!isset($provinceMap[$provinceCode])) {
                $provinceData = [
                    'code' => $provinceCode,
                    'name' => $provinceName,
                    'children' => []
                ];
                $result[] = $provinceData;
                $provinceMap[$provinceCode] = count($result) - 1;
            }
            
            // 添加城市到对应省份的children中
            $cityData = [
                'code' => $cityInfo->city_code,
                'name' => $cityInfo->city_name,
                'index' => $cityInitial
            ];
            $result[$provinceMap[$provinceCode]]['children'][] = $cityData;
        }
        
        // 将结果存入缓存（缓存7天）
        Cache::set($cacheKey, $result, 86400 * 7);
        
    }

    /**
     * 国外城市信息（按国家分组）
     * 
     */
    public function overseas()
    {
        // 定义缓存键
        $cacheKey = 'city_info_overseas';
        
        // 检查缓存是否存在
        if (Cache::has($cacheKey)) {
            return ;
        }
        
        // 缓存不存在，查询数据库
        // 获取所有国家及其城市数据
        $countries = Db::table('countries')
            ->alias('c')
            ->field([
                'c.id as country_id', 
                'c.name as country_name', 
                'c.name_en as country_name_en', 
                'c.initial as country_initial'
            ])
            ->select();
        
        $result = [];
        $countryMap = [];
        
        // 构建国家层级
        foreach ($countries as $country) {
            $countryData = [
                'code' => $country['country_id'],
                'name' => $country['country_name']  . "({$country['country_name_en']})",
                'children' => []
            ];
            
            $result[] = $countryData;
            $countryMap[$country['country_id']] = count($result) - 1;
        }
        
        // 获取并关联城市数据
        $cities = Db::table('cities')
            ->field([
                'id', 
                'country_id',
                'name', 
                'name_en',
                'city_code',
                'poi_id', 
                'initial'
            ])
            ->select();
        
        // 将城市添加到对应国家下
        foreach ($cities as $city) {
            $countryId = $city['country_id'];
            
            // 如果国家存在于结果集中
            if (isset($countryMap[$countryId])) {
                $cityInitial = strtoupper($city['initial'] ?: substr($city['name'], 0, 1));
                
                $cityData = [
                    'name' => $city['name'] . "({$city['name_en']})",
                    'code' => $city['city_code'],
                    'index' => $cityInitial
                ];
                
                $result[$countryMap[$countryId]]['children'][] = $cityData;
            }
        }
        
        // 排序：先按国家首字母排序，再对每个国家下的城市按首字母排序
        foreach ($result as &$country) {
            usort($country['children'], function($a, $b) {
                return $a['index'] <=> $b['index'];
            });
        }
        
        // 将结果存入缓存（缓存1天）
        Cache::set($cacheKey, $result, 86400 * 7);
        
    }
    /**
     * @param InputInterface $input
     * @param OutputInterface $output
     * @return int
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    protected function execute(InputInterface $input, OutputInterface $output): int
    {

        $output->writeln('数据清洗开始');

        // 获取指定要执行的任务
        $specifiedTask = $input->getOption('task');
        $tasks = $specifiedTask ? explode(',', $specifiedTask) : [];
        
        $this->init($output);
        // 如果没有指定任务或指定了'departure_cities'任务
        if (empty($tasks) || in_array('departure_cities', $tasks)) {
            $output->writeln('出发城市');
            $this->processDepartureCities($output);
        }

        // 如果没有指定任务或指定了'destinations'任务
        if (empty($tasks) || in_array('destinations', $tasks)) {
            $output->writeln('目的地');
            $this->processDestinations($output);
        }
        
        // 如果没有指定任务或指定了'travel_days'任务
        if (empty($tasks) || in_array('travel_days', $tasks)) {
            $output->writeln('旅行天数统计与推荐');
            $this->processTravelDays($output);
        }
        
        // 如果没有指定任务或指定了'price_ranges'任务
        if (empty($tasks) || in_array('price_ranges', $tasks)) {
            $output->writeln('价格区间统计');
            $this->processPriceRanges($output);
        }
        
        // 如果没有指定任务或指定了'seasonal'任务
        if (empty($tasks) || in_array('seasonal', $tasks)) {
            $output->writeln('季节性推荐');
            $this->processSeasonalRecommendations($output);
        }
        
        // 如果没有指定任务或指定了'promotions'任务
        if (empty($tasks) || in_array('promotions', $tasks)) {
            $output->writeln('特价促销线路');
            $this->processPromotions($output);
        }
        
        // 如果没有指定任务或指定了'hot_search'任务
        if (empty($tasks) || in_array('hot_search', $tasks)) {
            $output->writeln('热门搜索关键词');
            $this->processHotSearchKeywords($output);
        }
        
        // 如果没有指定任务或指定了'homepage_recommends'任务
        if (empty($tasks) || in_array('homepage_recommends', $tasks)) {
            $output->writeln('首页推荐');
            $this->processHomepageRecommends($output);
        }
        
        // 如果没有指定任务或指定了'special_recommends'任务
        if (empty($tasks) || in_array('special_recommends', $tasks)) {
            $output->writeln('特殊推荐(周边游、国内游、出境游)');
            $this->processSpecialRecommends($output);
            
            // 同时处理页面数据
            $output->writeln('处理页面数据');
            $this->processPageData($output);
        }

        $output->writeln('数据清洗完成');
        return self::SUCCESS;
    }

    /**
     * 处理出发城市数据
     * @param OutputInterface $output
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    private function processDepartureCities(OutputInterface $output): void
    {
        $output->writeln('- 开始处理出发城市数据');
        
        // 获取所有国内城市数据
        $domesticCities = [];

      $cityData = Cache::get('city_info_json');

        
        // 从省份分组数据中提取城市信息
        foreach ($cityData as $province) {
            if (!empty($province['children'])) {
                foreach ($province['children'] as $city) {
                    $domesticCities[$city['code']] = [
                        'cityCode' => $city['code'],
                        'cityName' => $city['name'],
                        'cityInitial' => $city['index'],
                        'isOverseas' => false,
                        'provinceName' => $province['name'],
                        'provinceCode' => $province['code']
                    ];
                }
            }
        }

        // 合并所有城市数据
        $allCities = $domesticCities;
        
        // 将所有城市数据写入缓存
        Cache::set('all_cities', $allCities, 86400 * 7);
        $output->writeln('- 已缓存 ' . count($allCities) . ' 个城市（包含ID和名称）');

        // 获取所有行程数据的出发城市
        $rawItineraries = ItineraryModel::mk()->field('id, departure_city')
            ->where('departure_city', '<>', '')
            ->where('departure_city', 'not null')
            ->select()
            ->toArray();
            
        // 由于departure_city是英文逗号分隔的字符串，需要拆分并单独统计每个城市
        $cityCount = [];
        $cityInfo = [];
        foreach ($rawItineraries as $itinerary) {
            $cities = explode(',', $itinerary['departureCity']);
            foreach ($cities as $cityCode) {
                $cityCode = trim($cityCode);
                if (!empty($cityCode)) {
                    if (!isset($cityCount[$cityCode])) {
                        $cityCount[$cityCode] = 0;

                        // 尝试从城市数据中查找城市信息
                        foreach ($allCities as $code => $cityData) {
                            if ($cityData['cityCode'] === $cityCode || str_starts_with($cityData['cityCode'], $cityCode)) {
                                $cityInfo[$cityCode] = $cityData;
                                break;
                            }
                        }
                    }
                    $cityCount[$cityCode]++;
                }
            }
        }
        
        // 转换为数组格式并按数量排序
        $cities = [];
        foreach ($cityCount as $cityCode => $count) {
            $cityData = [
                'cityCode' => $cityCode,
                'count' => $count
            ];
            
            // 添加城市ID和其他信息（如果有）
            if (isset($cityInfo[$cityCode])) {
                $cityData = array_merge($cityData, $cityInfo[$cityCode]);
            }
            
            $cities[] = $cityData;
        }
        
        // 按出现次数降序排序
        usort($cities, function($a, $b) {
            return $b['count'] - $a['count'];
        });

        // 将城市写入缓存
        Cache::set('departure_cities', $cities, 86400 * 7);
        $output->writeln('- 已缓存 ' . count($cities) . ' 个出发城市');

        // 获取热门城市（前10个）
        $hotCities = array_slice($cities, 0, 10);
        
        // 获取热门城市的封面图
        $hotCitiesWithCover = [];
        
        foreach ($hotCities as $city) {
            $cityName = $city['cityName'];
            $coverImage = '';
            
            // 尝试从已有数据中获取封面图
            if (isset($city['coverImage'])) {
                $coverImage = $city['coverImage'];
            }
            
            $hotCitiesWithCover[] = [
                'cityName' => $cityName,
                'cityCode' => $city['cityCode'] ?? '',
                'count' => $city['count'],
                'coverImage' => $coverImage,
                'isOverseas' => $city['isOverseas'] ?? false
            ];
        }
        
        // 将热门城市封面写入缓存
        Cache::set('hot_departure_cities', $hotCitiesWithCover, 86400 * 7);
        $output->writeln('- 已缓存热门出发城市封面');
        
        // 按字母排序城市列表（用于城市选择器）
        $alphabetCities = [];
        foreach ($cities as $city) {
            $firstLetter = isset($city['cityInitial']) ? substr($city['cityInitial'], 0, 1) : $this->getFirstLetter($city['cityCode']);
            
            if (!isset($alphabetCities[$firstLetter])) {
                $alphabetCities[$firstLetter] = [];
            }
            $alphabetCities[$firstLetter][] = $city;
        }
        
        // 字母顺序排序
        ksort($alphabetCities);
        
        // 将按字母排序的城市列表写入缓存
        Cache::set('alphabet_departure_cities', $alphabetCities, 86400 * 7);
        $output->writeln('- 已缓存按字母排序的出发城市列表');
        
        // 分别缓存国内和国外城市
        $domesticDepartureCities = array_filter($cities, function($city) {
            return !($city['isOverseas'] ?? false);
        });

        Cache::set('domestic_departure_cities', $domesticDepartureCities, 86400 * 7);
        $output->writeln('- 已缓存国内和国外出发城市列表');
    }
    
    /**
     * 获取中文字符的首字母
     * @param string $str
     * @return string
     */
    private function getFirstLetter($str) {
        $firstChar = mb_substr($str, 0, 1, 'utf-8');
        
        // 这里简化处理，实际应有完整的中文转拼音首字母的逻辑
        $letters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
        return strtoupper(substr(iconv('UTF-8', 'gb2312//IGNORE', $firstChar), 0, 1));
    }

    /**
     * 处理线路品类数据
     * @param OutputInterface $output
     */
    private function processProductCategories(OutputInterface $output)
    {
        // 获取所有品类
        $categoryModel = ProductCategoryModel::mk();
        $categories = $categoryModel->where('category_discard', false)
            ->order('sort_order', 'asc')
            ->select()
            ->toArray();
            
        // 将品类列表写入缓存
        Cache::set('product_categories', $categories, 86400 * 7);
        $output->writeln('- 已缓存 ' . count($categories) . ' 个品类');

        // 获取大品类和小品类的关系
        $mainCategories = [
            '境内行程游' => 32006000,
            '境内目的地玩乐' => 32007000,
            '出境行程游' => 32002000,
            '出境目的地玩乐' => 32003000
        ];
        
        // 区分境内和境外分类
        $overseasCategoryIds = [32002000, 32003000]; // 出境行程游、出境目的地玩乐
        
        // 查询所有可用的小品类及其父级关系
        $smallCategories = ProductCategoryModel::mk()->alias('c3')
            ->join('product_category c2', 'c3.parent_id = c2.category_id')
            ->join('product_category c1', 'c2.parent_id = c1.category_id')
            ->where('c3.category_level', 3)
            ->where('c3.access_status', 1)
            ->where('c3.category_discard', 0)
            ->where('c2.access_status', 1)
            ->where('c2.category_discard', 0)
            ->where('c1.access_status', 1)
            ->where('c1.category_discard', 0)
            ->whereIn('c2.category_id', array_values($mainCategories))
            ->field([
                'c3.category_id AS small_category_id',
                'c3.category_name AS small_category_name',
                'c2.category_id AS big_category_id',
                'c2.category_name AS big_category_name',
                'c1.category_id AS top_category_id',
                'c1.category_name AS top_category_name'
            ])
            ->order('c1.category_id, c2.category_id, c3.category_id')
            ->select()
            ->toArray();
        
        // 将小品类按大品类分组
        $categoriesByMain = [];
        foreach ($smallCategories as $category) {
            $bigCategoryId = $category['bigCategoryId'];
            $isOverseas = in_array($bigCategoryId, $overseasCategoryIds);
            
            if (!isset($categoriesByMain[$bigCategoryId])) {
                $categoriesByMain[$bigCategoryId] = [
                    'id' => $bigCategoryId,
                    'name' => $category['bigCategoryName'],
                    'isOverseas' => $isOverseas,
                    'children' => []
                ];
            }
            
            $categoriesByMain[$bigCategoryId]['children'][] = [
                'id' => $category['smallCategoryId'],
                'name' => $category['smallCategoryName'],
                'isOverseas' => $isOverseas
            ];
        }
        
        // 将大品类和小品类的关系写入缓存
        Cache::set('category_relationships', $categoriesByMain, 86400 * 7);
        $output->writeln('- 已缓存大品类和小品类的关系');
        
        // 处理大品类的推荐线路
        $itineraryModel = new ItineraryModel();
        
        foreach ($mainCategories as $mainName => $mainId) {
            // 获取该大品类下所有小品类ID
            $smallCategoryIds = isset($categoriesByMain[$mainId]) 
                ? array_column($categoriesByMain[$mainId]['children'], 'id') 
                : [];
            
            if (empty($smallCategoryIds)) {
                continue;
            }
            
            $isOverseas = in_array($mainId, $overseasCategoryIds);
            
            // 获取该大品类的推荐线路（最多8条）
            $mainRecommends = $itineraryModel->whereIn('category_id', $smallCategoryIds)
                ->where('status', 1)
                ->order('create_time', 'desc')
                ->limit(8)
                ->field('id, title, cover_image, price, original_price, days, nights, departure_city, destination, category_id')
                ->select()
                ->toArray();
            
            // 为每个推荐线路添加isOverseas标记    
            foreach ($mainRecommends as &$recommend) {
                $recommend['isOverseas'] = $isOverseas;
            }
                
            if (!empty($mainRecommends)) {
                Cache::set('big_category_recommends_' . $mainId, $mainRecommends, 86400 * 3);
                $output->writeln('- 已缓存大品类 ' . $mainName . ' 推荐线路 ' . count($mainRecommends) . ' 条');
            }
            
            // 为该大品类获取热门目的地（按照目的地数量排序）
            $hotDestinations = $itineraryModel->whereIn('category_id', $smallCategoryIds)
                ->where('status', 1)
                ->field('destination, COUNT(*) as count')
                ->group('destination')
                ->order('count', 'desc')
                ->limit(5)
                ->select()
                ->toArray();
            
            if (!empty($hotDestinations)) {
                Cache::set('big_category_hot_destinations_' . $mainId, $hotDestinations, 86400 * 3);
                $output->writeln('- 已缓存大品类 ' . $mainName . ' 热门目的地 ' . count($hotDestinations) . ' 个');
            }
            
            // 处理每个小品类的推荐
            if (isset($categoriesByMain[$mainId])) {
                foreach ($categoriesByMain[$mainId]['children'] as $smallCategory) {
                    $smallId = $smallCategory['id'];
                    $smallName = $smallCategory['name'];
                    
                    // 获取该小品类的推荐线路（最多6条）
                    $smallRecommends = $itineraryModel->where('category_id', $smallId)
                        ->where('status', 1)
                        ->order('create_time', 'desc')
                        ->limit(6)
                        ->field('id, title, cover_image, price, original_price, days, nights, departure_city, destination, category_id')
                        ->select()
                        ->toArray();
                        
                    // 为每个推荐线路添加isOverseas标记
                    foreach ($smallRecommends as &$recommend) {
                        $recommend['isOverseas'] = $isOverseas;
                    }
                        
                    if (!empty($smallRecommends)) {
                        Cache::set('small_category_recommends_' . $smallId, $smallRecommends, 86400 * 3);
                        $output->writeln('- 已缓存小品类 ' . $smallName . ' 推荐线路 ' . count($smallRecommends) . ' 条');
                    }
                }
            }
        }
        
        // 处理特殊产品类型
        // 从三级类目中获取产品类型
        $product_type_categories = [];
        
        foreach ($categories as $category) {
            if ($category['categoryLevel'] == 3 && $category['accessStatus'] == 1 && $category['categoryDiscard'] == 0) {
                // 根据分类名称判断产品类型
                $typeName = $category['categoryName'];
                $product_type_categories[$category['categoryId']] = [
                    'id' => $category['categoryId'],
                    'name' => $typeName
                ];
            }
        }
        
        // 获取每个特殊产品类型的推荐线路
        foreach ($product_type_categories as $categoryId => $categoryData) {
            // 获取该品类的推荐线路（最多8条）
            $typeRecommends = $itineraryModel->where('category_id', $categoryId)
                ->where('status', 1)
                ->order('create_time', 'desc')
                ->limit(8)
                ->field('id, title, cover_image, price, original_price, days, nights, departure_city, destination, category_id')
                ->select()
                ->toArray();
                
            if (!empty($typeRecommends)) {
                Cache::set('category_recommends_' . $categoryId, $typeRecommends, 86400 * 3);
                $output->writeln('- 已缓存产品分类 ' . $categoryData['name'] . ' 推荐线路 ' . count($typeRecommends) . ' 条');
            }
        }
        
        // 处理主题标签，提取features字段中的主题标签
        $themes = [];
        $itineraries = $itineraryModel->field('id, features')
            ->where('status', 1)
            ->select()
            ->toArray();
            
        foreach ($itineraries as $itinerary) {
            if (!empty($itinerary['features']) && is_array($itinerary['features'])) {
                foreach ($itinerary['features'] as $feature) {
                    if (!isset($themes[$feature])) {
                        $themes[$feature] = 0;
                    }
                    $themes[$feature]++;
                }
            }
        }
        
        // 按出现次数排序
        arsort($themes);
        
        // 提取前20个热门主题
        $hotThemes = array_slice($themes, 0, 20, true);
        
        // 将热门主题写入缓存
        Cache::set('hot_themes', $hotThemes, 86400 * 7);
        $output->writeln('- 已缓存热门主题标签 ' . count($hotThemes) . ' 个');
        
        // 特殊处理：境内和境外分类
        $domesticCategories = [$mainCategories['境内行程游'], $mainCategories['境内目的地玩乐']];
        $overseasCategories = [$mainCategories['出境行程游'], $mainCategories['出境目的地玩乐']];
        
        // 获取境内线路推荐
        $domesticRecommends = $itineraryModel->whereIn('category_id', $this->getAllSubcategoryIds($domesticCategories, $smallCategories))
            ->where('status', 1)
            ->order('create_time', 'desc')
            ->limit(10)
            ->field('id, title, cover_image, price, original_price, days, nights, departure_city, destination, category_id')
            ->select()
            ->toArray();
            
        // 为境内线路添加标记
        foreach ($domesticRecommends as &$recommend) {
            $recommend['isOverseas'] = false;
        }
            
        if (!empty($domesticRecommends)) {
            Cache::set('domestic_recommends', $domesticRecommends, 86400 * 3);
            $output->writeln('- 已缓存境内线路推荐 ' . count($domesticRecommends) . ' 条');
        }
        
        // 获取境外线路推荐
        $overseasRecommends = $itineraryModel->whereIn('category_id', $this->getAllSubcategoryIds($overseasCategories, $smallCategories))
            ->where('status', 1)
            ->order('create_time', 'desc')
            ->limit(10)
            ->field('id, title, cover_image, price, original_price, days, nights, departure_city, destination, category_id')
            ->select()
            ->toArray();
            
        // 为境外线路添加标记
        foreach ($overseasRecommends as &$recommend) {
            $recommend['isOverseas'] = true;
        }
            
        if (!empty($overseasRecommends)) {
            Cache::set('overseas_recommends', $overseasRecommends, 86400 * 3);
            $output->writeln('- 已缓存境外线路推荐 ' . count($overseasRecommends) . ' 条');
        }
    }
    
    /**
     * 获取指定大品类下所有小品类ID
     * @param array $bigCategoryIds 大品类ID数组
     * @param array $smallCategories 小品类数据
     * @return array 小品类ID数组
     */
    private function getAllSubcategoryIds(array $bigCategoryIds, array $smallCategories): array
    {
        $smallCategoryIds = [];
        foreach ($smallCategories as $category) {
            if (in_array($category['bigCategoryId'], $bigCategoryIds)) {
                $smallCategoryIds[] = $category['smallCategoryId'];
            }
        }
        return $smallCategoryIds;
    }

    /**
     * 处理目的地数据
     * @param OutputInterface $output
     */
    private function processDestinations(OutputInterface $output)
    {
        $output->writeln('- 开始处理目的地数据');
        
        // 获取所有国内城市数据
        $domesticCities = [];
        $cityData = Cache::get('city_info_json');
        
        if (!$cityData) {
            $cityData = [];
        }
        
        // 从省份分组数据中提取城市信息
        foreach ($cityData as $province) {
            if (!empty($province['children'])) {
                foreach ($province['children'] as $city) {
                    $domesticCities[$city['code']] = [
                        'cityCode' => $city['code'],
                        'cityName' => $city['name'],
                        'cityInitial' => $city['index'],
                        'isOverseas' => false,
                        'provinceName' => $province['name'],
                        'provinceCode' => $province['code'],
                        'index' => $city['index'],
                    ];
                }
            }
        }
        
        // 获取所有国外城市数据
        $overseasCities = [];
        $overseasData = Cache::get('city_info_overseas');
        
        if ($overseasData) {
            // 从国家分组数据中提取城市信息
            foreach ($overseasData as $country) {
                if (!empty($country['children'])) {
                    foreach ($country['children'] as $city) {
                        $overseasCities[$city['code']] = [
                            'cityCode' => $city['code'],
                            'cityName' => $city['name'],
                            'cityInitial' => $city['index'],
                            'isOverseas' => true,
                            'countryName' => $country['name'],
                            'countryCode' => $country['code'],
                            'index' => $city['index'],
                        ];
                    }
                }
            }
        }
        
        // 合并所有城市数据
        $allCities = array_merge($domesticCities, $overseasCities);
        
        // 获取所有行程数据
        $itineraryModel = ItineraryModel::mk();
        $rawItineraries = $itineraryModel->field('id, destination')
            ->where('destination', '<>', '')
            ->where('destination', 'not null')
            ->select()
            ->toArray();
            
        // 由于destination是英文逗号分隔的字符串，需要拆分并单独统计每个目的地
        $destCount = [];
        $cityInfo = [];
        
        foreach ($rawItineraries as $itinerary) {
            $destinations = explode(',', $itinerary['destination']);
            foreach ($destinations as $cityCode) {
                $cityCode = trim($cityCode);
                if (!empty($cityCode)) {
                    if (!isset($destCount[$cityCode])) {
                        $destCount[$cityCode] = 0;
                        
                        // 尝试从城市数据中查找城市信息
                        foreach ($allCities as $cityData) {
                            if ($cityData['cityCode'] === $cityCode || str_starts_with($cityData['cityCode'], $cityCode)) {
                                $cityInfo[$cityCode] = $cityData;
                                break;
                            }
                        }
                    }
                    $destCount[$cityCode]++;
                }
            }
        }

        // 转换为数组格式并按数量排序
        $destinationsWithInfo = [];
        foreach ($destCount as $cityCode => $count) {
            $destData = [
                'cityName' => $cityCode,
                'cityCode' => '',
                'count' => $count
            ];
            
            // 添加城市详细信息（如果有）
            if (isset($cityInfo[$cityCode])) {
                $destData = array_merge($destData, $cityInfo[$cityCode]);
            }
            
            $destinationsWithInfo[] = $destData;
        }
        
        // 按出现次数降序排序
        usort($destinationsWithInfo, function($a, $b) {
            return $b['count'] - $a['count'];
        });

        // 将目的地写入缓存
        Cache::set('destinations', $destinationsWithInfo, 86400 * 7);
        $output->writeln('- 已缓存 ' . count($destinationsWithInfo) . ' 个目的地（包含城市ID和名称）');

        // 获取热门目的地（前15个）
        $hotDestinations = array_slice($destinationsWithInfo, 0, 15);
        
        // 确保热门目的地都有封面图
        $hotDestinationsWithCover = [];
        
        foreach ($hotDestinations as $destination) {
            $destName = $destination['cityName'];
            $coverImage = $destination['coverImage'] ?? '';
            

            
            $hotDestinationsWithCover[] = [
                'cityName' => $destName,
                'cityCode' => $destination['cityCode'] ?? '',
                'count' => $destination['count'],
                'coverImage' => $coverImage,
                'isOverseas' => $destination['isOverseas'] ?? false
            ];
            
            // 获取该目的地的推荐线路（最多6条）
            $destRecommends = $itineraryModel->where('destination', 'like', "%{$destName}%")
                ->where('status', 1)
                ->order('create_time', 'desc')
                ->limit(6)
                ->field('id, title, cover_image, price, original_price, days, nights, departure_city, category_id')
                ->select()
                ->toArray();
                
            if (!empty($destRecommends)) {
                Cache::set('destination_recommends_' . md5($destName), $destRecommends, 86400 * 3);
                $output->writeln('- 已缓存目的地 ' . $destName . ' 推荐线路 ' . count($destRecommends) . ' 条');
            }
        }
        
        // 将热门目的地封面写入缓存
        Cache::set('hot_destinations', $hotDestinationsWithCover, 86400 * 7);
        $output->writeln('- 已缓存热门目的地封面');
        
        // 按大洲/国家/地区分类目的地
        $destinationsByRegion = [
            '国内' => [
                '华东' => [],
                '华南' => [],
                '华中' => [],
                '华北' => [],
                '西南' => [],
                '西北' => [],
                '东北' => [],
                '港澳台' => [],
            ],
            '亚洲' => [],
            '欧洲' => [],
            '北美洲' => [],
            '南美洲' => [],
            '大洋洲' => [],
            '非洲' => [],
        ];
        
        // 分类目的地到各个区域
        foreach ($destinationsWithInfo as $destination) {
            $destName = $destination['cityName'];
            $isOverseas = $destination['isOverseas'] ?? false;
            
            if (!$isOverseas) {
                // 国内城市分区域处理
                $provinceCode = $destination['provinceCode'] ?? '';
                
                // 根据省份编码判断区域
                if (in_array(substr($provinceCode, 0, 2), ['31', '32', '33', '34', '35', '36', '37'])) {
                    // 上海、江苏、浙江、安徽、福建、江西、山东属于华东
                    $destinationsByRegion['国内']['华东'][] = $destination;
                } elseif (in_array(substr($provinceCode, 0, 2), ['44', '45', '46', '81', '82'])) {
                    // 广东、广西、海南、香港、澳门属于华南
                    $destinationsByRegion['国内']['华南'][] = $destination;
                } elseif (in_array(substr($provinceCode, 0, 2), ['42', '43', '41'])) {
                    // 湖北、湖南、河南属于华中
                    $destinationsByRegion['国内']['华中'][] = $destination;
                } elseif (in_array(substr($provinceCode, 0, 2), ['11', '12', '13', '14', '15'])) {
                    // 北京、天津、河北、山西、内蒙古属于华北
                    $destinationsByRegion['国内']['华北'][] = $destination;
                } elseif (in_array(substr($provinceCode, 0, 2), ['50', '51', '52', '53', '54'])) {
                    // 重庆、四川、贵州、云南、西藏属于西南
                    $destinationsByRegion['国内']['西南'][] = $destination;
                } elseif (in_array(substr($provinceCode, 0, 2), ['61', '62', '63', '64', '65'])) {
                    // 陕西、甘肃、青海、宁夏、新疆属于西北
                    $destinationsByRegion['国内']['西北'][] = $destination;
                } elseif (in_array(substr($provinceCode, 0, 2), ['21', '22', '23'])) {
                    // 辽宁、吉林、黑龙江属于东北
                    $destinationsByRegion['国内']['东北'][] = $destination;
                } elseif (in_array(substr($provinceCode, 0, 2), ['71', '81', '82'])) {
                    // 台湾、香港、澳门属于港澳台
                    $destinationsByRegion['国内']['港澳台'][] = $destination;
                }
            } else {
                // 国外城市处理
                // 根据国家判断大洲，这里需要根据实际数据结构判断
                // 简单示例，实际应该有完整的国家-大洲映射
                $countryName = $destination['countryName'] ?? '';
                
                if (in_array($countryName, ['日本', '韩国', '新加坡', '泰国', '马来西亚', '印度尼西亚', '越南'])) {
                    $destinationsByRegion['亚洲'][] = $destination;
                } elseif (in_array($countryName, ['英国', '法国', '德国', '意大利', '西班牙'])) {
                    $destinationsByRegion['欧洲'][] = $destination;
                } elseif (in_array($countryName, ['美国', '加拿大', '墨西哥'])) {
                    $destinationsByRegion['北美洲'][] = $destination;
                } elseif (in_array($countryName, ['巴西', '阿根廷', '智利'])) {
                    $destinationsByRegion['南美洲'][] = $destination;
                } elseif (in_array($countryName, ['澳大利亚', '新西兰'])) {
                    $destinationsByRegion['大洋洲'][] = $destination;
                } elseif (in_array($countryName, ['埃及', '南非', '摩洛哥'])) {
                    $destinationsByRegion['非洲'][] = $destination;
                }
            }
        }
        
        // 将按区域分类的目的地写入缓存
        Cache::set('destinations_by_region', $destinationsByRegion, 86400 * 7);
        $output->writeln('- 已缓存按区域分类的目的地');
        
        // 分别缓存国内和国外目的地
        $domesticDestinations = array_filter($destinationsWithInfo, function($destination) {
            return !($destination['isOverseas'] ?? false);
        });
        
        $overseasDestinations = array_filter($destinationsWithInfo, function($destination) {
            return ($destination['isOverseas'] ?? false);
        });
        
        Cache::set('domestic_destinations', $domesticDestinations, 86400 * 7);
        Cache::set('overseas_destinations', $overseasDestinations, 86400 * 7);
        $output->writeln('- 已缓存国内和国外目的地列表');
    }
    
    /**
     * 处理旅行天数统计与推荐
     * @param OutputInterface $output
     */
    private function processTravelDays(OutputInterface $output)
    {
        $itineraryModel = new ItineraryModel();
        
        // 按天数统计线路数量
        $daysStats = $itineraryModel->field('days, COUNT(*) as count')
            ->where('status', 1)
            ->group('days')
            ->order('days', 'asc')
            ->select()
            ->toArray();
            
        // 将天数统计写入缓存
        Cache::set('travel_days_stats', $daysStats, 86400 * 7);
        $output->writeln('- 已缓存旅行天数统计');
        
        // 预设天数区间
        $dayRanges = [
            '短途' => [1, 3],  // 1-3天
            '中途' => [4, 7],  // 4-7天
            '长途' => [8, 14], // 8-14天
            '长线' => [15, 999] // 15天以上
        ];
        
        // 为每个天数区间获取推荐线路
        foreach ($dayRanges as $rangeName => $range) {
            $rangeRecommends = $itineraryModel->where('status', ItineraryModel::ONLINE_STATUS)
                ->where('days', '>=', $range[0])
                ->where('days', '<=', $range[1])
                ->order('create_time', 'desc')
                ->limit(8)
                ->field('id, title, cover_image, price, original_price, days, nights, departure_city, destination')
                ->append(['id'])
                ->select()
                ->toArray();
                
            if (!empty($rangeRecommends)) {
                Cache::set('day_range_recommends_' . $rangeName, $rangeRecommends, 86400 * 3);
                $output->writeln('- 已缓存' . $rangeName . '线路推荐 ' . count($rangeRecommends) . ' 条');
            }
        }
        
        // 周末游推荐(2-3天)
        $weekendRecommends = $itineraryModel->where('status', ItineraryModel::ONLINE_STATUS)
            ->where('days', '>=', 2)
            ->where('days', '<=', 3)
            ->order('create_time', 'desc')
            ->limit(6)
            ->field('id, title, cover_image, price, original_price, days, nights, departure_city, destination')
            ->append(['id'])
            ->select()
            ->toArray();
            
        if (!empty($weekendRecommends)) {
            Cache::set('weekend_travel_recommends', $weekendRecommends, 86400 * 3);
            $output->writeln('- 已缓存周末游推荐 ' . count($weekendRecommends) . ' 条');
        }
        
        // 小长假推荐(4-5天)
        $shortHolidayRecommends = $itineraryModel->where('status', ItineraryModel::ONLINE_STATUS)
            ->where('days', '>=', 4)
            ->where('days', '<=', 5)
            ->order('create_time', 'desc')
            ->limit(6)
            ->field('id, title, cover_image, price, original_price, days, nights, departure_city, destination')
            ->append(['id'])
            ->select()
            ->toArray();
            
        if (!empty($shortHolidayRecommends)) {
            Cache::set('short_holiday_recommends', $shortHolidayRecommends, 86400 * 3);
            $output->writeln('- 已缓存小长假推荐 ' . count($shortHolidayRecommends) . ' 条');
        }
        
        // 年假推荐(7-15天)
        $annualLeaveRecommends = $itineraryModel->where('status', ItineraryModel::ONLINE_STATUS)
            ->where('days', '>=', 7)
            ->where('days', '<=', 15)
            ->order('create_time', 'desc')
            ->limit(6)
            ->field('id, title, cover_image, price, original_price, days, nights, departure_city, destination')
            ->append(['id'])
            ->select()
            ->toArray();
            
        if (!empty($annualLeaveRecommends)) {
            Cache::set('annual_leave_recommends', $annualLeaveRecommends, 86400 * 3);
            $output->writeln('- 已缓存年假旅行推荐 ' . count($annualLeaveRecommends) . ' 条');
        }
    }
    
    /**
     * 处理价格区间统计
     * @param OutputInterface $output
     */
    private function processPriceRanges(OutputInterface $output)
    {
        // 价格区间定义
        $priceRanges = [
            '2000以下' => [0, 2000],
            '2000-5000' => [2000, 5000],
            '5000-10000' => [5000, 10000],
            '10000-20000' => [10000, 20000],
            '20000以上' => [20000, 999999]
        ];
        
        $rangesStats = [];
        
        // 统计每个价格区间的线路数量
        foreach ($priceRanges as $rangeName => $range) {
            $count = ItineraryModel::mk()->where('status', ItineraryModel::ONLINE_STATUS)
                ->where('price', '>=', $range[0])
                ->where('price', '<', $range[1])
                ->count();
                
            $rangesStats[$rangeName] = $count;
            
            // 为每个价格区间获取推荐线路
            $rangeRecommends = ItineraryModel::mk()->where('status', ItineraryModel::ONLINE_STATUS)
                ->where('price', '>=', $range[0])
                ->where('price', '<', $range[1])
                ->order('create_time', 'desc')
                ->limit(6)
                ->field('id, title, cover_image, price, original_price, days, nights, departure_city, destination')
                ->append(['id'])
                ->select()
                ->toArray();
                
            if (!empty($rangeRecommends)) {
                $safeRangeName = str_replace(['-', ' ', '+'], '_', $rangeName);
                Cache::set('price_range_recommends_' . $safeRangeName, $rangeRecommends, 86400 * 3);
                $output->writeln('- 已缓存价格区间' . $rangeName . '推荐 ' . count($rangeRecommends) . ' 条');
            }
        }
        
        // 将价格区间统计写入缓存
        Cache::set('price_ranges_stats', $rangesStats, 86400 * 7);
        $output->writeln('- 已缓存价格区间统计');
        
        // 经济实惠线路推荐（价格低于平均价的30%）
        $avgPrice = ItineraryModel::mk()->where('status', 1)->avg('price');
        $economyPrice = $avgPrice * 0.7; // 30%低于平均价
        
        $economyRecommends = ItineraryModel::mk()->where('status', ItineraryModel::ONLINE_STATUS)
            ->where('price', '<', $economyPrice)
            ->order('create_time', 'desc')
            ->limit(8)
            ->field('id, title, cover_image, price, original_price, days, nights, departure_city, destination')
            ->append(['id'])
            ->select()
            ->toArray();
            
        if (!empty($economyRecommends)) {
            Cache::set('economy_recommends', $economyRecommends, 86400 * 3);
            $output->writeln('- 已缓存经济实惠线路推荐 ' . count($economyRecommends) . ' 条');
        }
        
        // 高端精品线路推荐（价格高于平均价的50%）
        $luxuryPrice = $avgPrice * 1.5; // 50%高于平均价
        
        $luxuryRecommends = ItineraryModel::mk()->where('status', ItineraryModel::ONLINE_STATUS)
            ->where('price', '>', $luxuryPrice)
            ->order('create_time', 'desc')
            ->limit(8)
            ->field('id, title, cover_image, price, original_price, days, nights, departure_city, destination')
            ->append(['id'])
            ->select()
            ->toArray();
            
        if (!empty($luxuryRecommends)) {
            Cache::set('luxury_recommends', $luxuryRecommends, 86400 * 3);
            $output->writeln('- 已缓存高端精品线路推荐 ' . count($luxuryRecommends) . ' 条');
        }
    }
    
    /**
     * 处理季节性推荐
     * @param OutputInterface $output
     */
    private function processSeasonalRecommendations(OutputInterface $output)
    {
        $itineraryModel = new ItineraryModel();
        $currentMonth = (int)date('n');
        
        // 季节定义
        $seasons = [
            '春季' => [3, 4, 5],
            '夏季' => [6, 7, 8],
            '秋季' => [9, 10, 11],
            '冬季' => [12, 1, 2]
        ];
        
        // 确定当前季节
        $currentSeason = '';
        foreach ($seasons as $season => $months) {
            if (in_array($currentMonth, $months)) {
                $currentSeason = $season;
                break;
            }
        }
        
        // 下一个季节
        $seasonKeys = array_keys($seasons);
        $currentSeasonIndex = array_search($currentSeason, $seasonKeys);
        $nextSeasonIndex = ($currentSeasonIndex + 1) % count($seasonKeys);
        $nextSeason = $seasonKeys[$nextSeasonIndex];
        
        // 当前季节热门目的地 (实际实现应基于历史数据或预设)
        $currentSeasonDestinations = [];
        switch ($currentSeason) {
            case '春季':
                $currentSeasonDestinations = [
                    ['name' => '杭州', 'cityCode' => '330100'],
                    ['name' => '苏州', 'cityCode' => '320500'],
                    ['name' => '武汉', 'cityCode' => '420100'],
                    ['name' => '厦门', 'cityCode' => '350200'],
                    ['name' => '青岛', 'cityCode' => '370200']
                ];
                break;
            case '夏季':
                $currentSeasonDestinations = [
                    ['name' => '三亚', 'cityCode' => '460200'],
                    ['name' => '青岛', 'cityCode' => '370200'],
                    ['name' => '厦门', 'cityCode' => '350200'],
                    ['name' => '北戴河', 'cityCode' => '130303'],
                    ['name' => '大连', 'cityCode' => '210200']
                ];
                break;
            case '秋季':
                $currentSeasonDestinations = [
                    ['name' => '北京', 'cityCode' => '110000'],
                    ['name' => '张家界', 'cityCode' => '430800'],
                    ['name' => '九寨沟', 'cityCode' => '513200'],
                    ['name' => '黄山', 'cityCode' => '341000'],
                    ['name' => '香格里拉', 'cityCode' => '533400']
                ];
                break;
            case '冬季':
                $currentSeasonDestinations = [
                    ['name' => '哈尔滨', 'cityCode' => '230100'],
                    ['name' => '长白山', 'cityCode' => '222400'],
                    ['name' => '三亚', 'cityCode' => '460200'],
                    ['name' => '海口', 'cityCode' => '460100'],
                    ['name' => '昆明', 'cityCode' => '530100']
                ];
                break;
        }
        
        $seasonalRecommends = [];
        foreach ($currentSeasonDestinations as $destination) {
            $destRecommends = $itineraryModel->where('status', ItineraryModel::ONLINE_STATUS)
                ->where(function ($query) use ($destination) {
                    // 尝试通过目的地名称或cityCode查询
                    $query->whereFindInSet('destination', $destination['cityCode']);
                })
                ->order('create_time', 'desc')
                ->limit(3)
                ->field('id, title, cover_image, price, original_price, days, nights, departure_city, destination')
                ->select()
                ->toArray();
                
            if (!empty($destRecommends)) {
                $seasonalRecommends = array_merge($seasonalRecommends, $destRecommends);
            }
        }
        
        // 将当季推荐写入缓存
        Cache::set('current_season_recommends', [
            'season' => $currentSeason,
            'destinations' => $currentSeasonDestinations,
            'itineraries' => $seasonalRecommends
        ], 86400 * 7);
        $output->writeln('- 已缓存' . $currentSeason . '推荐 ' . count($seasonalRecommends) . ' 条');
        
        // 下一季节预热推荐
        $nextSeasonDestinations = [];
        switch ($nextSeason) {
            case '春季':
                $nextSeasonDestinations = [
                    ['name' => '杭州', 'cityCode' => '330100'],
                    ['name' => '苏州', 'cityCode' => '320500'],
                    ['name' => '武汉', 'cityCode' => '420100'],
                    ['name' => '厦门', 'cityCode' => '350200'],
                    ['name' => '青岛', 'cityCode' => '370200']
                ];
                break;
            case '夏季':
                $nextSeasonDestinations = [
                    ['name' => '三亚', 'cityCode' => '460200'],
                    ['name' => '青岛', 'cityCode' => '370200'],
                    ['name' => '厦门', 'cityCode' => '350200'],
                    ['name' => '北戴河', 'cityCode' => '130303'],
                    ['name' => '大连', 'cityCode' => '210200']
                ];
                break;
            case '秋季':
                $nextSeasonDestinations = [
                    ['name' => '北京', 'cityCode' => '110000'],
                    ['name' => '张家界', 'cityCode' => '430800'],
                    ['name' => '九寨沟', 'cityCode' => '513200'],
                    ['name' => '黄山', 'cityCode' => '341000'],
                    ['name' => '香格里拉', 'cityCode' => '533400']
                ];
                break;
            case '冬季':
                $nextSeasonDestinations = [
                    ['name' => '哈尔滨', 'cityCode' => '230100'],
                    ['name' => '长白山', 'cityCode' => '222400'],
                    ['name' => '三亚', 'cityCode' => '460200'],
                    ['name' => '海口', 'cityCode' => '460100'],
                    ['name' => '昆明', 'cityCode' => '530100']
                ];
                break;
        }
        
        $nextSeasonalRecommends = [];
        foreach ($nextSeasonDestinations as $destination) {
            $destRecommends = $itineraryModel->where('status', ItineraryModel::ONLINE_STATUS)
                ->where(function (Query $query) use ($destination) {
                    // 尝试通过目的地名称或cityCode查询
                    $query->whereFindInSet('destination', $destination['cityCode']);
                })
                ->order('create_time', 'desc')
                ->limit(2)
                ->field('id, title, cover_image, price, original_price, days, nights, departure_city, destination')
                ->append(['id'])
                ->select()
                ->toArray();
                
            if (!empty($destRecommends)) {
                $nextSeasonalRecommends = array_merge($nextSeasonalRecommends, $destRecommends);
            }
        }
        
        // 将下季预热推荐写入缓存
        Cache::set('next_season_recommends', [
            'season' => $nextSeason,
            'destinations' => $nextSeasonDestinations,
            'itineraries' => $nextSeasonalRecommends
        ], 86400 * 14);
        $output->writeln('- 已缓存' . $nextSeason . '预热推荐 ' . count($nextSeasonalRecommends) . ' 条');
    }

    /**
     * 处理特价促销线路
     * @param OutputInterface $output
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    private function processPromotions(OutputInterface $output): void
    {
        // 获取有折扣的线路（原价与现价不同的线路）
        $discountedItineraries =ItineraryModel::mk()->where('status', ItineraryModel::ONLINE_STATUS)
            ->field('id, title, cover_image, price, original_price, days, nights, departure_city, destination,(original_price - price) / original_price as order_price')
            ->append(['id'])
            ->where('original_price', '>', 'price') // 原价大于现价
            ->order('order_price', 'desc') // 按折扣幅度排序
            ->select()
            ->toArray();
        // 计算折扣率并标记超值优惠
        foreach ($discountedItineraries as &$item) {
            $discountRate = ($item['originalPrice'] - $item['price']) / $item['originalPrice'];
            $item['discountRate'] = round($discountRate * 100);
            $item['isSuperDeal'] = $discountRate > 0.3; // 折扣超过30%标记为超值
        }
        
        // 将特价线路写入缓存
        Cache::set('discounted_itineraries', $discountedItineraries, 86400); // 每天更新
        $output->writeln('- 已缓存特价促销线路 ' . count($discountedItineraries) . ' 条');
        
        // 闪购限时特价（示例数据，实际应动态生成或从数据库读取）
        $flashDeals = array_slice($discountedItineraries, 0, 5);
        foreach ($flashDeals as &$deal) {
            // 设置随机过期时间（12-24小时内）
            $deal['expire_time'] = time() + rand(43200, 86400);
            // 设置限量数量
            $deal['limited_count'] = rand(3, 10);
        }
        
        // 将闪购特价写入缓存
        Cache::set('flash_deals', $flashDeals, 86400 * 1); // 每天更新
        $output->writeln('- 已缓存闪购限时特价 ' . count($flashDeals) . ' 条');
    }
    
    /**
     * 处理热门搜索关键词
     * @param OutputInterface $output
     */
    private function processHotSearchKeywords(OutputInterface $output)
    {
        // 实际应从搜索日志中统计热搜词，这里使用模拟数据
        $hotSearchKeywords = [
            ['keyword' => '三亚', 'count' => 8760, 'is_hot' => true],
            ['keyword' => '普吉岛', 'count' => 6543, 'is_hot' => true],
            ['keyword' => '日本', 'count' => 6210, 'is_hot' => true],
            ['keyword' => '巴厘岛', 'count' => 5876, 'is_hot' => true],
            ['keyword' => '泰国', 'count' => 4982, 'is_hot' => false],
            ['keyword' => '丽江', 'count' => 4521, 'is_hot' => false],
            ['keyword' => '九寨沟', 'count' => 3987, 'is_hot' => false],
            ['keyword' => '张家界', 'count' => 3654, 'is_hot' => false],
            ['keyword' => '北京', 'count' => 3210, 'is_hot' => false],
            ['keyword' => '厦门', 'count' => 2987, 'is_hot' => false],
            ['keyword' => '上海', 'count' => 2876, 'is_hot' => false],
            ['keyword' => '云南', 'count' => 2543, 'is_hot' => false],
            ['keyword' => '洪崖洞', 'count' => 2132, 'is_hot' => false],
            ['keyword' => '大理', 'count' => 1987, 'is_hot' => false],
            ['keyword' => '重庆', 'count' => 1876, 'is_hot' => false],
        ];
        
        // 将热搜关键词写入缓存
        Cache::set('hot_search_keywords', $hotSearchKeywords, 86400 * 1); // 每天更新
        $output->writeln('- 已缓存热搜关键词 ' . count($hotSearchKeywords) . ' 条');
        
        // 为热搜词准备匹配的线路推荐
        $itineraryModel = new ItineraryModel();
        foreach (array_slice($hotSearchKeywords, 0, 5) as $hotKeyword) {
            $keyword = $hotKeyword['keyword'];
            
            // 搜索匹配的线路
            $matchedItineraries = $itineraryModel->where('status', ItineraryModel::ONLINE_STATUS)
                ->where(function ($query) use ($keyword) {
                    $query->where('title', 'like', "%{$keyword}%")
                        ->whereOr('destination', 'like', "%{$keyword}%")
                        ->whereOr('description', 'like', "%{$keyword}%");
                })
                ->order('create_time', 'desc')
                ->limit(6)
                ->field('id, title, cover_image, price, original_price, days, nights, departure_city, destination')
                ->append(['id'])
                ->select()
                ->toArray();
                
            if (!empty($matchedItineraries)) {
                Cache::set('hot_keyword_itineraries_' . md5($keyword), $matchedItineraries, 86400 * 3);
                $output->writeln('- 已缓存热搜词"' . $keyword . '"匹配线路 ' . count($matchedItineraries) . ' 条');
            }
        }
    }

    /**
     * 处理首页大图推荐
     * @param OutputInterface $output
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    private function processHomepageRecommends(OutputInterface $output)
    {
        $output->writeln('- 开始处理首页大图推荐');
        
        // 首页大图推荐
        $homepageRecommends = [];
        $itineraryModel = ItineraryModel::mk();
        
        // 尝试从缓存中获取分类关系
        $categoryRelationships = Cache::get('category_relationships');

            $output->writeln('- 使用缓存的分类关系');
            
            // 大分类推荐
            foreach ($categoryRelationships as $categoryId => $categoryData) {
                $categoryName = $categoryData['name'];
                $childrenIds = array_column($categoryData['children'], 'id');
                
                if (empty($childrenIds)) {
                    continue;
                }
                
                // 随机获取一个该大分类下的线路作为首页推荐
                $recommend = $itineraryModel->whereIn('category_id', $childrenIds)
                    ->where('status', ItineraryModel::ONLINE_STATUS)
                    ->orderRaw('RAND()')
                    ->field('id, title, description, cover_image, price, original_price, days, nights, category_id')
                    ->append(['id'])
                    ->find();
                    
                if ($recommend) {
                    $recommend = $recommend->toArray();
                    $recommend['categoryName'] = $categoryName;
                    $homepageRecommends[$recommend['id']] = $recommend;
                }
                
                // 为每个小分类获取推荐
                foreach ($categoryData['children'] as $smallCategory) {
                    $smallId = $smallCategory['id'];
                    $smallName = $smallCategory['name'];
                    
                    $recommend = $itineraryModel->where('category_id', $smallId)
                        ->where('status', ItineraryModel::ONLINE_STATUS)
                        ->orderRaw('RAND()')
                        ->field('id, title, description, cover_image, price, original_price, days, nights, category_id')
                        ->append(['id'])
                        ->find();
                        
                    if ($recommend) {
                        $recommend = $recommend->toArray();
                        $recommend['categoryName'] = $categoryName;
                        $homepageRecommends[$recommend['id']] = $recommend;
                    }
                }
            }
        
        
        // 将首页大图推荐写入缓存
        Cache::set('homepage_recommends', $homepageRecommends, 86400 * 3);
        $output->writeln('- 已缓存首页大图推荐 ' . count($homepageRecommends) . ' 个品类');
        
        // 获取最热门的线路作为轮播图推荐
        $carouselRecommends = $itineraryModel->where('status', ItineraryModel::ONLINE_STATUS)
            ->order('id', 'desc')
            ->limit(5)
            ->field('id, title, cover_image, price, original_price, days, nights')
            ->append(['id'])
            ->select()
            ->toArray();
            
        if (!empty($carouselRecommends)) {
            Cache::set('carousel_recommends', $carouselRecommends, 86400 * 3);
            $output->writeln('- 已缓存首页轮播图推荐 ' . count($carouselRecommends) . ' 条');
        }
        
        // 获取限时特价推荐
        $flashSaleRecommends = $itineraryModel->where('status', ItineraryModel::ONLINE_STATUS)
            ->where('original_price', '>', 'price')
            ->orderRaw('(original_price - price) / original_price DESC')
            ->limit(3)
            ->field('id, title, cover_image, price, original_price, days, nights')
            ->append(['id'])
            ->select()
            ->toArray();
            
        if (!empty($flashSaleRecommends)) {
            // 为每个特价商品添加过期时间
            foreach ($flashSaleRecommends as &$item) {
                $item['expire_time'] = time() + rand(43200, 86400); // 12-24小时内过期
            }
            
            Cache::set('flash_sale_recommends', $flashSaleRecommends, 86400 * 1);
            $output->writeln('- 已缓存首页限时特价推荐 ' . count($flashSaleRecommends) . ' 条');
        }
    }

    /**
     * 处理特殊推荐(周边游、国内游、出境游)
     * @param OutputInterface $output
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    private function processSpecialRecommends(OutputInterface $output): void
    {
        $output->writeln('- 开始处理特殊推荐线路数据');
        $itineraryModel = ItineraryModel::mk();
        
        // 周边游推荐 - 短距离、短天数的行程
        $nearbyRecommends = $itineraryModel->where('status', ItineraryModel::ONLINE_STATUS)
            ->where('days', '<=', 3) // 3天以内的短途旅行
            ->order('create_time', 'desc')
            ->limit(4)
            ->field('id, title, cover_image, price, original_price, days, nights, departure_city, destination, category_id, themes_ids')
            ->append(['id'])
            ->withAttr('themes_ids', function ($value) {
                return str2Arr($value);
            })
            ->select()
            ->toArray();
            
        if (!empty($nearbyRecommends)) {
            Cache::set('nearby_travel_recommends', $nearbyRecommends, 86400 * 3); // 缓存3天
            $output->writeln('- 已缓存周边游推荐 ' . count($nearbyRecommends) . ' 条');
        }
        
        // 国内游推荐 - 从国内分类中选择
        $mainCategories = [
            '境内行程游' => 32006000,
            '境内目的地玩乐' => 32007000,
        ];
        
        // 获取国内品类下的所有分类ID
        $categoryRelationships = Cache::get('category_relationships');
        $domesticCategoryIds = [];
        
        if (!empty($categoryRelationships)) {
            foreach ($mainCategories as $mainId) {
                if (isset($categoryRelationships[$mainId]) && !empty($categoryRelationships[$mainId]['children'])) {
                    $childCategories = $categoryRelationships[$mainId]['children'];
                    foreach ($childCategories as $child) {
                        $domesticCategoryIds[] = $child['id'];
                    }
                }
            }
        }
        
        // 如果没有获取到分类ID，使用主分类ID
        if (empty($domesticCategoryIds)) {
            $domesticCategoryIds = array_values($mainCategories);
        }
        
        $domesticRecommends = $itineraryModel->whereIn('category_id', $domesticCategoryIds)
            ->where('status', ItineraryModel::ONLINE_STATUS)
            ->order('create_time', 'desc')
            ->limit(4)
            ->field('id, title, cover_image, price, original_price, days, nights, departure_city, destination, category_id, themes_ids')
            ->append(['id'])
            ->withAttr('themes_ids', function ($value) {
                return str2Arr($value);
            })
            ->select()
            ->toArray();
            
        foreach ($domesticRecommends as &$recommend) {
            $recommend['isOverseas'] = false;
        }
            
        if (!empty($domesticRecommends)) {
            Cache::set('domestic_travel_recommends', $domesticRecommends, 86400 * 3); // 缓存3天
            $output->writeln('- 已缓存国内游推荐 ' . count($domesticRecommends) . ' 条');
        }
        
        // 出境游推荐 - 从出境分类中选择
        $overseasCategories = [
            '出境行程游' => 32002000,
            '出境目的地玩乐' => 32003000
        ];
        
        $overseasCategoryIds = [];
        
        if (!empty($categoryRelationships)) {
            foreach ($overseasCategories as $mainId) {
                if (isset($categoryRelationships[$mainId]) && !empty($categoryRelationships[$mainId]['children'])) {
                    $childCategories = $categoryRelationships[$mainId]['children'];
                    foreach ($childCategories as $child) {
                        $overseasCategoryIds[] = $child['id'];
                    }
                }
            }
        }
        
        // 如果没有获取到分类ID，使用主分类ID
        if (empty($overseasCategoryIds)) {
            $overseasCategoryIds = array_values($overseasCategories);
        }
        
        $overseasRecommends = $itineraryModel->whereIn('category_id', $overseasCategoryIds)
            ->where('status', ItineraryModel::ONLINE_STATUS)
            ->order('create_time', 'desc')
            ->limit(4)
            ->field('id, title, cover_image, price, original_price, days, nights, departure_city, destination, category_id, themes_ids')
            ->append(['id'])
            ->withAttr('themes_ids', function ($value) {
                return str2Arr($value);
            })
            ->select()
            ->toArray();
            
        foreach ($overseasRecommends as &$recommend) {
            $recommend['isOverseas'] = true;
        }
            
        if (!empty($overseasRecommends)) {
            Cache::set('overseas_travel_recommends', $overseasRecommends, 86400 * 3); // 缓存3天
            $output->writeln('- 已缓存出境游推荐 ' . count($overseasRecommends) . ' 条');
        }
    }

    /**
     * 处理页面数据（周边游、国内游、出境游）
     * @param OutputInterface $output
     */
    private function processPageData(OutputInterface $output): void
    {
        $output->writeln('- 开始处理页面数据（周边游、国内游、出境游）');
        
        // 获取缓存的基础数据
        $nearbyRecommends = Cache::get('nearby_travel_recommends', []);
        $domesticRecommends = Cache::get('domestic_travel_recommends', []);
        $overseasRecommends = Cache::get('overseas_travel_recommends', []);
        $hotDestinations = Cache::get('hot_destinations', []);
        
        // 从线路推荐数据中提取所有使用的主题ID
        $usedThemeIds = [];
        
        // 从各类型线路中提取主题ID
        $nearbyThemeIds = [];
        $domesticThemeIds = [];
        $overseasThemeIds = [];
        
        // 处理周边游线路的主题
        foreach ($nearbyRecommends as $recommend) {
            if (isset($recommend['themesIds']) && !empty($recommend['themesIds'])) {
                foreach ($recommend['themesIds'] as $themeId) {
                    $usedThemeIds[$themeId] = true;
                    $nearbyThemeIds[$themeId] = true;
                }
            }
        }
        
        // 处理国内游线路的主题
        foreach ($domesticRecommends as $recommend) {
            if (isset($recommend['themesIds']) && !empty($recommend['themesIds'])) {
                foreach ($recommend['themesIds'] as $themeId) {
                    $usedThemeIds[$themeId] = true;
                    $domesticThemeIds[$themeId] = true;
                }
            }
        }
        
        // 处理出境游线路的主题
        foreach ($overseasRecommends as $recommend) {
            if (isset($recommend['themesIds']) && !empty($recommend['themesIds'])) {
                foreach ($recommend['themesIds'] as $themeId) {
                    $usedThemeIds[$themeId] = true;
                    $overseasThemeIds[$themeId] = true;
                }
            }
        }
        
        // 获取正在使用的主题信息
        $usedThemeIds = array_keys($usedThemeIds);
        $nearbyThemeIds = array_keys($nearbyThemeIds);
        $domesticThemeIds = array_keys($domesticThemeIds);
        $overseasThemeIds = array_keys($overseasThemeIds);
        
        $output->writeln('- 从线路中提取到 ' . count($usedThemeIds) . ' 个正在使用的主题ID');
        $output->writeln('- 周边游线路使用了 ' . count($nearbyThemeIds) . ' 个主题');
        $output->writeln('- 国内游线路使用了 ' . count($domesticThemeIds) . ' 个主题');
        $output->writeln('- 出境游线路使用了 ' . count($overseasThemeIds) . ' 个主题');
                  // 查询主题表获取主题详情
 
        $themeData = [];
        if (!empty($usedThemeIds)) {
            $themes = ItineraryThemeModel::mk()
            ->whereIn('id', $usedThemeIds)
            ->append(['id'])
            ->select();
        
            foreach ($themes as $theme) {
                $themeData[$theme['id']] = [
                    'id' => $theme['id'],
                    'name' => $theme['name'],
                    'icon' => $theme['icon'] ?: 'icon-theme-default',
                    'imageUrl' => $theme['image_url'] ?: '',
                    'code' => $theme['id']
                ];
            }
            $output->writeln('- 获取到 ' . count($themeData) . ' 个主题详情');
        }
        
        // 使用各类型线路关联的主题
        $nearbyThemes = [];
        $domesticThemes = [];
        $overseasThemes = [];
        
        // 构建周边游主题列表
        foreach ($nearbyThemeIds as $themeId) {
            if (isset($themeData[$themeId])) {
                $nearbyThemes[] = $themeData[$themeId];
            }
        }
        
        // 构建国内游主题列表
        foreach ($domesticThemeIds as $themeId) {
            if (isset($themeData[$themeId])) {
                $domesticThemes[] = $themeData[$themeId];
            }
        }
        
        // 构建出境游主题列表
        foreach ($overseasThemeIds as $themeId) {
            if (isset($themeData[$themeId])) {
                $overseasThemes[] = $themeData[$themeId];
            }
        }
        
        // 确保各类型主题数量达到要求
        $allThemesArray = array_values($themeData);
        shuffle($allThemesArray);
        
        // 确保周边游至少有4个主题
        if (count($nearbyThemes) < 4) {
            $output->writeln('- 周边游主题不足4个，从其他主题中补充');
            $additionalThemes = [];
            $usedIds = array_column($nearbyThemes, 'id');
            
            foreach ($allThemesArray as $theme) {
                if (!in_array($theme['id'], $usedIds)) {
                    $additionalThemes[] = $theme;
                    $usedIds[] = $theme['id'];
                    if (count($additionalThemes) >= 4 - count($nearbyThemes)) {
                        break;
                    }
                }
            }
            
            $nearbyThemes = array_merge($nearbyThemes, $additionalThemes);
        }
        
        // 确保国内游至少有5个主题
        if (count($domesticThemes) < 5) {
            $output->writeln('- 国内游主题不足5个，从其他主题中补充');
            $additionalThemes = [];
            $usedIds = array_column($domesticThemes, 'id');
            
            foreach ($allThemesArray as $theme) {
                if (!in_array($theme['id'], $usedIds)) {
                    $additionalThemes[] = $theme;
                    $usedIds[] = $theme['id'];
                    if (count($additionalThemes) >= 5 - count($domesticThemes)) {
                        break;
                    }
                }
            }
            
            $domesticThemes = array_merge($domesticThemes, $additionalThemes);
        }
        
        // 确保出境游至少有5个主题
        if (count($overseasThemes) < 5) {
            $output->writeln('- 出境游主题不足5个，从其他主题中补充');
            $additionalThemes = [];
            $usedIds = array_column($overseasThemes, 'id');
            
            foreach ($allThemesArray as $theme) {
                if (!in_array($theme['id'], $usedIds)) {
                    $additionalThemes[] = $theme;
                    $usedIds[] = $theme['id'];
                    if (count($additionalThemes) >= 5 - count($overseasThemes)) {
                        break;
                    }
                }
            }
            
            $overseasThemes = array_merge($overseasThemes, $additionalThemes);
        }
        
        // 1. 周边游页面数据
        $nearbyPageData = [
            'title' => '周末好去处，轻松休闲周边游',
            'subTitle' => '短途出行，周末放松，精选本地周边精彩行程',
            'banner' => [
                'image' => 'https://images.unsplash.com/photo-1533105079780-92b9be482077?q=80&w=1000',
                'title' => '周边游玩，乐享休闲时光'
            ],
            'hotDestinations' => $this->getPageHotDestinations($hotDestinations, false, true, 6), // 获取6个热门国内短途目的地
            'recommendedRoutes' => $nearbyRecommends,
            'themes' => $nearbyThemes,
            // 添加更多周边游相关的过滤选项
            'filters' => [
                'days' => [
                    ['name' => '1日游', 'value' => 1],
                    ['name' => '2日游', 'value' => 2],
                    ['name' => '3日游', 'value' => 3]
                ],
                'priceRanges' => [
                    ['name' => '500以下', 'min' => 0, 'max' => 500],
                    ['name' => '500-1000', 'min' => 500, 'max' => 1000],
                    ['name' => '1000-2000', 'min' => 1000, 'max' => 2000]
                ]
            ]
        ];
        
        // 2. 国内游页面数据
        $domesticPageData = [
            'title' => '探索祖国大好河山，畅游锦绣中华',
            'subTitle' => '遍览名山大川，体验多彩中国文化',
            'banner' => [
                'image' => 'https://images.unsplash.com/photo-1547981609-4b6bfe67ca0b?q=80&w=1000',
                'title' => '国内精选目的地，带你领略中华风光'
            ],
            'hotDestinations' => $this->getPageHotDestinations($hotDestinations, false, false, 8), // 获取8个国内目的地
            'recommendedRoutes' => $domesticRecommends,
            'themes' => $domesticThemes,
            'regions' => [
                ['name' => '华东', 'code' => 'east_china'],
                ['name' => '华南', 'code' => 'south_china'],
                ['name' => '华北', 'code' => 'north_china'],
                ['name' => '西南', 'code' => 'southwest'],
                ['name' => '西北', 'code' => 'northwest'],
                ['name' => '东北', 'code' => 'northeast']
            ],
            'filters' => [
                'days' => [
                    ['name' => '3-5日', 'min' => 3, 'max' => 5],
                    ['name' => '6-8日', 'min' => 6, 'max' => 8],
                    ['name' => '9日以上', 'min' => 9, 'max' => 30]
                ],
                'priceRanges' => [
                    ['name' => '3000以下', 'min' => 0, 'max' => 3000],
                    ['name' => '3000-5000', 'min' => 3000, 'max' => 5000],
                    ['name' => '5000-8000', 'min' => 5000, 'max' => 8000],
                    ['name' => '8000以上', 'min' => 8000, 'max' => 99999]
                ]
            ]
        ];
        
        // 3. 出境游页面数据
        $overseasPageData = [
            'title' => '环游世界，领略异国风情',
            'subTitle' => '让旅行带你探索世界的每一个角落',
            'banner' => [
                'image' => 'https://images.unsplash.com/photo-1530870110042-98b2cb110834?q=80&w=1000',
                'title' => '全球精选目的地，带你领略世界风光'
            ],
            'hotDestinations' => $this->getPageHotDestinations($hotDestinations, true, false, 8), // 获取8个国外目的地
            'recommendedRoutes' => $overseasRecommends,
            'themes' => $overseasThemes,
            'continents' => [
                ['name' => '亚洲', 'code' => 'asia'],
                ['name' => '欧洲', 'code' => 'europe'],
                ['name' => '北美洲', 'code' => 'north_america'],
                ['name' => '大洋洲', 'code' => 'oceania'],
                ['name' => '南美洲', 'code' => 'south_america'],
                ['name' => '非洲', 'code' => 'africa']
            ],
            'filters' => [
                'days' => [
                    ['name' => '5-7日', 'min' => 5, 'max' => 7],
                    ['name' => '8-10日', 'min' => 8, 'max' => 10],
                    ['name' => '11日以上', 'min' => 11, 'max' => 30]
                ],
                'priceRanges' => [
                    ['name' => '10000以下', 'min' => 0, 'max' => 10000],
                    ['name' => '10000-15000', 'min' => 10000, 'max' => 15000],
                    ['name' => '15000-20000', 'min' => 15000, 'max' => 20000],
                    ['name' => '20000以上', 'min' => 20000, 'max' => 999999]
                ]
            ]
        ];
        
        // 缓存页面数据
        Cache::set('nearby_travel_page_data', $nearbyPageData, 86400 * 3); // 缓存3天
        $output->writeln('- 已缓存周边游页面数据');
        
        Cache::set('domestic_travel_page_data', $domesticPageData, 86400 * 3); // 缓存3天
        $output->writeln('- 已缓存国内游页面数据');
        
        Cache::set('overseas_travel_page_data', $overseasPageData, 86400 * 3); // 缓存3天
        $output->writeln('- 已缓存出境游页面数据');
    }
    
    /**
     * 从热门目的地中筛选适合特定页面的目的地
     * @param array $allDestinations 所有热门目的地
     * @param bool $isOverseas 是否境外
     * @param bool $isShortDistance 是否短途
     * @param int $limit 限制数量
     * @return array
     */
    private function getPageHotDestinations(array $allDestinations, bool $isOverseas, bool $isShortDistance = false, int $limit = 6): array
    {
        // 筛选出符合条件的目的地
        $filteredDestinations = array_filter($allDestinations, function($dest) use ($isOverseas) {
            return isset($dest['isOverseas']) && $dest['isOverseas'] === $isOverseas;
        });
        
        // 如果是短途，优先选择天数少的目的地
        if ($isShortDistance) {
            // 这里假设我们已经有了目的地对应的平均天数数据
            // 实际应用中可能需要通过查询数据库获取
            usort($filteredDestinations, function($a, $b) {
                return ($a['avgDays'] ?? 7) - ($b['avgDays'] ?? 7);
            });
        }
        
        // 限制数量
        return array_slice($filteredDestinations, 0, $limit);
    }
}
