<?php

declare(strict_types=1);

namespace app\service\payment;

use app\model\PaymentModel;
use base\exception\BaseException;

class PaymentFactory
{
    public static function make(string $channel): PaymentInterface
    {
        switch ($channel) {
            case PaymentModel::METHOD_WECHAT:
            case 'wechat':
                return new WechatPayment();
            case PaymentModel::METHOD_ALIPAY:
            case 'alipay':
                return new AlipayPayment();
            case PaymentModel::METHOD_XHS:
            case 'xhs':
                return new XhsPayment();
            default:
                throw new BaseException('不支持的支付方式: ' . $channel, 400);
        }
    }
}

