<?php

declare(strict_types=1);

namespace app\model;

use base\base\BaseModel;
use think\model\relation\HasMany;
use think\model\relation\BelongsToMany;

class LiveRoomModel extends BaseModel {
    /**
     * 设置表名
     * @var string
     */
    protected $name = 'live_room';

    /**
     * 设置主键
     * @var string
     */
    protected $pk = 'id';

    /**
     * 允许写入的字段
     * @var array
     */
    protected $field = [
        'id',
        'title',
        'status',
        'description',
        'create_time',
        'update_time',
        'delete_time'
    ];

    /**
     * 类型转换
     * @var array
     */
    protected $type = [
        'id' => 'integer',
        'status' => 'integer',
        'create_time' => 'integer',
        'update_time' => 'integer',
        'delete_time' => 'integer'
    ];

    /**
     * 关联直播商品
     * @return HasMany
     */
    public function liveProducts(): HasMany
    {
        return $this->hasMany(LiveRoomProductModel::class, 'live_room_id', 'id');
    }

    /**
     * 关联统计数据
     * @return HasMany
     */
    public function stats(): HasMany
    {
        return $this->hasMany(LiveRoomStatsModel::class, 'live_room_id', 'id');
    }

    /**
     * 通过中间表关联行程商品
     * @return BelongsToMany
     */
    public function itineraries(): BelongsToMany
    {
        return $this->belongsToMany(
            ItineraryModel::class,
            LiveRoomProductModel::class,
            'live_room_id',
            'product_id'
        );
    }

    /**
     * 获取直播间列表
     * @param array $where 查询条件
     * @param int $page 页码
     * @param int $pageSize 每页数量
     * @return array
     */
    public function getList(array $where = [], int $page = 1, int $pageSize = 10): array
    {
        $query = $this->where($where);

        $total = $query->count();
        $list = $query->page($page, $pageSize)
            ->order('sort_order desc, id desc')
            ->select()
            ->toArray();

        return [
            'list' => $list,
            'total' => $total,
            'page' => $page,
            'page_size' => $pageSize
        ];
    }

    /**
     * 获取直播间详情
     * @param int $id 直播间ID
     * @return array|null
     */
    public function getDetail(int $id): ?array
    {
        $data = $this->where('id', $id)->find();
        return $data ? $data->toArray() : null;
    }

    /**
     * 获取推荐直播间
     * @param int $limit 数量限制
     * @return array
     */
    public function getFeatured(int $limit = 10): array
    {
        return $this->where('is_featured', 1)
            ->where('delete_time', 0)
            ->order('sort_order desc, id desc')
            ->limit($limit)
            ->select()
            ->toArray();
    }

    /**
     * 获取正在直播的房间
     * @param int $limit 数量限制
     * @return array
     */
    public function getLiveRooms(int $limit = 10): array
    {
        return $this->where('delete_time', 0)
            ->limit($limit)
            ->select()
            ->toArray();
    }
}
