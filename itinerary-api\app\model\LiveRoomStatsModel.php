<?php

declare(strict_types=1);

namespace app\model;

use base\base\BaseModel;
use think\model\relation\BelongsTo;

class LiveRoomStatsModel extends BaseModel
{
    /**
     * 设置表名
     * @var string
     */
    protected $name = 'live_room_stats';

    /**
     * 设置主键
     * @var string
     */
    protected $pk = 'id';

    /**
     * 允许写入的字段
     * @var array
     */
    protected $field = [
        'id',
        'live_room_id',
        'date',
        'total_viewers',
        'peak_viewers',
        'total_likes',
        'total_shares',
        'total_orders',
        'total_sales',
        'duration_minutes',
        'create_time',
        'update_time'
    ];

    /**
     * 类型转换
     * @var array
     */
    protected $type = [
        'id' => 'integer',
        'live_room_id' => 'integer',
        'total_viewers' => 'integer',
        'peak_viewers' => 'integer',
        'total_likes' => 'integer',
        'total_shares' => 'integer',
        'total_orders' => 'integer',
        'total_sales' => 'float',
        'duration_minutes' => 'integer',
        'create_time' => 'integer',
        'update_time' => 'integer'
    ];

    /**
     * 关联直播间
     * @return BelongsTo
     */
    public function liveRoom(): BelongsTo
    {
        return $this->belongsTo(LiveRoomModel::class, 'live_room_id', 'id');
    }

    /**
     * 获取直播间统计数据
     * @param int $liveRoomId 直播间ID
     * @param string $startDate 开始日期
     * @param string $endDate 结束日期
     * @return array
     */
    public function getStats(int $liveRoomId, string $startDate = '', string $endDate = ''): array
    {
        $query = $this->where('live_room_id', $liveRoomId);
        
        if ($startDate) {
            $query->where('date', '>=', $startDate);
        }
        
        if ($endDate) {
            $query->where('date', '<=', $endDate);
        }
        
        return $query->order('date desc')->select()->toArray();
    }

    /**
     * 获取统计汇总数据
     * @param int $liveRoomId 直播间ID
     * @param string $startDate 开始日期
     * @param string $endDate 结束日期
     * @return array
     */
    public function getSummary(int $liveRoomId, string $startDate = '', string $endDate = ''): array
    {
        $query = $this->where('live_room_id', $liveRoomId);
        
        if ($startDate) {
            $query->where('date', '>=', $startDate);
        }
        
        if ($endDate) {
            $query->where('date', '<=', $endDate);
        }
        
        $result = $query->field([
            'SUM(total_viewers) as total_viewers',
            'MAX(peak_viewers) as max_peak_viewers',
            'SUM(total_likes) as total_likes',
            'SUM(total_shares) as total_shares',
            'SUM(total_orders) as total_orders',
            'SUM(total_sales) as total_sales',
            'SUM(duration_minutes) as total_duration'
        ])->find();
        
        return $result ? $result->toArray() : [];
    }

    /**
     * 创建或更新统计数据
     * @param int $liveRoomId 直播间ID
     * @param string $date 日期
     * @param array $data 统计数据
     * @return bool
     */
    public function createOrUpdate(int $liveRoomId, string $date, array $data): bool
    {
        $existing = $this->where('live_room_id', $liveRoomId)
            ->where('date', $date)
            ->find();
        
        $time = time();
        $data['update_time'] = $time;
        
        if ($existing) {
            return $this->where('id', $existing->id)->update($data) !== false;
        } else {
            $data['live_room_id'] = $liveRoomId;
            $data['date'] = $date;
            $data['create_time'] = $time;
            return $this->insert($data) !== false;
        }
    }

    /**
     * 获取热门直播间排行
     * @param string $startDate 开始日期
     * @param string $endDate 结束日期
     * @param int $limit 数量限制
     * @param string $orderBy 排序字段
     * @return array
     */
    public function getTopLiveRooms(string $startDate = '', string $endDate = '', int $limit = 10, string $orderBy = 'total_viewers'): array
    {
        $query = $this->with(['liveRoom']);
        
        if ($startDate) {
            $query->where('date', '>=', $startDate);
        }
        
        if ($endDate) {
            $query->where('date', '<=', $endDate);
        }
        
        $validOrderFields = ['total_viewers', 'peak_viewers', 'total_likes', 'total_shares', 'total_orders', 'total_sales'];
        if (!in_array($orderBy, $validOrderFields)) {
            $orderBy = 'total_viewers';
        }
        
        return $query->field([
            'live_room_id',
            "SUM({$orderBy}) as total_value",
            'SUM(total_viewers) as total_viewers',
            'MAX(peak_viewers) as max_peak_viewers',
            'SUM(total_likes) as total_likes',
            'SUM(total_shares) as total_shares',
            'SUM(total_orders) as total_orders',
            'SUM(total_sales) as total_sales'
        ])
        ->group('live_room_id')
        ->order('total_value desc')
        ->limit($limit)
        ->select()
        ->toArray();
    }

    /**
     * 获取日期范围内的趋势数据
     * @param int $liveRoomId 直播间ID
     * @param string $startDate 开始日期
     * @param string $endDate 结束日期
     * @return array
     */
    public function getTrendData(int $liveRoomId, string $startDate, string $endDate): array
    {
        return $this->where('live_room_id', $liveRoomId)
            ->where('date', '>=', $startDate)
            ->where('date', '<=', $endDate)
            ->field([
                'date',
                'total_viewers',
                'peak_viewers',
                'total_likes',
                'total_shares',
                'total_orders',
                'total_sales',
                'duration_minutes'
            ])
            ->order('date asc')
            ->select()
            ->toArray();
    }
}
