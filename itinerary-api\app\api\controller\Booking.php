<?php

declare(strict_types=1);

namespace app\api\controller;

use app\model\PaymentModel;
use app\model\ReservationModel;
use app\service\BookingService;
use base\exception\BaseException;
use base\helper\Output;
use support\Request;

class Booking
{
    /**
     * @var BookingService
     */
    private BookingService $bookingService;

    /**
     * 构造函数
     */
    public function __construct()
    {
        $this->bookingService = new BookingService();
    }

    /**
     * 用户下单
     *
     * @param Request $request
     * @return \support\Response
     */
    public function createOrder(Request $request)
    {
        try {
            // 获取行程ID
            $itineraryId = $request->post('itinerary_id');

            if (empty($itineraryId)) {
                throw new BaseException('行程ID不能为空', 400);
            }

            // 获取用户ID
            $userId = $request->user->id ?? null;

            if (empty($userId)) {
                throw new BaseException('用户未登录', 401);
            }

            // 创建订单
            $result = $this->bookingService->createOrder($itineraryId, $userId);

            return Output::success($result);
        } catch (BaseException $e) {
            return Output::error($e->getCode(), $e->getMessage());
        } catch (\Exception $e) {
            return Output::error(500, '创建订单失败：' . $e->getMessage());
        }
    }

    /**
     * 创建预约
     *
     * @param Request $request
     * @return \support\Response
     */
    public function createReservation(Request $request)
    {
        try {
            // 获取订单ID
            $orderId = $request->post('order_id');

            if (empty($orderId)) {
                throw new BaseException('订单ID不能为空', 400);
            }

            // 获取预约信息
            $name = $request->post('name');
            $phone = $request->post('phone');
            $travelDate = $request->post('travel_date');
            $adultCount = (int)$request->post('adult_count', 1);
            $childCount = (int)$request->post('child_count', 0);
            $occupancies = (int)$request->post('occupancies', []);

            // 验证必填项
            if (empty($name)) {
                throw new BaseException('联系人姓名不能为空', 400);
            }

            if (empty($phone)) {
                throw new BaseException('联系电话不能为空', 400);
            }

            if (empty($travelDate)) {
                throw new BaseException('出行日期不能为空', 400);
            }

            // 验证人数
            if ($adultCount <= 0) {
                throw new BaseException('成人人数必须大于0', 400);
            }

            if ($childCount < 0) {
                throw new BaseException('儿童人数不能为负数', 400);
            }

            // 验证日期格式
            if (!strtotime($travelDate)) {
                throw new BaseException('日期格式不正确', 400);
            }
            // 验证出行人信息
            if (!is_array($occupancies) || count($occupancies) != ($adultCount + $childCount)) {
                throw new BaseException('出行人信息数量与成人和儿童人数不符', 400);
            }

            // 创建预约
            $reservationData = [
                'name' => $name,
                'phone' => $phone,
                'travel_date' => $travelDate,
                'adult_count' => $adultCount,
                'child_count' => $childCount,
                'occupancies' => $occupancies,
            ];

            $result = $this->bookingService->createReservation((int)$orderId, $reservationData);

            return Output::success($result);
        } catch (BaseException $e) {
            return Output::error($e->getCode(), $e->getMessage());
        } catch (\Exception $e) {
            return Output::error(500, '创建预约失败：' . $e->getMessage());
        }
    }

    /**
     * 支付加价
     *
     * @param Request $request
     * @return \support\Response
     */
    public function paySurcharge(Request $request)
    {
        try {
            // 获取支付ID
            $paymentId = $request->post('payment_id');

            if (empty($paymentId)) {
                throw new BaseException('支付ID不能为空', 400);
            }

            // 支付加价
            $result = $this->bookingService->paySurcharge($paymentId);

            return Output::success($result);
        } catch (BaseException $e) {
            return Output::error($e->getCode(), $e->getMessage());
        } catch (\Exception $e) {
            return Output::error(500, '支付失败：' . $e->getMessage());
        }
    }

    /**
     * 取消预约
     *
     * @param Request $request
     * @return \support\Response
     */
    public function cancelReservation(Request $request)
    {
        try {
            // 获取预约ID
            $reservationId = $request->post('reservation_id');

            if (empty($reservationId)) {
                throw new BaseException('预约ID不能为空', 400);
            }

            // 获取用户ID（假设已通过中间件设置）
            $userId = $request->user->id ?? null;

            if (empty($userId)) {
                throw new BaseException('用户未登录', 401);
            }

            // 验证预约是否属于当前用户
            $reservation = ReservationModel::where('id', $reservationId)
                ->where('user_id', $userId)
                ->where('delete_time', 0)
                ->find();

            if (!$reservation) {
                throw new BaseException('预约不存在或不属于当前用户', 403);
            }

            // 取消预约
            $result = $this->bookingService->cancelReservation($reservationId);

            return Output::success($result);
        } catch (BaseException $e) {
            return Output::error($e->getCode(), $e->getMessage());
        } catch (\Exception $e) {
            return Output::error(500, '取消预约失败：' . $e->getMessage());
        }
    }

    /**
     * 支付宝支付回调通知
     *
     * @param Request $request
     * @return \support\Response
     */
    public function alipayNotify(Request $request)
    {
        try {
            // 获取支付宝回调参数
            $params = $request->post();

            if (empty($params)) {
                return response('fail');
            }

            // 处理支付通知
            $result = $this->bookingService->handlePaymentNotify($params);

            return $result ? response('success') : response('fail');
        } catch (\Exception $e) {
            // 记录错误日志但返回成功以避免支付宝重复通知
            // 实际项目中应该记录详细日志并设置告警
            return response('fail');
        }
    }

    /**
     * 支付宝支付同步返回
     *
     * @param Request $request
     * @return \support\Response
     */
    public function alipayReturn(Request $request)
    {
        try {
            // 获取支付宝回调参数
            $params = $request->get();
            $outTradeNo = $params['out_trade_no'] ?? '';

            if (empty($outTradeNo)) {
                throw new BaseException('订单号不能为空', 400);
            }

            // 查询支付状态
            $payment = PaymentModel::where('payment_no', $outTradeNo)
                ->where('delete_time', 0)
                ->find();

            if (!$payment) {
                throw new BaseException('支付记录不存在', 404);
            }

            // 根据支付类型返回不同的结果
            $data = [
                'payment_no' => $outTradeNo,
                'status' => $payment['status'],
                'payment_type' => $payment['payment_type'],
                'order_id' => $payment['order_id']
            ];

            return Output::success($data);
        } catch (BaseException $e) {
            return Output::error($e->getCode(), $e->getMessage());
        } catch (\Exception $e) {
            return Output::error(500, '处理支付返回失败：' . $e->getMessage());
        }
    }
}
