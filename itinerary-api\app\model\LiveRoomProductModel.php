<?php

declare(strict_types=1);

namespace app\model;

use base\base\BaseModel;
use think\model\relation\BelongsTo;
use think\facade\Db;

class LiveRoomProductModel extends BaseModel
{
    // 状态常量
    const STATUS_ACTIVE = 'active';
    const STATUS_INACTIVE = 'inactive';

    /**
     * 设置表名
     * @var string
     */
    protected $name = 'live_room_product';

    /**
     * 设置主键
     * @var string
     */
    protected $pk = 'id';

    /**
     * 允许写入的字段
     * @var array
     */
    protected $field = [
        'id',
        'live_room_id',
        'product_id',
        'product_type',
        'special_price',
        'discount_rate',
        'stock_limit',
        'sold_count',
        'is_featured',
        'sort_order',
        'status',
        'create_time',
        'update_time',
        'delete_time'
    ];

    /**
     * 类型转换
     * @var array
     */
    protected $type = [
        'id' => 'integer',
        'live_room_id' => 'integer',
        'product_id' => 'integer',
        'special_price' => 'float',
        'discount_rate' => 'float',
        'stock_limit' => 'integer',
        'sold_count' => 'integer',
        'is_featured' => 'boolean',
        'sort_order' => 'integer',
        'create_time' => 'integer',
        'update_time' => 'integer',
        'delete_time' => 'integer'
    ];

    /**
     * 关联直播间
     * @return BelongsTo
     */
    public function liveRoom(): BelongsTo
    {
        return $this->belongsTo(LiveRoomModel::class, 'live_room_id', 'id');
    }

    /**
     * 关联行程商品
     * @return BelongsTo
     */
    public function itinerary(): BelongsTo
    {
        return $this->belongsTo(ItineraryModel::class, 'product_id', 'id');
    }

    /**
     * 获取直播间商品列表
     * @param int $liveRoomId 直播间ID
     * @param array $where 额外查询条件
     * @param int $page 页码
     * @param int $pageSize 每页数量
     * @return array
     */
    public function getListByLiveRoom(int $liveRoomId, array $where = [], int $page = 1, int $pageSize = 10): array
    {
        $query = $this->where('live_room_id', $liveRoomId)
            ->where('delete_time', 0);
        
        if (!empty($where)) {
            $query->where($where);
        }
        
        $total = $query->count();
        $list = $query->with(['itinerary'])
            ->page($page, $pageSize)
            ->order('is_featured desc, sort_order desc, id desc')
            ->select()
            ->toArray();
        
        return [
            'list' => $list,
            'total' => $total,
            'page' => $page,
            'page_size' => $pageSize
        ];
    }

    /**
     * 获取直播间推荐商品
     * @param int $liveRoomId 直播间ID
     * @param int $limit 数量限制
     * @return array
     */
    public function getFeaturedProducts(int $liveRoomId, int $limit = 5): array
    {
        return $this->where('live_room_id', $liveRoomId)
            ->where('is_featured', 1)
            ->where('status', self::STATUS_ACTIVE)
            ->where('delete_time', 0)
            ->with(['itinerary'])
            ->order('sort_order desc, id desc')
            ->limit($limit)
            ->select()
            ->toArray();
    }

    /**
     * 检查商品是否已添加到直播间
     * @param int $liveRoomId 直播间ID
     * @param int $productId 商品ID
     * @return bool
     */
    public function isProductExists(int $liveRoomId, int $productId): bool
    {
        return $this->where('live_room_id', $liveRoomId)
            ->where('product_id', $productId)
            ->where('delete_time', 0)
            ->count() > 0;
    }

    /**
     * 增加销售数量
     * @param int $id 记录ID
     * @param int $count 增加数量
     * @return bool
     */
    public function addSoldCount(int $id, int $count = 1): bool
    {
        return $this->where('id', $id)->inc('sold_count', $count)->update() !== false;
    }

    /**
     * 检查库存是否充足
     * @param int $id 记录ID
     * @param int $quantity 需要数量
     * @return bool
     */
    public function checkStock(int $id, int $quantity): bool
    {
        $product = $this->find($id);
        if (!$product) {
            return false;
        }

        // 如果没有设置库存限制，则认为库存充足
        if ($product->stock_limit === null) {
            return true;
        }

        return ($product->stock_limit - $product->sold_count) >= $quantity;
    }

    /**
     * 获取商品的实际价格
     * @param int $id 记录ID
     * @return float|null
     */
    public function getActualPrice(int $id): ?float
    {
        $product = $this->with(['itinerary'])->find($id);
        if (!$product || !$product->itinerary) {
            return null;
        }

        // 如果设置了直播间特价，使用特价
        if ($product->special_price !== null && $product->special_price > 0) {
            return $product->special_price;
        }

        // 如果设置了折扣率，计算折扣价
        if ($product->discount_rate !== null && $product->discount_rate > 0) {
            return $product->itinerary->price * ($product->discount_rate / 100);
        }

        // 否则使用原价
        return $product->itinerary->price;
    }

    /**
     * 批量同步直播间商品（存在则更新，不存在则新增，不在新列表中则删除）
     * @param int $liveRoomId 直播间ID
     * @param array $products 商品数据数组，每项至少包含 product_id
     * @return bool
     */
    public function batchAddProducts(int $liveRoomId, array $products): bool
    {
        if (empty($products)) {
            return true;
        }

        $time = time();

        // 规范化输入：以 product_id 为键
        $incoming = [];
        foreach ($products as $item) {
            if (!isset($item['product_id'])) {
                // 跳过无效项
                continue;
            }
            $pid = (int)$item['product_id'];
            $incoming[$pid] = [
                'live_room_id' => $liveRoomId,
                'product_id' => $pid,
                'product_type' => $item['product_type'] ?? 'itinerary',
                'special_price' => $item['special_price'] ?? null,
                'discount_rate' => $item['discount_rate'] ?? null,
                'stock_limit' => $item['stock_limit'] ?? null,
                'is_featured' => (int)($item['is_featured'] ?? 0),
                'sort_order' => (int)($item['sort_order'] ?? 0),
                'status' => $item['status'] ?? self::STATUS_ACTIVE,
                'update_time' => $time,
            ];
        }

        // 开启事务
        return Db::transaction(function () use ($liveRoomId, $incoming, $time) {
            // 查询现有商品（包含已软删除的）
            $existingAll = $this->where('live_room_id', $liveRoomId)
                ->column('*', 'product_id');

            $toInsert = [];
            $handledProductIds = [];

            // 遍历新列表：存在则更新，不存在则加入插入集合
            foreach ($incoming as $pid => $row) {
                if (isset($existingAll[$pid])) {
                    // 恢复软删除并更新字段
                    $row['delete_time'] = 0;
                    $this->where('id', $existingAll[$pid]['id'])->update($row);
                } else {
                    $row['create_time'] = $time;
                    $toInsert[] = $row;
                }
                $handledProductIds[] = $pid;
            }

            // 插入新增记录
            if (!empty($toInsert)) {
                $this->insertAll($toInsert);
            }

            // 仅对当前未删除的老商品执行删除逻辑
            $existingActiveIds = [];
            foreach ($existingAll as $pid => $row) {
                if ((int)($row['delete_time'] ?? 0) === 0) {
                    $existingActiveIds[] = $pid;
                }
            }
            $toDelete = array_diff($existingActiveIds, $handledProductIds);
            if (!empty($toDelete)) {
                $this->where('live_room_id', $liveRoomId)
                    ->whereIn('product_id', $toDelete)
                    ->update(['delete_time' => $time, 'update_time' => $time]);
            }

            return true;
        });
    }
}
