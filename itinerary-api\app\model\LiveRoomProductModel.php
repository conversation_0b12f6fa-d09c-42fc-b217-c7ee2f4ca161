<?php

declare(strict_types=1);

namespace app\model;

use base\base\BaseModel;
use think\model\relation\BelongsTo;

class LiveRoomProductModel extends BaseModel
{
    // 状态常量
    const STATUS_ACTIVE = 'active';
    const STATUS_INACTIVE = 'inactive';

    /**
     * 设置表名
     * @var string
     */
    protected $name = 'live_room_product';

    /**
     * 设置主键
     * @var string
     */
    protected $pk = 'id';

    /**
     * 允许写入的字段
     * @var array
     */
    protected $field = [
        'id',
        'live_room_id',
        'product_id',
        'product_type',
        'special_price',
        'discount_rate',
        'stock_limit',
        'sold_count',
        'is_featured',
        'sort_order',
        'status',
        'create_time',
        'update_time',
        'delete_time'
    ];

    /**
     * 类型转换
     * @var array
     */
    protected $type = [
        'id' => 'integer',
        'live_room_id' => 'integer',
        'product_id' => 'integer',
        'special_price' => 'float',
        'discount_rate' => 'float',
        'stock_limit' => 'integer',
        'sold_count' => 'integer',
        'is_featured' => 'boolean',
        'sort_order' => 'integer',
        'create_time' => 'integer',
        'update_time' => 'integer',
        'delete_time' => 'integer'
    ];

    /**
     * 关联直播间
     * @return BelongsTo
     */
    public function liveRoom(): BelongsTo
    {
        return $this->belongsTo(LiveRoomModel::class, 'live_room_id', 'id');
    }

    /**
     * 关联行程商品
     * @return BelongsTo
     */
    public function itinerary(): BelongsTo
    {
        return $this->belongsTo(ItineraryModel::class, 'product_id', 'id');
    }

    /**
     * 获取直播间商品列表
     * @param int $liveRoomId 直播间ID
     * @param array $where 额外查询条件
     * @param int $page 页码
     * @param int $pageSize 每页数量
     * @return array
     */
    public function getListByLiveRoom(int $liveRoomId, array $where = [], int $page = 1, int $pageSize = 10): array
    {
        $query = $this->where('live_room_id', $liveRoomId)
            ->where('delete_time', 0);
        
        if (!empty($where)) {
            $query->where($where);
        }
        
        $total = $query->count();
        $list = $query->with(['itinerary'])
            ->page($page, $pageSize)
            ->order('is_featured desc, sort_order desc, id desc')
            ->select()
            ->toArray();
        
        return [
            'list' => $list,
            'total' => $total,
            'page' => $page,
            'page_size' => $pageSize
        ];
    }

    /**
     * 获取直播间推荐商品
     * @param int $liveRoomId 直播间ID
     * @param int $limit 数量限制
     * @return array
     */
    public function getFeaturedProducts(int $liveRoomId, int $limit = 5): array
    {
        return $this->where('live_room_id', $liveRoomId)
            ->where('is_featured', 1)
            ->where('status', self::STATUS_ACTIVE)
            ->where('delete_time', 0)
            ->with(['itinerary'])
            ->order('sort_order desc, id desc')
            ->limit($limit)
            ->select()
            ->toArray();
    }

    /**
     * 检查商品是否已添加到直播间
     * @param int $liveRoomId 直播间ID
     * @param int $productId 商品ID
     * @return bool
     */
    public function isProductExists(int $liveRoomId, int $productId): bool
    {
        return $this->where('live_room_id', $liveRoomId)
            ->where('product_id', $productId)
            ->where('delete_time', 0)
            ->count() > 0;
    }

    /**
     * 增加销售数量
     * @param int $id 记录ID
     * @param int $count 增加数量
     * @return bool
     */
    public function addSoldCount(int $id, int $count = 1): bool
    {
        return $this->where('id', $id)->inc('sold_count', $count)->update() !== false;
    }

    /**
     * 检查库存是否充足
     * @param int $id 记录ID
     * @param int $quantity 需要数量
     * @return bool
     */
    public function checkStock(int $id, int $quantity): bool
    {
        $product = $this->find($id);
        if (!$product) {
            return false;
        }

        // 如果没有设置库存限制，则认为库存充足
        if ($product->stock_limit === null) {
            return true;
        }

        return ($product->stock_limit - $product->sold_count) >= $quantity;
    }

    /**
     * 获取商品的实际价格
     * @param int $id 记录ID
     * @return float|null
     */
    public function getActualPrice(int $id): ?float
    {
        $product = $this->with(['itinerary'])->find($id);
        if (!$product || !$product->itinerary) {
            return null;
        }

        // 如果设置了直播间特价，使用特价
        if ($product->special_price !== null && $product->special_price > 0) {
            return $product->special_price;
        }

        // 如果设置了折扣率，计算折扣价
        if ($product->discount_rate !== null && $product->discount_rate > 0) {
            return $product->itinerary->price * ($product->discount_rate / 100);
        }

        // 否则使用原价
        return $product->itinerary->price;
    }

    /**
     * 批量添加商品到直播间
     * @param int $liveRoomId 直播间ID
     * @param array $products 商品数据
     * @return bool
     */
    public function batchAddProducts(int $liveRoomId, array $products): bool
    {
        $data = [];
        $time = time();
        
        foreach ($products as $product) {
            $data[] = [
                'live_room_id' => $liveRoomId,
                'product_id' => $product['product_id'],
                'product_type' => $product['product_type'] ?? 'itinerary',
                'special_price' => $product['special_price'] ?? null,
                'discount_rate' => $product['discount_rate'] ?? null,
                'stock_limit' => $product['stock_limit'] ?? null,
                'is_featured' => $product['is_featured'] ?? 0,
                'sort_order' => $product['sort_order'] ?? 0,
                'status' => $product['status'] ?? self::STATUS_ACTIVE,
                'create_time' => $time,
                'update_time' => $time
            ];
        }

        return $this->insertAll($data) !== false;
    }
}
