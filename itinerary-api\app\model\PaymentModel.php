<?php

declare(strict_types=1);

namespace app\model;

use base\base\BaseModel;

class PaymentModel extends BaseModel
{
    /**
     * 设置表名
     * @var string
     */
    protected $name = 'payments';

    /**
     * 设置主键
     * @var string
     */
    protected $pk = 'id';

    /**
     * 支付类型：订单支付
     */
    const TYPE_ORDER = 'order';

    /**
     * 支付类型：加价支付
     */
    const TYPE_SURCHARGE = 'surcharge';

    /**
     * 支付状态：待支付
     */
    const STATUS_PENDING = 'pending';

    /**
     * 支付状态：已支付
     */
    const STATUS_PAID = 'paid';

    /**
     * 支付状态：已取消
     */
    const STATUS_CANCELLED = 'cancelled';

    /**
     * 支付状态：已退款
     */
    const STATUS_REFUNDED = 'refunded';

    /**
     * 支付方式：支付宝
     */
    const METHOD_ALIPAY = 'alipay';

    /**
     * 允许写入的字段
     * @var array
     */
    protected $field = [
        'id',
        'payment_no',
        'order_id',
        'user_id',
        'amount',
        'payment_type', // order 或 surcharge
        'payment_method', // alipay
        'status',
        'payment_time',
        'third_party_no',
        'refund_no',
        'refund_time',
        'create_time',
        'update_time',
        'delete_time'
    ];

    /**
     * 类型转换
     * @var array
     */
    protected $type = [
        'id' => 'integer',
        'amount' => 'float',
        'payment_time' => 'integer',
        'refund_time' => 'integer',
        'create_time' => 'integer',
        'update_time' => 'integer',
        'delete_time' => 'integer'
    ];

    /**
     * 生成支付编号
     * @param string $type 支付类型 (order 或 surcharge)
     * @return string
     */
    public static function generatePaymentNo(string $type): string
    {
        $prefix = $type === self::TYPE_ORDER ? 'PAY' : 'SUR';
        return $prefix . date('YmdHis') . mt_rand(1000, 9999);
    }

    /**
     * 创建支付记录
     * @param array $data 支付数据
     * @return string 支付ID
     */
    public function createPayment(array $data): string
    {
        if (!isset($data['payment_type'])) {
            $data['payment_type'] = self::TYPE_ORDER;
        }

        $data['payment_no'] = self::generatePaymentNo($data['payment_type']);
        $data['status'] = self::STATUS_PENDING;
        $data['create_time'] = $data['update_time'] = time();
        
        $this->save($data);
        return (string)$this->id;
    }

    /**
     * 更新支付状态
     * @param string $id 支付ID
     * @param string $status 状态
     * @param array $additionalData 额外数据
     * @return bool
     */
    public function updateStatus(string $id, string $status, array $additionalData = []): bool
    {
        $updateData = [
            'status' => $status,
            'update_time' => time()
        ];

        if ($status === self::STATUS_PAID) {
            $updateData['payment_time'] = time();
        } elseif ($status === self::STATUS_REFUNDED) {
            $updateData['refund_time'] = time();
        }

        // 合并额外数据
        if (!empty($additionalData)) {
            $updateData = array_merge($updateData, $additionalData);
        }

        return $this->where('id', $id)->where('delete_time', 0)->update($updateData) > 0;
    }

    /**
     * 获取支付详情
     * @param string $id 支付ID
     * @return array|null
     */
    public function getDetail(string $id): ?array
    {
        $payment = $this->where('id', $id)->where('delete_time', 0)->find();
        
        if (!$payment) {
            return null;
        }
        
        return $payment->toArray();
    }

    /**
     * 通过订单ID获取支付记录
     * @param string $orderId 订单ID
     * @param string $type 支付类型
     * @return array|null
     */
    public function getByOrderId(string $orderId, string $type = self::TYPE_ORDER): ?array
    {
        $payment = $this->where('order_id', $orderId)
            ->where('payment_type', $type)
            ->where('delete_time', 0)
            ->order('create_time', 'desc')
            ->find();
            
        if (!$payment) {
            return null;
        }
        
        return $payment->toArray();
    }
} 